import { forwardRef } from 'react';
import { But<PERSON> } from '@cosmos/components/button';
import type { FormFieldProps } from '@cosmos/components/form-field';
import { FormWrapper, type FormWrapperProps } from './form-wrapper';
import { UniversalRenderFields } from './universal-render-fields';

/**
 * The Form component is an easy to way to quickly render a Form using Cosmos components.  The structure of the form is defined by `schema`, a JSON-like representation of the form.
 *
 * 🚧 Needs Figma Link.
 */
export interface FormProps extends Omit<FormWrapperProps, 'children'> {
    /** If `true`, then the form will not render a submit button. */
    hasExternalSubmitButton?: boolean;

    /**
     * Optional style overrides for field labels throughout the form.
     * Allows customizing label appearance consistently across all form fields.
     */
    labelStyleOverrides?: FormFieldProps['labelStyleOverrides'];

    /**
     * Optional label for the form submission button.
     */
    submitButtonLabel?: string;
}

export const Form = forwardRef(
    (
        {
            'data-id': dataId,
            formId,
            hasExternalSubmitButton = false,
            labelStyleOverrides,
            onSubmit,
            schema,
            isReadOnly = false,
            submitButtonLabel = 'Submit',
        }: FormProps,
        ref: React.ForwardedRef<HTMLFormElement>,
    ): React.JSX.Element => {
        return (
            <FormWrapper
                data-id={dataId}
                formId={formId}
                schema={schema}
                ref={ref}
                isReadOnly={isReadOnly}
                onSubmit={onSubmit}
            >
                <UniversalRenderFields
                    fields={schema}
                    formId={formId}
                    data-id={`${dataId}-fields`}
                    labelStyleOverrides={labelStyleOverrides}
                />

                {hasExternalSubmitButton ? (
                    <button
                        hidden
                        type="submit"
                        aria-hidden="true"
                        tabIndex={-1}
                    />
                ) : (
                    <Button
                        type="submit"
                        label={submitButtonLabel}
                        width="auto"
                        data-id={`${dataId}-submit`}
                    />
                )}
            </FormWrapper>
        );
    },
);

Form.displayName = 'Form';
