import { isBoolean, isEmpty, isObject, isString } from 'lodash-es';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import type { FilterState } from '@cosmos/components/filter-field';
import { accessReviewUserControllerGetReviewApplicationUsersOptions } from '@globals/api-sdk/queries';
import type { ClientTypeEnum } from '@globals/api-sdk/types';
import {
    makeAutoObservable,
    observable,
    ObservedQuery,
    runInAction,
} from '@globals/mobx';

// Define constants for default filter values
const DEFAULT_FILTER_VALUES = {
    status: 'ALL_REVIEW_STATUS',
    permission: 'ALL_PERMISSIONS',
    warning: 'ALL_WARNINGS',
    employmentStatus: 'ALL_CURRENT_PERSONNEL',
    connections: '',
    groupIds: '',
};

// Define a type for the filter keys
type FilterKey =
    | 'status'
    | 'employmentStatus'
    | 'permission'
    | 'connections'
    | 'groupIds'
    | 'warning';

interface FilterValues {
    status?: string;
    employmentStatus?: string[] | string;
    permission?: string;
    connections?: string[] | string;
    groupIds?: string[] | string;
    warning?: string;
}

class AccessReviewPeriodApplicationUsersController {
    /**
     * Observable properties.
     */
    reviewStatusFilter = '';
    isLoading = false;

    /**
     * Private fields.
     */
    internalPeriodId: number | null = null;
    internalApplicationId: number | null = null;
    #clientType: ClientTypeEnum | null = null;
    #filterValues: FilterValues = {};
    #lastQueryParams: FetchDataResponseParams | null = null;
    #lastQueryString: string | null = null;

    accessReviewPeriodApplicationUsers = new ObservedQuery(
        accessReviewUserControllerGetReviewApplicationUsersOptions,
    );

    constructor() {
        makeAutoObservable(this, {
            reviewStatusFilter: observable,
            isLoading: observable,
        });
    }

    /**
     * Getters for controller state.
     */
    get periodId(): number | null {
        return this.internalPeriodId;
    }

    get applicationId(): number | null {
        return this.internalApplicationId;
    }

    /**
     * Computed getters for data from the query.
     */
    get accessReviewPeriodApplicationUsersList() {
        return this.accessReviewPeriodApplicationUsers.data?.data ?? [];
    }

    get totalReviewApplicationUsers() {
        return this.accessReviewPeriodApplicationUsers.data?.total ?? 0;
    }

    /**
     * Sets for the controller.
     */
    setPeriodId = (periodId: number): void => {
        if (this.internalPeriodId === periodId) {
            return;
        }

        this.internalPeriodId = periodId;
        this.#resetStateOnContextChange();
    };

    setApplicationId = (applicationId: number): void => {
        if (this.internalApplicationId === applicationId) {
            return;
        }

        this.internalApplicationId = applicationId;
        this.#resetStateOnContextChange();
    };

    setClientType = (clientType: ClientTypeEnum | null): void => {
        if (this.#clientType === clientType) {
            return;
        }

        this.#clientType = clientType;
        this.#resetStateOnContextChange();
    };

    /**
     * Centralized method to reset state when context changes.
     * This prevents stale filter state from causing UI errors.
     */
    #resetStateOnContextChange = (): void => {
        this.#filterValues = { ...DEFAULT_FILTER_VALUES };

        // Clear query cache to force fresh data load
        this.#lastQueryString = '';
        this.#lastQueryParams = null;

        // Invalidate cached data to ensure fresh load
        this.invalidateAccessReviewApplicationUsers();
    };

    /**
     * Resets filter values and clears cached state.
     * This is the primary method for resetting filter state externally.
     */
    resetFilterValues = (): void => {
        this.#filterValues = { ...DEFAULT_FILTER_VALUES };

        // Clear query cache to force fresh data load
        this.#lastQueryString = '';
        this.#lastQueryParams = null;
    };

    /**
     * Processes status filter values (review-status and status).
     */
    #processStatusFilter = (filterRecord: Record<string, unknown>): boolean => {
        if (filterRecord['review-status'] || filterRecord.status) {
            this.#filterValues.status =
                (filterRecord['review-status'] as string) ||
                (filterRecord.status as string);

            return true;
        }

        return false;
    };

    /**
     * Processes array-type filter values.
     */
    #processArrayFilters = (filterRecord: Record<string, unknown>): boolean => {
        const arrayFilters = [
            'employmentStatus',
            'connections',
            'groupIds',
        ] as const;

        let updated = false;

        arrayFilters.forEach((key) => {
            if (filterRecord[key] !== undefined) {
                if (Array.isArray(filterRecord[key])) {
                    // Extract values from array of objects
                    const values = (filterRecord[key] as { value: string }[])
                        .map((item) => item.value)
                        .filter(Boolean);

                    // Handle empty arrays as valid filter values (clears the filter)
                    this.#filterValues[key] = values;
                    updated = true;
                } else {
                    // Handle single value case
                    this.#filterValues[key] = filterRecord[key] as
                        | string
                        | string[];
                    updated = true;
                }
            }
        });

        return updated;
    };

    /**
     * Processes string-type filter values.
     */
    #processStringFilters = (
        filterRecord: Record<string, unknown>,
    ): boolean => {
        const stringFilters = ['permission', 'warning'] as const;
        let updated = false;

        stringFilters.forEach((key) => {
            if (filterRecord[key] === undefined) {
                return;
            }

            // Only process permission filter if it's actually provided in the filter record
            // This prevents setting default permission values for providers that don't support permissions
            if (key === 'permission' && !filterRecord[key]) {
                return;
            }

            this.#filterValues[key] = filterRecord[key] as string;
            updated = true;
        });

        return updated;
    };

    /**
     * Triggers data reload if filters were updated and required IDs are available.
     */
    #triggerDataReloadIfNeeded = (filtersUpdated: boolean): void => {
        if (
            filtersUpdated &&
            this.internalApplicationId &&
            this.internalPeriodId &&
            this.#lastQueryParams
        ) {
            this.loadAccessReviewPeriodApplicationUsers(this.#lastQueryParams);
        }
    };

    /**
     * Updates filter values directly.
     */
    updateFilterValues = (
        filters: Record<string, FilterState['value']>,
    ): void => {
        const filterRecord = Array.isArray(filters)
            ? Object.fromEntries([...filters].entries())
            : filters;

        // Process different types of filters
        const statusUpdated = this.#processStatusFilter(filterRecord);
        const arrayFiltersUpdated = this.#processArrayFilters(filterRecord);
        const stringFiltersUpdated = this.#processStringFilters(filterRecord);

        const filtersUpdated =
            statusUpdated || arrayFiltersUpdated || stringFiltersUpdated;

        // Trigger data reload if any filters were updated
        this.#triggerDataReloadIfNeeded(filtersUpdated);
    };

    /**
     * Loads access review period application users.
     */
    loadAccessReviewPeriodApplicationUsers = (
        params: FetchDataResponseParams,
    ): void => {
        // Store the latest params for comparison
        this.#lastQueryParams = params;

        try {
            this.#processFilters(params);

            // Skip if query hasn't changed
            const query = this.buildQueryFromParams(params);
            const queryString = JSON.stringify(query);

            if (queryString === this.#lastQueryString) {
                return;
            }

            this.#lastQueryString = queryString;

            // Early return if required IDs are missing
            if (!this.internalApplicationId || !this.internalPeriodId) {
                runInAction(() => {
                    this.isLoading = false;
                });

                return;
            }

            this.#executeQuery(params);
        } catch (error) {
            console.error(
                'Error loading access review period application users:',
                error,
            );
            runInAction(() => {
                this.isLoading = false;
            });
        }
    };

    #processFilters = (params: FetchDataResponseParams): void => {
        this.#handleFilter(params, 'review-status', 'status', false);
        this.#handleFilter(
            params,
            'employmentStatus',
            'employmentStatus',
            true,
        );

        this.#handleFilter(params, 'permission', 'permission', false);
        this.#handleFilter(params, 'connections', 'connections', true);
        this.#handleFilter(params, 'groupIds', 'groupIds', true);
        this.#handleFilter(params, 'warning', 'warning', false);
    };

    /**
     * Execute the query with current filters.
     */
    #executeQuery = (params: FetchDataResponseParams): void => {
        runInAction(() => {
            this.isLoading = true;
        });

        const apiQuery = this.#buildApiQuery(params);

        this.accessReviewPeriodApplicationUsers.load({
            path: {
                periodId: Number(this.internalPeriodId),
                reviewAppId: Number(this.internalApplicationId),
            },
            query: apiQuery,
        });

        runInAction(() => {
            this.isLoading = false;
        });
    };

    /**
     * Build API query with all filter parameters.
     */
    #buildApiQuery = (
        params: FetchDataResponseParams,
    ): Record<string, unknown> => {
        const apiQuery: Record<string, unknown> = {
            page: params.pagination.page,
            limit: params.pagination.pageSize,
            clientType: this.#clientType || undefined,
            applicationId: this.internalApplicationId
                ? Number(this.internalApplicationId)
                : undefined,
            accessApplicationId: this.internalApplicationId
                ? Number(this.internalApplicationId)
                : undefined,
        };

        if (!isEmpty(params.sorting)) {
            apiQuery.sort = params.sorting[0].id;
            apiQuery.sortDir = params.sorting[0].desc ? 'DESC' : 'ASC';
        }

        this.#addFilterToQuery(apiQuery, 'status');
        this.#addArrayFilterToQuery(apiQuery, 'employmentStatus');
        if (this.#filterValues.permission) {
            this.#addFilterToQuery(apiQuery, 'permission');
        }
        this.#addArrayFilterToQuery(apiQuery, 'connections');
        this.#addArrayFilterToQuery(apiQuery, 'groupIds');
        this.#addFilterToQuery(apiQuery, 'warning');

        return apiQuery;
    };

    /**
     * Add a simple filter to the query if it exists and is not the default value.
     */
    #addFilterToQuery = (
        query: Record<string, unknown>,
        key: FilterKey,
    ): void => {
        if (
            this.#filterValues[key] &&
            this.#filterValues[key] !== DEFAULT_FILTER_VALUES[key]
        ) {
            query[key] = String(this.#filterValues[key]);
        }
    };

    /**
     * Add an array filter to the query if it exists and is not the default value.
     */
    #addArrayFilterToQuery = (
        query: Record<string, unknown>,
        key: FilterKey,
    ): void => {
        if (!this.#filterValues[key]) {
            return;
        }

        // Skip default values
        if (
            this.#filterValues[key] === DEFAULT_FILTER_VALUES[key] ||
            (Array.isArray(this.#filterValues[key]) &&
                this.#filterValues[key].length === 1 &&
                this.#filterValues[key][0] === DEFAULT_FILTER_VALUES[key])
        ) {
            return;
        }

        const arrayKey = `${key}[]`;

        if (Array.isArray(this.#filterValues[key])) {
            query[arrayKey] = this.#filterValues[key];
        } else {
            query[arrayKey] = [this.#filterValues[key]];
        }
    };

    /**
     * Extracts values from array filter parameters.
     */
    #extractArrayValues = (filterValueFromParams: unknown[]): string[] => {
        return filterValueFromParams
            .map((item) =>
                isObject(item) && 'value' in item ? item.value : item,
            )
            .filter(Boolean)
            .map(String);
    };

    /**
     * Handles array filter values for specific filter keys.
     */
    #handleArrayFilterValue = (stateKey: FilterKey, values: string[]): void => {
        // Only assign array values to filter keys that accept arrays
        if (
            stateKey === 'employmentStatus' ||
            stateKey === 'connections' ||
            stateKey === 'groupIds'
        ) {
            // Handle empty arrays as valid filter values (clears the filter)
            this.#filterValues[stateKey] = values;
        } else {
            // For non-array filter keys, use the first value if available
            if (!isEmpty(values)) {
                this.#handleSingleFilterValue(stateKey, values[0]);
            }
        }
    };

    /**
     * Handles single filter values with type conversion.
     */
    #handleSingleFilterValue = (
        stateKey: FilterKey,
        filterValueFromParams: unknown,
    ): void => {
        if (isBoolean(filterValueFromParams)) {
            this.#filterValues[stateKey] = String(filterValueFromParams);
        } else if (isString(filterValueFromParams)) {
            this.#filterValues[stateKey] = filterValueFromParams;
        } else if (isObject(filterValueFromParams)) {
            this.#handleObjectFilterValue(stateKey, filterValueFromParams);
        } else {
            this.#filterValues[stateKey] = String(filterValueFromParams);
        }
    };

    /**
     * Handles object filter values by extracting the appropriate property.
     */
    #handleObjectFilterValue = (
        stateKey: FilterKey,
        filterValueFromParams: object,
    ): void => {
        if ('value' in filterValueFromParams) {
            this.#filterValues[stateKey] = String(filterValueFromParams.value);
        } else if ('id' in filterValueFromParams) {
            this.#filterValues[stateKey] = String(filterValueFromParams.id);
        } else {
            this.#filterValues[stateKey] = JSON.stringify(
                filterValueFromParams,
            );
        }
    };

    /**
     * Generic filter handler that synchronizes filter values between params and controller state.
     */
    #handleFilter = (
        params: FetchDataResponseParams,
        filterKey: string,
        stateKey: FilterKey,
        isArray = false,
    ): void => {
        // Check if the filter key exists in the filters object
        if (!(filterKey in params.globalFilter.filters)) {
            return;
        }

        const filterObject = params.globalFilter.filters[filterKey];
        const filterValueFromParams = filterObject.value;

        // Check if we have a valid filter value
        // For arrays, empty arrays are valid (they clear the filter)
        // For non-arrays, empty strings are invalid
        const hasValidValue =
            filterValueFromParams !== undefined &&
            (isArray
                ? Array.isArray(filterValueFromParams)
                : filterValueFromParams !== '');

        if (hasValidValue) {
            if (isArray && Array.isArray(filterValueFromParams)) {
                const values = this.#extractArrayValues(filterValueFromParams);

                this.#handleArrayFilterValue(stateKey, values);
            } else {
                this.#handleSingleFilterValue(stateKey, filterValueFromParams);
            }
        }
        // If params has an empty value but we have a stored value, update the params
        else if (this.#filterValues[stateKey]) {
            filterObject.value = this.#filterValues[stateKey];
        }
    };

    /**
     * Builds a query object from the provided params.
     */
    buildQueryFromParams = (
        params: FetchDataResponseParams,
    ): Record<string, unknown> => {
        const query: Record<string, unknown> = {
            applicationId: this.internalApplicationId,
            accessApplicationId: this.internalApplicationId,
            clientType: this.#clientType || undefined,
            limit: params.pagination.pageSize,
            page: params.pagination.page,
            q: params.globalFilter.search || '',
            sort: params.sorting[0]?.id,
            sortDir: params.sorting[0]?.desc ? 'DESC' : 'ASC',
        };

        // Add all filters using the helper methods
        this.#addFilterToQuery(query, 'status');
        if (this.#filterValues.permission) {
            this.#addFilterToQuery(query, 'permission');
        }
        this.#addFilterToQuery(query, 'warning');
        this.#addArrayFilterToQuery(query, 'employmentStatus');
        this.#addArrayFilterToQuery(query, 'connections');
        this.#addArrayFilterToQuery(query, 'groupIds');

        return query;
    };

    /**
     * Invalidates the access review period application users query.
     */
    invalidateAccessReviewApplicationUsers = (): void => {
        this.accessReviewPeriodApplicationUsers.invalidate();
    };
}

export const sharedAccessReviewPeriodApplicationUsersController =
    new AccessReviewPeriodApplicationUsersController();
