import { grcControllerSearchControlsOptions } from '@globals/api-sdk/queries';
import type { ControlPaginatedResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

class GetControlsController {
    constructor() {
        makeAutoObservable(this);
    }

    getControlsQuery = new ObservedQuery(grcControllerSearchControlsOptions);

    get isLoading(): boolean {
        return this.getControlsQuery.isLoading;
    }

    get getControls(): ControlPaginatedResponseDto | null {
        return this.getControlsQuery.data ?? null;
    }

    load = (workspaceId?: number | null): void => {
        if (!workspaceId) {
            throw new Error('Workspace ID is required');
        }

        this.getControlsQuery.load({
            path: { xProductId: workspaceId },
            query: {
                q: 'DCF-11',
            },
        });
    };
}

export const sharedGetControlsController = new GetControlsController();
