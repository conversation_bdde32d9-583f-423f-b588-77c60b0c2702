import { snackbarController } from '@controllers/snackbar';
import { accessReviewPeriodControllerUpdateAccessReviewPeriodMutation } from '@globals/api-sdk/queries';
import type { UpdateReviewPeriodRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';

class CompleteReviewController {
    constructor() {
        makeAutoObservable(this);
    }

    completeReviewPeriodMutation = new ObservedMutation(
        accessReviewPeriodControllerUpdateAccessReviewPeriodMutation,
    );

    completeReviewPeriod = (
        periodId: number,
        reviewPeriod: UpdateReviewPeriodRequestDto,
    ): Promise<void> => {
        return this.completeReviewPeriodMutation
            .mutateAsync({
                path: {
                    periodId,
                },
                body: {
                    startingDate: reviewPeriod.startingDate,
                    endingDate: reviewPeriod.endingDate,
                    completed: true,
                },
            })
            .then(() => {
                snackbarController.addSnackbar({
                    id: 'complete-review-period-success',
                    props: {
                        title: t`Review Period completed!`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch((error: Error) => {
                snackbarController.addSnackbar({
                    id: 'complete-review-period-error',
                    props: {
                        title: t`Unable to complete review period.`,
                        description: error.message || t`Please try again later`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                throw error;
            });
    };
}

export const sharedCompleteReviewController = new CompleteReviewController();
