import { snackbarController } from '@controllers/snackbar';
import {
    assetsControllerDeleteAssetMutation,
    deviceControllerLinkDeviceMutation,
    deviceControllerUnlinkDeviceMutation,
} from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { activeAssetController } from './asset-controller';

class AssetMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    deleteAssetMutation = new ObservedMutation(
        assetsControllerDeleteAssetMutation,
    );

    unlinkDeviceMutation = new ObservedMutation(
        deviceControllerUnlinkDeviceMutation,
    );

    get isDeleting(): boolean {
        return this.deleteAssetMutation.isPending;
    }

    get isUnlinkingDevice(): boolean {
        return this.unlinkDeviceMutation.isPending;
    }

    deleteAsset = (
        assetId: number,
        onSuccess?: () => void,
        onClose?: () => void,
    ) => {
        if (!assetId || this.deleteAssetMutation.isPending) {
            return;
        }

        this.deleteAssetMutation.mutate({
            path: { id: assetId },
        });

        when(
            () => !this.deleteAssetMutation.isPending,
            () => {
                if (this.deleteAssetMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'asset-delete-error',
                        props: {
                            title: t`Asset could not be deleted`,
                            description: t`An error occurred while deleting the asset. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: 'asset-delete-success',
                        props: {
                            title: t`Asset deleted successfully`,
                            description: t`The asset was deleted successfully.`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    onSuccess?.();
                }

                onClose?.();
            },
        );
    };
    linkDeviceMutation = new ObservedMutation(
        deviceControllerLinkDeviceMutation,
    );

    linkDevice = (deviceId: number) => {
        if (!deviceId || this.linkDeviceMutation.isPending) {
            return;
        }
        this.linkDeviceMutation.mutate({
            path: { deviceId },
        });

        when(
            () => !this.linkDeviceMutation.isPending,
            () => {
                if (this.linkDeviceMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'device-link-error',
                        props: {
                            title: t`The device could not be linked.`,
                            description: t`An error occurred while linking the device. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: 'device-link-success',
                        props: {
                            title: t`Device linked successfully`,
                            description: t`The device was linked successfully.`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    const assetId = activeAssetController.assetDetails?.id;

                    if (assetId) {
                        activeAssetController.loadAsset(assetId.toString());
                    }
                }
            },
        );
    };

    unlinkDevice = (deviceId: number) => {
        if (!deviceId || this.unlinkDeviceMutation.isPending) {
            return;
        }

        this.unlinkDeviceMutation.mutate({
            path: { deviceId },
        });

        when(
            () => !this.unlinkDeviceMutation.isPending,
            () => {
                if (this.unlinkDeviceMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'device-unlink-error',
                        props: {
                            title: t`Device could not be unlinked`,
                            description: t`An error occurred while unlinking the device. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: 'device-unlink-success',
                        props: {
                            title: t`Device unlinked successfully`,
                            description: t`The device was unlinked successfully.`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    const assetId = activeAssetController.assetDetails?.id;

                    if (assetId) {
                        activeAssetController.loadAsset(assetId.toString());
                    }
                }
            },
        );
    };
}

export const sharedAssetMutationController = new AssetMutationController();
