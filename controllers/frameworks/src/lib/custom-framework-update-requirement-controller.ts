import { sharedRequirementDetailsController } from '@controllers/requirements';
import { snackbarController } from '@controllers/snackbar';
import { customFrameworksControllerUpdateCustomRequirementMutation } from '@globals/api-sdk/queries';
import type { UpdateCustomRequirementRequestDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';

class CustomFrameworkUpdateRequirementController {
    constructor() {
        makeAutoObservable(this);
    }

    #updateCustomRequirementMutation = new ObservedMutation(
        customFrameworksControllerUpdateCustomRequirementMutation,
    );

    get isPending(): boolean {
        return this.#updateCustomRequirementMutation.isPending;
    }
    get hasError(): boolean {
        return this.#updateCustomRequirementMutation.hasError;
    }

    updateCustomRequirement(
        requirement: UpdateCustomRequirementRequestDto,
    ): void {
        this.#updateCustomRequirementMutation.mutate({
            body: {
                ...requirement,
            },
        });

        when(
            () => !this.isPending,
            () => {
                if (this.hasError) {
                    snackbarController.addSnackbar({
                        id: 'custom-framework-update-requirement-error',
                        props: {
                            title: t`Failed to update custom framework requirement`,
                            description: t`An error occurred while updating the custom framework requirement. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: 'custom-framework-update-requirement-success',
                        props: {
                            title: t`Custom framework requirement updated successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    sharedRequirementDetailsController.requirementDetailsQuery.invalidate();
                }
            },
        );
    }
}

export const sharedCustomFrameworkUpdateRequirementController =
    new CustomFrameworkUpdateRequirementController();
