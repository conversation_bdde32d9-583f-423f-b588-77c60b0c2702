import { isNil } from 'lodash-es';
import {
    grcControllerGetFrameworkOptions,
    grcControllerGetLevelImpactOptions,
    grcControllerGetLevelOptions,
    grcControllerGetProfileOptions,
} from '@globals/api-sdk/queries';
import type {
    FrameworkLevelResponseDto,
    FrameworkProfileSelectionResponseDto,
    FrameworkResponseDto,
    LevelImpactResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

export class FrameworkDetailsController {
    constructor() {
        makeAutoObservable(this);
    }

    frameworkDetailsQuery = new ObservedQuery(grcControllerGetFrameworkOptions);

    get frameworkDetails(): FrameworkResponseDto | null {
        return this.frameworkDetailsQuery.data;
    }

    get isLoading(): boolean {
        return this.frameworkDetailsQuery.isLoading;
    }

    get isFedramp(): boolean {
        return this.frameworkDetails?.tag === 'FEDRAMP';
    }

    get isFrameworkV2(): boolean {
        return !isNil(this.frameworkDetails?.externalId);
    }

    get selectedLevel(): string | null | undefined {
        return this.frameworkDetails?.selectedLevel;
    }

    get hasSelectedLevel(): boolean {
        return !isNil(this.frameworkDetails?.selectedLevel);
    }

    /**
     * Whether the framework has a level.
     * This means that it either has a level or has a profile.
     */
    get hasLevel(): boolean {
        return this.frameworkDetails?.hasLevel ?? false;
    }

    getProfileQuery = new ObservedQuery(grcControllerGetProfileOptions);

    get isLoadingProfile(): boolean {
        return this.getProfileQuery.isLoading;
    }

    get profiles(): FrameworkProfileSelectionResponseDto[] {
        return this.getProfileQuery.data?.profiles ?? [];
    }

    get selectedProfile(): string | undefined {
        return this.frameworkDetails?.profileOscalId;
    }

    get usesProfile(): boolean {
        return this.isFrameworkV2 && this.hasLevel;
    }

    get needsProfile(): boolean {
        return this.usesProfile && isNil(this.frameworkDetails?.profileOscalId);
    }

    getLevelQuery = new ObservedQuery(grcControllerGetLevelOptions);

    get isLoadingLevel(): boolean {
        return this.getLevelQuery.isLoading;
    }

    get levels(): FrameworkLevelResponseDto | null {
        return this.getLevelQuery.data;
    }

    get usesLevel(): boolean {
        return !this.isFrameworkV2 && this.hasLevel;
    }

    get needsLevel(): boolean {
        return this.usesLevel && !this.hasSelectedLevel;
    }

    getLevelImpactQuery = new ObservedQuery(grcControllerGetLevelImpactOptions);

    get isLoadingLevelImpact(): boolean {
        return this.getLevelImpactQuery.isLoading;
    }

    get levelImpact(): LevelImpactResponseDto | null {
        return this.getLevelImpactQuery.data;
    }
}

export const sharedFrameworkDetailsController =
    new FrameworkDetailsController();
