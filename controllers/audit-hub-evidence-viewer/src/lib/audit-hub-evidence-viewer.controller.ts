import { isEmpty } from 'lodash-es';
import { sharedAuditHubEvidenceController } from '@controllers/audit-hub';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import type { AuditHubEvidenceResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { downloadFileFromSignedUrl } from '@helpers/download-file';

class AuditHubEvidenceViewerController {
    selectedEvidence: {
        evidence: AuditHubEvidenceResponseDto;
        rowIndex: number;
    } | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    get evidenceDocument(): AuditHubEvidenceResponseDto | null {
        return this.selectedEvidence?.evidence ?? null;
    }

    get keyValuePairs(): KeyValuePairProps[] {
        if (!this.selectedEvidence) {
            return [];
        }

        const { artifact, type, date, controlCodes } =
            this.selectedEvidence.evidence;

        const artifactKVP: KeyValuePairProps = {
            id: 'evidence-artifact',
            label: t`Artifact`,
            value: artifact || '',
            showEmptyValue: !artifact,
        };

        const typeKVP: KeyValuePairProps = {
            id: 'evidence-type',
            label: t`Type`,
            value: type,
        };

        const controlCodesKVP: KeyValuePairProps = {
            id: 'evidence-control-codes',
            label: t`Linked Controls`,
            type: 'TAG',
            visibleItemsLimit: 2,
            value: isEmpty(controlCodes)
                ? []
                : controlCodes.map((code: string) => ({
                      label: code,
                      colorScheme: 'neutral',
                      type: 'tag',
                  })),
            showEmptyValue: isEmpty(controlCodes),
        };

        const dateKVP: KeyValuePairProps = {
            id: 'evidence-date',
            label: t`Creation Date`,
            value: date ? formatDate('table', date) : '',
            showEmptyValue: !date,
        };

        return [artifactKVP, typeKVP, controlCodesKVP, dateKVP];
    }

    setSelectedEvidence = (
        evidence: AuditHubEvidenceResponseDto,
        rowIndex: number,
    ): void => {
        this.selectedEvidence = { evidence, rowIndex };
    };

    get currentEvidenceIndex(): number {
        return this.selectedEvidence?.rowIndex ?? -1;
    }

    get canNavigatePrevious(): boolean {
        return this.currentEvidenceIndex > 0;
    }

    get canNavigateNext(): boolean {
        const evidenceList =
            sharedAuditHubEvidenceController.auditCustomerRequestEvidences;

        return (
            this.currentEvidenceIndex >= 0 &&
            this.currentEvidenceIndex < evidenceList.length - 1
        );
    }

    navigateToPrevious = (): void => {
        if (!this.canNavigatePrevious) {
            return;
        }

        const evidenceList =
            sharedAuditHubEvidenceController.auditCustomerRequestEvidences;
        const previousIndex = this.currentEvidenceIndex - 1;

        if (evidenceList[previousIndex]) {
            this.setSelectedEvidence(
                evidenceList[previousIndex],
                previousIndex,
            );
        }
    };

    navigateToNext = (): void => {
        if (!this.canNavigateNext) {
            return;
        }

        const evidenceList =
            sharedAuditHubEvidenceController.auditCustomerRequestEvidences;
        const nextIndex = this.currentEvidenceIndex + 1;

        if (evidenceList[nextIndex]) {
            this.setSelectedEvidence(evidenceList[nextIndex], nextIndex);
        }
    };

    downloadSelectedEvidence = (): void => {
        if (!this.selectedEvidence?.evidence.signedUrl) {
            return;
        }

        downloadFileFromSignedUrl(this.selectedEvidence.evidence.signedUrl);
    };
}

export const sharedAuditHubEvidenceViewerController =
    new AuditHubEvidenceViewerController();
