import { isEmpty, isNil, map, uniqueId } from 'lodash-es';
import {
    closeRenewalDateModal,
    type ExternalFile,
} from '@components/evidence-library';
import { modalController } from '@controllers/modal';
import { panelController } from '@controllers/panel';
import { snackbarController } from '@controllers/snackbar';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import {
    evidenceLibraryControllerBulkOwnerAssignMutation,
    evidenceLibraryControllerCreateEvidenceMutation,
    evidenceLibraryControllerDeleteBulkDocumentMutation,
    evidenceLibraryControllerUpdateEvidenceMutation,
} from '@globals/api-sdk/queries';
import type {
    EvidenceLibraryControllerBulkOwnerAssignData,
    EvidenceLibraryControllerDeleteBulkDocumentData,
    EvidenceRequestDto,
    EvidenceResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { sharedEvidenceLibraryUpdateDatesModel } from '@models/evidence';
import { AppLink } from '@ui/app-link';
import type { FormValues } from '@ui/forms';
import { LINK_CONTROLS_MODAL_ID } from '../../../../components/evidence-library/src/lib/modals/link-controls/constants/link-controls-modal.constant';
import { buildCreateEvidenceRequestDto } from '../helpers/build-create-evidence-request-dto.helper';
import { buildEvidenceRequestDto } from '../helpers/build-evidence-request-dto.helper';
import { buildFileBodyContent } from '../helpers/build-file-body-content.helper';
import { getAddEvidenceErrorMessages } from '../helpers/get-add-evidence-error-messages.helper';
import type { ErrorWithCode } from '../types/error-with-code.type';
import type { EvidenceControl } from '../types/evidence-control.type';
import { sharedEvidenceDetailsController } from './evidence-details.controller';
import { sharedEvidenceLibraryListController } from './evidence-library-list.controller';

export interface EvidenceBulkProps {
    isAllRowsSelected?: boolean;
}

class EvidenceMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    createEvidenceMutation = new ObservedMutation(
        evidenceLibraryControllerCreateEvidenceMutation,
    );

    updateEvidenceMutation = new ObservedMutation(
        evidenceLibraryControllerUpdateEvidenceMutation,
        {
            onSuccess: (data) => {
                sharedEvidenceLibraryListController.setEvidenceListQueryData(
                    data,
                );

                sharedEvidenceDetailsController.setEvidenceDetailsQueryData(
                    data,
                );
            },
        },
    );

    evidenceOwnerBulkMutation = new ObservedMutation(
        evidenceLibraryControllerBulkOwnerAssignMutation,
        {
            onSuccess: (data, variables) => {
                const [currentVariable] = variables as unknown as [
                    EvidenceLibraryControllerBulkOwnerAssignData,
                ];

                sharedEvidenceLibraryListController.setBulkOwnerEvidenceListQueryData(
                    data,
                    currentVariable,
                );
            },
        },
    );

    deleteEvidenceMutation = new ObservedMutation(
        evidenceLibraryControllerDeleteBulkDocumentMutation,
        {
            onSuccess: (_data, variables) => {
                const [currentVariable] = variables as unknown as [
                    EvidenceLibraryControllerDeleteBulkDocumentData,
                ];

                sharedEvidenceLibraryListController.setDeleteEvidenceListQueryData(
                    currentVariable,
                );
            },
        },
    );

    get isCreating(): boolean {
        return this.createEvidenceMutation.isPending;
    }

    get hasCreateError(): boolean {
        return this.createEvidenceMutation.hasError;
    }

    get createEvidenceData(): EvidenceResponseDto | null {
        return this.createEvidenceMutation.response;
    }

    get createEvidenceError(): ErrorWithCode | null {
        return this.createEvidenceMutation.error as ErrorWithCode | null;
    }

    get isBulkOwnerAssigning(): boolean {
        return this.evidenceOwnerBulkMutation.isPending;
    }

    get hasBulkOwnerAssignError(): boolean {
        return this.evidenceOwnerBulkMutation.hasError;
    }

    get isUpdating(): boolean {
        return this.updateEvidenceMutation.isPending;
    }

    get hasUpdateError(): boolean {
        return this.updateEvidenceMutation.hasError;
    }

    get isDeleting(): boolean {
        return this.deleteEvidenceMutation.isPending;
    }

    get hasDeleteError(): boolean {
        return this.deleteEvidenceMutation.hasError;
    }

    createEvidence = (values: FormValues, onSuccess?: () => void): void => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        const requestBody = buildCreateEvidenceRequestDto(values);

        if (!requestBody) {
            return;
        }

        this.createEvidenceMutation.mutate({
            body: requestBody,
            path: {
                xProductId: currentWorkspace.id,
            },
        });

        when(
            () => !this.isCreating,
            () => {
                if (this.hasCreateError) {
                    const errorCode = this.createEvidenceError?.code ?? 0;

                    const { title, description } = getAddEvidenceErrorMessages(
                        errorCode,
                        this.createEvidenceError?.message,
                        requestBody,
                    );

                    snackbarController.addSnackbar({
                        id: `create-evidence-error-${uniqueId()}`,
                        props: {
                            title,
                            description,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: `create-evidence-success-${this.createEvidenceData?.id}`,
                    props: {
                        title: t`Evidence created`,
                        description: t`Find it in the evidence list to edit or update later.`,
                        link: (
                            <AppLink
                                href={`/workspaces/${currentWorkspace.id}/compliance/evidence/${this.createEvidenceData?.id}/overview`}
                                label="View"
                            />
                        ),
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                onSuccess?.();
            },
        );
    };

    updateEvidence = (evidenceId: number, values: FormValues): void => {
        const { currentWorkspace } = sharedWorkspacesController;
        const { evidenceDetailsData, currentLibraryVersion } =
            sharedEvidenceDetailsController;

        if (!currentWorkspace) {
            return;
        }

        const requestBody = buildEvidenceRequestDto({
            evidenceDetails: evidenceDetailsData,
            currentLibraryVersion,
            requestBody: values,
        });

        if (!requestBody) {
            return;
        }

        this.updateEvidenceMutation.mutate({
            body: requestBody,
            path: {
                id: evidenceId,
                xProductId: currentWorkspace.id,
            },
        });

        when(
            () => !this.isUpdating,
            () => {
                if (this.hasUpdateError) {
                    snackbarController.addSnackbar({
                        id: `updated-evidence-error-${evidenceId}`,
                        props: {
                            title: "Couldn't update evidence",
                            description:
                                'An error occurred while updating the evidence. Try again later.',
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: `updated-evidence-success-${evidenceId}`,
                    props: {
                        title: 'Evidence successfully updated',
                        description:
                            'The evidence has been updated successfully.',
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            },
        );
    };

    updateEvidenceControls = (controlIds: number[]): void => {
        const { currentWorkspace } = sharedWorkspacesController;
        const { evidenceDetailsData, currentLibraryVersion } =
            sharedEvidenceDetailsController;

        if (!currentWorkspace || !evidenceDetailsData) {
            return;
        }

        const requestBody = buildEvidenceRequestDto({
            evidenceDetails: evidenceDetailsData,
            currentLibraryVersion,
            requestBody: {
                controlIds,
                owner: evidenceDetailsData.user
                    ? {
                          id: evidenceDetailsData.user.id.toString(),
                          label: evidenceDetailsData.user.firstName,
                          value: evidenceDetailsData.user.id.toString(),
                      }
                    : undefined,
                name: evidenceDetailsData.name,
            },
        });

        if (!requestBody) {
            return;
        }

        this.updateEvidenceMutation.mutate({
            body: requestBody,
            path: {
                id: evidenceDetailsData.id,
                xProductId: currentWorkspace.id,
            },
        });

        when(
            () => !this.isUpdating,
            () => {
                if (!this.hasUpdateError) {
                    return;
                }

                snackbarController.addSnackbar({
                    id: `updated-evidence-error-${evidenceDetailsData.id}`,
                    props: {
                        title: "Couldn't link controls to evidence",
                        description: 'Unable to update evidence',
                        severity: 'critical',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            },
        );
    };

    updateEvidenceDates = (values: FormValues): void => {
        const { currentWorkspace } = sharedWorkspacesController;
        const {
            fromEvidenceLibraryView,
            evidenceDetails,
            currentLibraryVersion,
        } = sharedEvidenceLibraryUpdateDatesModel;

        if (!currentWorkspace || !evidenceDetails) {
            return;
        }

        const requestBody = buildEvidenceRequestDto({
            evidenceDetails,
            currentLibraryVersion,
            requestBody: values,
        });

        if (!requestBody) {
            return;
        }

        this.updateEvidenceMutation.mutate({
            body: { ...requestBody, ownerId: evidenceDetails.user?.id },
            path: {
                id: evidenceDetails.id,
                xProductId: currentWorkspace.id,
            },
        });

        when(
            () => !this.isUpdating,
            () => {
                const updatePlace = fromEvidenceLibraryView
                    ? 'Evidence Library'
                    : 'Evidence Overview';

                if (this.hasUpdateError) {
                    snackbarController.addSnackbar({
                        id: 'evidence-library-update-artifact-dates-error',
                        props: {
                            title: 'Evidence could not be updated',
                            description: `There was an error updating the evidence details from ${updatePlace}`,
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'evidence-library-update-artifact-dates-success',
                    props: {
                        title: 'Evidence successfully updated',
                        description: `${evidenceDetails.name} has been successfully updated from ${updatePlace}`,
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });

                closeRenewalDateModal();
            },
        );
    };

    unlinkControl = (control: EvidenceControl): void => {
        const { code, id: controlId } = control;
        const { evidenceDetailsData } = sharedEvidenceDetailsController;
        const controlIds = evidenceDetailsData?.controls
            .filter(({ id }) => id !== controlId)
            .map(({ id }) => id);

        if (!controlIds) {
            return;
        }

        openConfirmationModal({
            title: t`Unmap control?`,
            body: t`This control will no longer be mapped to this evidence.`,
            confirmText: t`OK`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: () => {
                this.updateEvidenceControls(controlIds);
                when(
                    () => !this.isUpdating,
                    () => {
                        if (this.hasUpdateError) {
                            return;
                        }

                        snackbarController.addSnackbar({
                            id: `unmap-control-success-${controlId}`,
                            props: {
                                title: t`Control successfully unmapped.`,
                                description: t`Control ${code} has been successfully unmapped.`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        panelController.closePanel();
                        closeConfirmationModal();
                    },
                );
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    };

    linkControls = (controls: EvidenceControl[]): void => {
        const { evidenceDetailsData } = sharedEvidenceDetailsController;
        const existingControlIds = evidenceDetailsData?.controls.map(
            (control) => control.id,
        );

        if (!existingControlIds) {
            return;
        }

        this.updateEvidenceControls([
            ...existingControlIds,
            ...map(controls, 'id'),
        ]);

        when(
            () => !this.isUpdating,
            () => {
                if (this.hasUpdateError) {
                    return;
                }

                const codes = map(controls, 'code').join(', ');

                snackbarController.addSnackbar({
                    id: `link-control-success-${evidenceDetailsData?.id}`,
                    props: {
                        title: t`Controls successfully mapped.`,
                        description: t`Controls ${codes} have been successfully mapped.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                modalController.closeModal(LINK_CONTROLS_MODAL_ID);
            },
        );
    };

    deleteEvidence = (evidenceIds: number[], bulkProps?: EvidenceBulkProps) => {
        const { currentWorkspace } = sharedWorkspacesController;
        const { isAllRowsSelected = false } = bulkProps ?? {};

        if (isNil(currentWorkspace)) {
            snackbarController.addSnackbar({
                id: 'artifact-deleted-error',
                props: {
                    title: t`There was an error deleting the evidence.`,
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });

            return;
        }

        this.deleteEvidenceMutation.mutate({
            path: {
                xProductId: currentWorkspace.id,
            },
            body: {
                evidenceIds,
                isAllRowsOfEvidences: isAllRowsSelected,
            },
        });

        when(
            () => !this.isDeleting,
            () => {
                if (this.hasDeleteError) {
                    snackbarController.addSnackbar({
                        id: 'artifact-deleted-error',
                        props: {
                            title: t`There was an error deleting the evidence.`,
                            description:
                                this.deleteEvidenceMutation.error?.message,
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'evidence-deleted-successfully',
                    props: {
                        title: t`Evidence successfully deleted.`,
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            },
        );
    };

    createEvidenceArtifact = (
        values: FormValues,
        artifactType: Extract<
            EvidenceRequestDto['source'],
            'URL' | 'TICKET_PROVIDER' | 'S3_FILE'
        >,
        evidenceDetails: EvidenceResponseDto | null,
    ) => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace || !evidenceDetails) {
            snackbarController.addSnackbar({
                id: `artifact-created-error-${uniqueId()}`,
                props: {
                    title: t`Couldn't create artifact`,
                    description: t`Unable to create artifact`,
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });

            return;
        }

        const parsedValues = values as {
            artifactDatesInput: {
                creationDate: string;
                renewalDate: string;
                renewalFrequency: ListBoxItemData;
            };
            owner: ListBoxItemData;
            url?: string;
            ticketUrl?: string;
            file?: File;
            externalFile?: ExternalFile;
        };

        const body = {
            source: artifactType,
            name: evidenceDetails.name,
            filedAt: parsedValues.artifactDatesInput.creationDate,
            renewalDate: parsedValues.artifactDatesInput.renewalDate,
            renewalScheduleType:
                parsedValues.artifactDatesInput.renewalFrequency.value,
            controlIds: evidenceDetails.controls.map((c) => c.id),
            ownerId: Number(parsedValues.owner.value),
        } as EvidenceRequestDto;

        // Add type-specific properties
        if (artifactType === 'URL' && parsedValues.url) {
            body.url = parsedValues.url;
        } else if (
            artifactType === 'TICKET_PROVIDER' &&
            parsedValues.ticketUrl
        ) {
            body.ticketUrl = parsedValues.ticketUrl;
        } else if (parsedValues.externalFile || parsedValues.file) {
            const fileBodyContent = buildFileBodyContent(parsedValues);

            body.source = fileBodyContent.source;
            body.file = fileBodyContent.file;
            body.dataContent = fileBodyContent.dataContent;
        }

        this.updateEvidenceMutation.mutate({
            body,
            path: {
                id: evidenceDetails.id,
                xProductId: currentWorkspace.id,
            },
        });

        when(
            () => !this.isUpdating,
            () => {
                if (this.hasUpdateError) {
                    snackbarController.addSnackbar({
                        id: `artifact-created-error-${uniqueId()}`,
                        props: {
                            title: t`Couldn't create artifact`,
                            description: t`Unable to create artifact`,
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: `artifact-created-success-${uniqueId()}`,
                    props: {
                        title: t`Evidence successfully updated`,
                        description: t`The evidence has been updated successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            },
        );
    };

    bulkUpdateEvidenceOwner = (
        values: FormValues,
        evidenceIds: number[],
        bulkProps?: EvidenceBulkProps,
    ) => {
        const { currentWorkspace } = sharedWorkspacesController;
        const { isAllRowsSelected = false } = bulkProps ?? {};

        if (!currentWorkspace || (!isAllRowsSelected && isEmpty(evidenceIds))) {
            return;
        }

        const parsedValues = values as {
            owner: ListBoxItemData;
        };

        this.evidenceOwnerBulkMutation.mutate({
            body: {
                evidenceIds,
                newOwnerId: Number(parsedValues.owner.value),
                isAllRowsOfEvidences: isAllRowsSelected,
            },
            path: {
                xProductId: currentWorkspace.id,
            },
        });

        when(
            () => !this.isBulkOwnerAssigning,
            () => {
                if (this.hasBulkOwnerAssignError) {
                    snackbarController.addSnackbar({
                        id: `bulk-update-evidence-error-${uniqueId()}`,
                        props: {
                            title: t`Couldn't update evidence`,
                            description: t`Unable to update evidence`,
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: `bulk-update-evidence-success-${uniqueId()}`,
                    props: {
                        title: t`Evidence successfully updated`,
                        description: t`The evidence has been updated successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            },
        );
    };
}

export const sharedEvidenceMutationController =
    new EvidenceMutationController();
