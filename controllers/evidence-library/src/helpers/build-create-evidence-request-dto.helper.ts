import type { EvidenceOwner, ExternalFile } from '@components/evidence-library';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import type {
    ControlListResponseDto,
    EvidenceLibraryControllerCreateEvidenceData,
} from '@globals/api-sdk/types';
import type { FormValues } from '@ui/forms';
import { buildFileBodyContent } from './build-file-body-content.helper';

export const buildCreateEvidenceRequestDto = (
    requestBody: FormValues,
): EvidenceLibraryControllerCreateEvidenceData['body'] | null => {
    const typedRequestBody = requestBody as {
        name: string;
        description?: string;
        implementationGuidance?: string;
        source: 'URL' | 'TICKET_PROVIDER' | 'NEW_EVIDENCE' | 'NONE';
        url?: string;
        ticketUrl?: string;
        file?: File;
        creationDate?: TDateISODate;
        renewalDate?: TDateISODate;
        renewalFrequency?: NonNullable<
            EvidenceLibraryControllerCreateEvidenceData['body']
        >['renewalScheduleType'];
        owner?: EvidenceOwner;
        controls?: ControlListResponseDto[];
        externalFile?: ExternalFile;
    };

    const baseRequest = {
        name: typedRequestBody.name,
        description: typedRequestBody.description,
        implementationGuidance: typedRequestBody.implementationGuidance,
        filedAt: typedRequestBody.creationDate,
        renewalDate: typedRequestBody.renewalDate,
        renewalScheduleType: typedRequestBody.renewalFrequency,
        ownerId: typedRequestBody.owner?.id
            ? Number(typedRequestBody.owner.id)
            : undefined,
        controlIds: typedRequestBody.controls?.map((c) => c.id),
    };

    const fileBodyContent = buildFileBodyContent(typedRequestBody);

    switch (typedRequestBody.source) {
        case 'URL': {
            return {
                ...baseRequest,
                url: typedRequestBody.url,
                source: 'URL',
            };
        }
        case 'TICKET_PROVIDER': {
            return {
                ...baseRequest,
                ticketUrl: typedRequestBody.ticketUrl,
                source: 'TICKET_PROVIDER',
            };
        }
        case 'NEW_EVIDENCE': {
            return {
                ...baseRequest,
                ...fileBodyContent,
            };
        }
        case 'NONE':
        default: {
            return {
                name: typedRequestBody.name,
                description: typedRequestBody.description,
                implementationGuidance: typedRequestBody.implementationGuidance,
                source: typedRequestBody.source,
                controlIds: typedRequestBody.controls?.map((c) => c.id),
                renewalScheduleType: 'NONE',
                ...(typedRequestBody.owner && {
                    ownerId: Number(typedRequestBody.owner.id),
                }),
            };
        }
    }
};
