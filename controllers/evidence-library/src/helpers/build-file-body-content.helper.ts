import type { ExternalFile } from '@components/evidence-library';
import type { EvidenceRequestDto } from '@globals/api-sdk/types';

export const buildFileBodyContent = ({
    file,
    externalFile,
}: {
    file?: File;
    externalFile?: ExternalFile;
}): {
    source: EvidenceRequestDto['source'];
    file?: File;
    dataContent?: string;
} => {
    if (externalFile) {
        return {
            source: externalFile.provider.source,
            // we need to cast it since the API expects a File object
            // there is no way to generate union type of File and string
            file: JSON.stringify(externalFile) as unknown as File,
            dataContent: JSON.stringify({
                cloudProvider: externalFile.sourceCloudProvider,
                cloudFileId: externalFile.recordId,
            }),
        };
    }

    return {
        source: 'S3_FILE',
        file,
        dataContent: undefined,
    };
};
