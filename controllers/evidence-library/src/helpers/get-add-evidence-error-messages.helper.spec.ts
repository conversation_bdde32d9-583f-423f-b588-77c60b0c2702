import { afterAll, describe, expect, test, vi } from 'vitest';
import { i18n } from '@globals/i18n';
import { ADD_EVIDENCE_ERROR_CODES } from '../constants/add-evidence-error-codes.constant';
import { getAddEvidenceErrorMessages } from './get-add-evidence-error-messages.helper';

// Mock the Lingui core to prevent activation errors
vi.mock('@globals/i18n/macro', () => ({
    t: (str: string) => str,
}));

describe('getAddEvidenceErrorMessages', () => {
    afterAll(() => {
        // Reset i18n state by loading empty messages
        // There's no direct "deactivate" method, but we can reset to a clean state
        i18n.load('en', {});
    });

    test('should return default error message when error code is not recognized', () => {
        const result = getAddEvidenceErrorMessages(999);

        expect(result).toStrictEqual({
            title: `Couldn't create evidence`,
            description: `We ran into an issue while adding . Try again later or contact support.`,
        });
    });

    test('should return invalid ticket status error message', () => {
        const result = getAddEvidenceErrorMessages(
            ADD_EVIDENCE_ERROR_CODES.INVALID_TICKET_STATUS,
        );

        expect(result).toStrictEqual({
            title: `Couldn't add evidence`,
            description: `Ticket must be closed before it can be added as evidence.`,
        });
    });

    test('should return ticket attachment size limit error message with extracted size', () => {
        const result = getAddEvidenceErrorMessages(
            ADD_EVIDENCE_ERROR_CODES.TICKET_ATTACHMENT_SIZE_LIMIT,
            'File size exceeds the limit of 15',
        );

        expect(result).toStrictEqual({
            title: `Ticket attachments exceed size limit`,
            description: `Combined file size can't exceed 15 MB. Reduce total size and try again.`,
        });
    });

    test('should return default error message when size limit error has no message', () => {
        const result = getAddEvidenceErrorMessages(
            ADD_EVIDENCE_ERROR_CODES.TICKET_ATTACHMENT_SIZE_LIMIT,
        );

        expect(result).toStrictEqual({
            title: `Couldn't create evidence`,
            description: `We ran into an issue while adding . Try again later or contact support.`,
        });
    });

    test('should return ticket not found error message with URL', () => {
        const ticketUrl = 'https://jira.example.com/ticket-123';
        const result = getAddEvidenceErrorMessages(
            ADD_EVIDENCE_ERROR_CODES.TICKET_NOT_FOUND,
            undefined,
            { ticketUrl },
        );

        expect(result).toStrictEqual({
            title: `Couldn't create evidence`,
            description: `We couldn't locate the Jira ticket at ${ticketUrl}. Check the link and try again.`,
        });
    });

    test('should return file format mismatch error message with evidence name', () => {
        const evidenceName = 'Test Evidence';
        const result = getAddEvidenceErrorMessages(
            ADD_EVIDENCE_ERROR_CODES.FILE_FORMAT_MISMATCH,
            undefined,
            { name: evidenceName },
        );

        expect(result).toStrictEqual({
            title: `Couldn't create evidence`,
            description: `File extension doesn't match the actual file format for ${evidenceName}. Use a valid format and try again.`,
        });
    });

    test('should include evidence name in default error message', () => {
        const evidenceName = 'Test Evidence';
        const result = getAddEvidenceErrorMessages(999, undefined, {
            name: evidenceName,
        });

        expect(result).toStrictEqual({
            title: `Couldn't create evidence`,
            description: `We ran into an issue while adding ${evidenceName}. Try again later or contact support.`,
        });
    });
});
