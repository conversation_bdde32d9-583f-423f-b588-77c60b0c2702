import { describe, expect, test } from 'vitest';
import type { FormValues } from '@ui/forms';
import { buildCreateEvidenceRequestDto } from './build-create-evidence-request-dto.helper';

describe('buildCreateEvidenceRequestDto', () => {
    test('should build URL source request correctly', () => {
        const formValues: FormValues = {
            name: 'Test Evidence',
            description: 'Test Description',
            implementationGuidance: 'Test Guidance',
            source: 'URL',
            url: 'https://example.com',
            creationDate: '2023-01-01',
            renewalDate: '2024-01-01',
            renewalFrequency: 'ANNUAL',
            owner: { id: '123', name: 'Test Owner' },
            controls: [{ id: 1 }, { id: 2 }],
        };

        const result = buildCreateEvidenceRequestDto(formValues);

        expect(result).toStrictEqual({
            name: 'Test Evidence',
            description: 'Test Description',
            implementationGuidance: 'Test Guidance',
            filedAt: '2023-01-01',
            renewalDate: '2024-01-01',
            renewalScheduleType: 'ANNUAL',
            ownerId: 123,
            controlIds: [1, 2],
            url: 'https://example.com',
            source: 'URL',
        });
    });

    test('should build TICKET_PROVIDER source request correctly', () => {
        const formValues: FormValues = {
            name: 'Ticket Evidence',
            description: 'Ticket Description',
            source: 'TICKET_PROVIDER',
            ticketUrl: 'https://jira.example.com/ticket-123',
            controls: [{ id: 3 }],
        };

        const result = buildCreateEvidenceRequestDto(formValues);

        expect(result).toStrictEqual({
            name: 'Ticket Evidence',
            description: 'Ticket Description',
            implementationGuidance: undefined,
            filedAt: undefined,
            renewalDate: undefined,
            renewalScheduleType: undefined,
            ownerId: undefined,
            controlIds: [3],
            ticketUrl: 'https://jira.example.com/ticket-123',
            source: 'TICKET_PROVIDER',
        });
    });

    test('should build FILE source request correctly', () => {
        const mockFile = new File(['test content'], 'test.pdf', {
            type: 'application/pdf',
        });
        const formValues: FormValues = {
            name: 'File Evidence',
            source: 'NEW_EVIDENCE',
            file: mockFile,
            controls: [],
        };

        const result = buildCreateEvidenceRequestDto(formValues);

        expect(result).toStrictEqual({
            name: 'File Evidence',
            description: undefined,
            implementationGuidance: undefined,
            filedAt: undefined,
            renewalDate: undefined,
            renewalScheduleType: undefined,
            ownerId: undefined,
            controlIds: [],
            dataContent: undefined,
            file: mockFile,
            source: 'S3_FILE',
        });
    });

    test('should build NONE source request correctly', () => {
        const formValues: FormValues = {
            name: 'No Source Evidence',
            source: 'NONE',
            controls: [{ id: 5 }],
        };

        const result = buildCreateEvidenceRequestDto(formValues);

        expect(result).toStrictEqual({
            name: 'No Source Evidence',
            description: undefined,
            implementationGuidance: undefined,
            source: 'NONE',
            controlIds: [5],
            renewalScheduleType: 'NONE',
        });
    });

    test('should handle default case same as NONE', () => {
        const formValues: FormValues = {
            name: 'Default Case',
            source: 'INVALID_SOURCE' as unknown,
        };

        const result = buildCreateEvidenceRequestDto(formValues);

        expect(result).toStrictEqual({
            name: 'Default Case',
            description: undefined,
            implementationGuidance: undefined,
            source: 'INVALID_SOURCE',
            controlIds: undefined,
            renewalScheduleType: 'NONE',
        });
    });
});
