import { isEmpty, isNil } from 'lodash-es';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import {
    libraryTestTemplateControllerGetTestTemplateDetailsOptions,
    libraryTestTemplateControllerGetTestTemplateMappingsOptions,
    libraryTestTemplateControllerValidateBulkAddTestTemplatesMutation,
} from '@globals/api-sdk/queries';
import type {
    ControlTestInstanceBaseResponseDto,
    LibraryTestTemplateDetailsResponseDto,
    LibraryTestTemplateMappedControlDto,
    LibraryTestTemplateMappingsResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    LIBRARY_TEST_MAPPING_FRAMEWORK_FILTER_ID,
    LIBRARY_TEST_MAPPING_NAME_ID,
} from '@views/library-test-mappings';

class LibraryTestController {
    pagination = {
        page: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        pageIndex: 0,
        pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
    };

    filters: { frameworkTemplateIds: number[] } = {
        frameworkTemplateIds: [],
    };

    testQuery = new ObservedQuery(
        libraryTestTemplateControllerGetTestTemplateDetailsOptions,
    );

    getTestTemplateControls = new ObservedQuery(
        libraryTestTemplateControllerGetTestTemplateMappingsOptions,
    );

    validateTestTemplateMutation = new ObservedMutation(
        libraryTestTemplateControllerValidateBulkAddTestTemplatesMutation,
    );

    get isLoading(): boolean {
        return this.testQuery.isLoading;
    }

    get error(): Error | null {
        return this.testQuery.error;
    }

    get isLoadingTestTemplateControls(): boolean {
        return this.getTestTemplateControls.isLoading;
    }

    constructor() {
        makeAutoObservable(this);
    }

    get testTemplateControls(): LibraryTestTemplateMappingsResponseDto[] {
        return this.getTestTemplateControls.data?.data ?? [];
    }

    get totalMonitorControls(): number {
        return this.getTestTemplateControls.data?.total ?? 0;
    }

    get monitoringControlInstance(): LibraryTestTemplateDetailsResponseDto | null {
        return this.testQuery.data;
    }

    get testName(): string {
        return this.monitoringControlInstance?.name ?? t`Test`;
    }

    get isAiGenerated(): boolean {
        return (
            !isNil(this.monitoringControlInstance) &&
            ['DRATA_LIBRARY'].includes(this.monitoringControlInstance.source)
        );
    }

    get remedyDescription(): string {
        return this.monitoringControlInstance?.remedyDescription ?? '';
    }

    /**
     * Returns the controls associated with the current test.
     * This data is available in the test details response without requiring a separate API call.
     */
    get testControls(): LibraryTestTemplateMappedControlDto[] {
        if (isNil(this.monitoringControlInstance)) {
            return [];
        }

        return this.monitoringControlInstance.controls;
    }

    /**
     * Returns the Active Tests for the current test in all tenants.
     */
    get activeTests(): ControlTestInstanceBaseResponseDto[] {
        if (isNil(this.monitoringControlInstance)) {
            return [];
        }

        if (isNil(this.monitoringControlInstance.activeTests)) {
            return [];
        }

        return this.monitoringControlInstance.activeTests;
    }

    get templateHasErrors(): boolean {
        return !isEmpty(this.validateTestTemplateMutation.response?.errors);
    }

    validateTestTemplate = (testId: number): void => {
        when(
            () => !sharedWorkspacesController.isLoading,
            () => {
                const { workspaces } = sharedWorkspacesController;
                const workspaceIds = workspaces.map(
                    (workspace) => workspace.id,
                );

                if (isEmpty(workspaceIds) || isNil(testId)) {
                    logger.error({
                        message:
                            'Failed to validate test template. Missing workspace IDs or test ID',
                        additionalInfo: {
                            workspaceIds,
                            testId,
                        },
                    });

                    return;
                }

                this.validateTestTemplateMutation.mutate({
                    body: {
                        testTemplateIds: [testId],
                        productIds: workspaceIds,
                        isDraft: false,
                    },
                });
            },
        );
    };

    loadTest = (testId: number) => {
        when(
            () => !sharedWorkspacesController.isLoading,
            () => {
                this.testQuery.load({
                    path: { templateId: testId },
                });
                this.getTestTemplateControls.load({
                    path: { templateId: testId },
                    query: {
                        ...this.pagination,
                        ...this.filters,
                        limit: this.pagination.pageSize,
                    },
                });
            },
        );
    };

    loadTestTemplateControls = (params?: FetchDataResponseParams): void => {
        const testId = this.testQuery.data?.testId;

        if (isNil(testId)) {
            return;
        }
        if (params) {
            this.pagination = {
                page: params.pagination.page || 1,
                pageSize: params.pagination.pageSize || DEFAULT_PAGE_SIZE,
                pageIndex: params.pagination.pageIndex || 0,
                pageSizeOptions: params.pagination.pageSizeOptions,
            };
            const frameworksFilterValues = params.globalFilter.filters[
                LIBRARY_TEST_MAPPING_FRAMEWORK_FILTER_ID
            ].value as { value: string }[] | undefined;

            this.filters.frameworkTemplateIds =
                frameworksFilterValues?.map((item) => Number(item.value)) ?? [];
        }
        const sortItem = params?.sorting[0];
        const sortByName =
            sortItem?.id === LIBRARY_TEST_MAPPING_NAME_ID
                ? LIBRARY_TEST_MAPPING_NAME_ID
                : undefined;

        this.getTestTemplateControls.load({
            path: { templateId: testId },
            query: {
                ...this.pagination,
                ...this.filters,
                limit: this.pagination.pageSize,
                sort: sortByName,
                sortDir: sortItem?.desc ? 'DESC' : 'ASC',
            },
        });
    };
}

export const activeLibraryTestController = new LibraryTestController();
