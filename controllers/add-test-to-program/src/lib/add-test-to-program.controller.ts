import { isEmpty, isError } from 'lodash-es';
import { sharedConnectionsController } from '@controllers/connections';
import { activeLibraryTestController } from '@controllers/library-test';
import { snackbarController } from '@controllers/snackbar';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import {
    libraryTestTemplateControllerGetControlTestInstanceByNameOptions,
    libraryTestTemplateControllerImportTestTemplateByIdMutation,
} from '@globals/api-sdk/queries';
import type {
    ControlTestInstanceResponseDto,
    ControlTestTemplateValidationResponseDto,
    LibraryTestTemplateImportRequestDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    reaction,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

export interface Workspace
    extends Required<Pick<ListBoxItemData, 'id' | 'label' | 'value'>> {}

export interface ModifiedDetails {
    name: string;
    description: string;
}

export type TestType = 'draft' | 'published';

class AddTestToProgramController {
    hasAttemptedWorkspacesValidation = false;
    selectedWorkspaces: Workspace[] = [];
    modifiedDetails: ModifiedDetails | null = null;
    testNameValidationQuery = new ObservedQuery(
        libraryTestTemplateControllerGetControlTestInstanceByNameOptions,
    );
    importTestMutation = new ObservedMutation(
        libraryTestTemplateControllerImportTestTemplateByIdMutation,
    );
    testType: TestType = 'published';

    constructor() {
        makeAutoObservable(this);

        reaction(
            () => {
                const { monitoringControlInstance, isLoading } =
                    activeLibraryTestController;

                return {
                    testName:
                        this.modifiedDetails?.name ??
                        monitoringControlInstance?.name ??
                        '',
                    isLoading,
                };
            },
            ({ testName, isLoading }) => {
                if (testName && !isLoading) {
                    this.validateTestName(testName);
                }
            },
        );
    }

    get isWorkspacesStepValid(): boolean {
        if (!this.hasAttemptedWorkspacesValidation) {
            return true;
        }

        return !isEmpty(this.selectedWorkspaces);
    }

    get availableWorkspaces(): Workspace[] {
        const { workspaces } = sharedWorkspacesController;

        if (!Array.isArray(workspaces)) {
            return [];
        }

        return workspaces
            .filter((workspace) => this.workspaceHasValidConnection(workspace))
            .map((workspace) => ({
                id: workspace.id.toString(),
                label: workspace.name,
                value: workspace.id.toString(),
            }));
    }

    workspaceHasValidConnection = (workspace: { id: number }): boolean => {
        const configuredConnections =
            sharedConnectionsController.allConfiguredConnections;
        const requiredClientTypes =
            activeLibraryTestController.monitoringControlInstance
                ?.connectionClientTypes;

        // If there are no required client types, allow all workspaces
        if (!requiredClientTypes || isEmpty(requiredClientTypes)) {
            return true;
        }

        // Check if this workspace has at least one connection with the required client types
        return configuredConnections.some((connection) => {
            const connectionHasRequiredClientType =
                requiredClientTypes.includes(connection.clientType);
            const connectionIncludesWorkspace = connection.workspaces.some(
                (connectionWorkspace) =>
                    connectionWorkspace.id === workspace.id,
            );

            return (
                connectionHasRequiredClientType && connectionIncludesWorkspace
            );
        });
    };

    get testNameValidationResult(): ControlTestTemplateValidationResponseDto | null {
        return this.testNameValidationQuery.data ?? null;
    }

    get isTestNameValid(): boolean {
        return this.testNameValidationResult?.valid ?? false;
    }

    get existingTests(): ControlTestInstanceResponseDto[] {
        return this.testNameValidationResult?.controlTestInstances ?? [];
    }

    get isTestNameValidationLoading(): boolean {
        return this.testNameValidationQuery.isLoading;
    }

    get isMultiProduct(): boolean {
        return sharedWorkspacesController.workspaces.length > 1;
    }

    validateTestName = (name: string): void => {
        if (!name.trim()) {
            return;
        }

        this.testNameValidationQuery.load({
            body: { name: name.trim() },
        });
    };

    setSelectedWorkspaces = (workspaces: Workspace[]): void => {
        this.selectedWorkspaces = workspaces;
    };

    setModifiedDetails = (details: ModifiedDetails): void => {
        this.modifiedDetails = details;
    };

    setTestType = (type: TestType): void => {
        this.testType = type;
    };

    validateWorkspacesStep = (): Promise<boolean> => {
        this.hasAttemptedWorkspacesValidation = true;

        return Promise.resolve(this.isWorkspacesStepValid);
    };

    resetWorkspacesValidation = (): void => {
        if (isEmpty(this.selectedWorkspaces)) {
            this.hasAttemptedWorkspacesValidation = false;
        }
    };

    importTest = (templateId: number): Promise<void> => {
        if (!this.modifiedDetails) {
            return Promise.reject(new Error('Modified details are required'));
        }

        const workspaceIds = this.selectedWorkspaces.map((workspace) =>
            Number(workspace.id),
        );

        const requestData: LibraryTestTemplateImportRequestDto = {
            name: this.modifiedDetails.name,
            description: this.modifiedDetails.description,
            draft: this.testType === 'draft',
            workspaceIds,
        };

        return this.importTestMutation
            .mutateAsync({
                path: { templateId },
                body: requestData,
            })
            .then(() => {
                snackbarController.addSnackbar({
                    id: 'test-added-success',
                    hasTimeout: true,
                    props: {
                        title: t`Test added`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch((error: unknown) => {
                snackbarController.addSnackbar({
                    id: 'test-added-error',
                    props: {
                        title: t`Failed to add test`,
                        description: isError(error)
                            ? error.message
                            : t`Please try again`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    removeWorkspace = (workspaceId: string): void => {
        const updatedWorkspaces = this.selectedWorkspaces.filter(
            (workspace: Workspace) => workspace.id !== workspaceId,
        );

        this.setSelectedWorkspaces(updatedWorkspaces);
        this.resetWorkspacesValidation();
    };
}

export const sharedAddTestToProgramController =
    new AddTestToProgramController();
