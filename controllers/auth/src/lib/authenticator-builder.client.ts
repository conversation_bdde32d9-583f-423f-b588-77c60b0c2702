import { isEmpty, isNil } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    authControllerGenerateIdentityProviderTypeMutation,
    authControllerGenerateMagicLinkMutation,
    authControllerGetAccountTypesForEmailOptions,
    serviceUserControllerAgreeToTermsMutation,
} from '@globals/api-sdk/queries';
import type { Regions } from '@globals/config';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { AuditorActAsAuthenticator } from './auditor-act-as-authenticator.client';
import { AuditorAuthenticator } from './auditor-authenticator.client';
import { TenantAuthenticator } from './tenant-authenticator.client';
import type { Authenticator, AuthenticatorState } from './types/auth.types';
import type { AuthModes } from './types/auth-modes.type';
import type { AuthType } from './types/auth-type.type';
import type { Company } from './types/company.type';

export class AuthenticatorBuilder implements Authenticator {
    email: string | null = null;
    accessToken: string | null = null;
    authMode: AuthModes | null = null;
    companies: Company[] = [];
    region: Regions | null = null;
    authType: AuthType | null = null;
    initialRedirectionUrl: string | null = null;
    redirectionRoutes: Record<string, string> = {};
    loginNextStepQueryParams: URLSearchParams | null = null;
    loginNextStepUrl: string | null = null;
    accountTypeQuery = new ObservedQuery(
        authControllerGetAccountTypesForEmailOptions,
    );
    authTypeMutation = new ObservedMutation(
        authControllerGenerateIdentityProviderTypeMutation,
    );
    generateMagicLinkMutation = new ObservedMutation(
        authControllerGenerateMagicLinkMutation,
    );
    agreeToTermsMutation = new ObservedMutation(
        serviceUserControllerAgreeToTermsMutation,
    );
    loginMagicLinkMutation = null;
    defaultNextLoginStep = '/auth/login';
    defaultNextLoginStepQueryParams = 'completed';

    constructor() {
        makeAutoObservable(this);
    }

    get hasAttemptedLoginError(): boolean {
        return (
            Boolean(this.accountTypeQuery.error) ||
            Boolean(this.authTypeMutation.error)
        );
    }

    get hasAttemptedLogin(): boolean {
        return (
            Boolean(this.accountTypeQuery.data) ||
            Boolean(this.authTypeMutation.mutation?.state?.data) ||
            Boolean(this.loginNextStepUrl)
        );
    }

    get isAttemptingLogin(): boolean {
        return (
            this.accountTypeQuery.isLoading || this.authTypeMutation.isPending
        );
    }

    get isAcceptTermsPending(): boolean {
        return this.agreeToTermsMutation.isPending;
    }

    get hasAcceptTermsError(): boolean {
        return this.agreeToTermsMutation.hasError;
    }

    get hasAcceptTermsSuccess(): boolean {
        return Boolean(this.agreeToTermsMutation.mutation?.state?.isSuccess);
    }

    get token(): string | null {
        return null;
    }

    initFromPersistedState = (
        state: Partial<AuthenticatorState>,
    ): Authenticator => {
        const { authMode = null } = state;

        switch (authMode) {
            case 'NORMAL': {
                const authenticator = new TenantAuthenticator();

                return authenticator.initFromPersistedState(state);
            }
            case 'AUDITOR': {
                const authenticator = new AuditorAuthenticator();

                return authenticator.initFromPersistedState(state);
            }
            case 'AUDITOR_READ_ONLY': {
                const authenticator = new AuditorActAsAuthenticator();

                return authenticator.initFromPersistedState(state);
            }
            case 'ACT_AS':
            case 'ACT_AS_READ_ONLY':
            case 'ACT_AS_SERVICE_USER':
            case 'SERVICE_USER':
            default: {
                return this;
            }
        }
    };

    logout(): void {
        console.error('Cannot logout with the builder authenticator');
    }

    getPersistableState(): AuthenticatorState {
        return {
            email: this.email,
            accessToken: this.accessToken,
            authMode: this.authMode,
            companies: this.companies,
            authType: this.authType,
            region: this.region,
            initialRedirectionUrl: this.initialRedirectionUrl,
        };
    }

    persistState(state: AuthenticatorState | null): void {
        window.localStorage.setItem('authenticator', JSON.stringify(state));
    }

    setEmail(email: string): void {
        this.email = email;
    }

    setRegion(region: Regions): void {
        this.region = region;
    }

    setAccessToken(accessToken: string | null): void {
        this.accessToken = accessToken;
    }

    setAuthMode(authMode: AuthModes): Authenticator {
        if (authMode === 'NORMAL') {
            const newTenantAuthenticator = new TenantAuthenticator();

            newTenantAuthenticator.initFromPersistedState({
                ...this.getPersistableState(),
                authMode: 'NORMAL',
            });

            return newTenantAuthenticator;
        }

        if (authMode === 'AUDITOR') {
            const newAuditorAuthenticator = new AuditorAuthenticator();

            newAuditorAuthenticator.initFromPersistedState({
                ...this.getPersistableState(),
                authMode: 'AUDITOR',
            });

            return newAuditorAuthenticator;
        }

        if (authMode === 'AUDITOR_READ_ONLY') {
            const newAuditorActAsAuthenticator =
                new AuditorActAsAuthenticator();

            newAuditorActAsAuthenticator.initFromPersistedState({
                ...this.getPersistableState(),
                authMode: 'AUDITOR_READ_ONLY',
            });

            return newAuditorActAsAuthenticator;
        }

        return this;
    }

    setAuthType(authType: AuthType): void {
        this.authType = authType;
    }

    setLoginNextStepUrlForMultipleAccountTypes(): void {
        if (isNil(this.loginNextStepQueryParams)) {
            this.loginNextStepQueryParams = new URLSearchParams();
        }

        if (this.loginNextStepQueryParams.has('multiple-account-types')) {
            return;
        }

        this.loginNextStepQueryParams.append('multiple-account-types', 'true');
    }

    handleAuthModeLogin(): void {
        if (this.accountTypeQuery.data?.accountTypes.length === 1) {
            const singleAccountType =
                this.accountTypeQuery.data.accountTypes[0];

            switch (singleAccountType) {
                case 'SERVICE_ACCOUNT': {
                    this.authMode = 'SERVICE_USER';

                    return;
                }

                case 'AUDITOR_ACCOUNT': {
                    this.authMode = 'AUDITOR';

                    return;
                }

                case 'STANDARD_ACCOUNT': {
                    this.authMode = 'NORMAL';

                    return;
                }

                default: {
                    console.error('Unsupported account type');

                    return;
                }
            }
        }

        this.setLoginNextStepUrlForMultipleAccountTypes();
    }

    setDefaultNextStepLoginUrl(): void {
        this.loginNextStepUrl = this.defaultNextLoginStep;
        this.loginNextStepQueryParams = new URLSearchParams();
        this.loginNextStepQueryParams.append(
            this.defaultNextLoginStepQueryParams,
            'true',
        );
    }

    /**
     * This method is used to resend the magic link email.
     * It is used when the user clicks on the "Resend email" button.
     */
    resendMagicLinkEmail(): void {
        if (!this.email) {
            console.error('Cannot generate magic link without an email');

            return;
        }

        this.generateMagicLinkMutation.mutate({
            body: { email: this.email },
        });

        when(
            () => !this.generateMagicLinkMutation.isPending,
            () => {
                if (this.generateMagicLinkMutation.error) {
                    snackbarController.addSnackbar({
                        id: 'auditor-login-email-sent-error',
                        props: {
                            title: 'Email could not be sent. Please try again or contact support.',
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });
                } else {
                    this.persistState(this.getPersistableState());
                    snackbarController.addSnackbar({
                        id: 'auditor-login-email-sent-success',
                        props: {
                            title: 'Email was sent successfully.',
                            severity: 'success',
                            closeButtonAriaLabel: 'Close',
                        },
                    });
                }
            },
        );
    }

    generateMagicLink(): void {
        if (!this.email) {
            console.error('Cannot generate magic link without an email');

            return;
        }

        this.generateMagicLinkMutation
            .mutateAsync({ body: { email: this.email } })
            .catch((error) => {
                console.error('Magic link mutation failed');
                console.error(error);
            });

        when(
            () =>
                !this.generateMagicLinkMutation.isPending &&
                !this.generateMagicLinkMutation.error,
            () => {
                this.persistState(this.getPersistableState());
                this.setDefaultNextStepLoginUrl();
            },
        );
    }

    handleAuthTypeLogin(): void {
        switch (
            this.authTypeMutation.mutation?.state?.data?.identityProviderType
        ) {
            case 'MAGIC_LINK': {
                this.authType = 'MAGIC_LINK';
                const magicLinkUrl = 'localhost:5173';

                // This is simply the web app url.
                // const magicLinkUrl = sharedConfigController.loadConfig('magicLinkUrl');
                this.generateMagicLink();
                this.loginNextStepUrl = magicLinkUrl;

                return;
            }

            case 'OKTA': {
                this.authType = 'OKTA';
                const oktaUrl = 'localhost:5173';

                // For okta we need to hit an api endpoint so that it is correctly generated.
                // const oktaUrl = getOktaUrl();
                this.loginNextStepUrl = oktaUrl;

                return;
            }

            case 'GOOGLE': {
                this.authType = 'GOOGLE';
                const googleUrl = 'localhost:5173';

                // For google we can get it from the config.
                // const googleUrl = sharedConfigController.loadConfig('googleOAuthUrl');
                this.loginNextStepUrl = googleUrl;

                return;
            }

            case 'MICROSOFT_365': {
                this.authType = 'MICROSOFT_365';
                const microsoftUrl = 'localhost:5173';

                // For microsoft we can get it from the config.
                // const microsoftUrl = sharedConfigController.loadConfig('microsoftOAuthUrl');
                this.loginNextStepUrl = microsoftUrl;

                return;
            }

            case 'MICROSOFT_365_GCC_HIGH': {
                this.authType = 'MICROSOFT_365_GCC_HIGH';
                const microsoftGccHighUrl = 'localhost:5173';

                // For microsoft we can get it from the config.
                // const microsoftGccHighUrl = sharedConfigController.loadConfig('microsoftGccHighOAuthUrl');
                this.loginNextStepUrl = microsoftGccHighUrl;

                return;
            }

            case 'ENTERPRISE_SSO': {
                // this might be worksOS but not exactly sure about it.
                this.authType = 'ENTERPRISE_SSO';
                const enterpriseSsoUrl = 'localhost:5173';

                // For enterprise sso we can get it from the config.
                // const enterpriseSsoUrl = sharedConfigController.loadConfig('enterpriseSsoUrl');
                this.loginNextStepUrl = enterpriseSsoUrl;

                return;
            }

            default: {
                // Couldn't find information about enterprise sso. Might be a legacy identity provider.
                console.error('Unsupported auth type');
                this.setDefaultNextStepLoginUrl();

                return;
            }
        }
    }

    handleAuthenticationAttempt(): void {
        if (isEmpty(this.accountTypeQuery.data?.accountTypes)) {
            console.error('No account types returned from account type query');
            this.setDefaultNextStepLoginUrl();

            return;
        }

        if (
            !this.authTypeMutation.mutation?.state?.data?.identityProviderType
        ) {
            console.error('Error while fetching the auth type');
            this.setDefaultNextStepLoginUrl();

            return;
        }

        this.handleAuthModeLogin();

        this.handleAuthTypeLogin();
    }

    attemptLogin(region: Regions): void {
        if (!this.email) {
            console.error('Cannot attempt login without an email');

            return;
        }

        this.setRegion(region);

        this.accountTypeQuery.load({
            query: { email: this.email },
        });

        this.authTypeMutation
            .mutateAsync({ body: { email: this.email } })
            .catch((error) => {
                console.error('Auth type mutation failed');
                console.error(error);
            });

        when(
            () =>
                !this.authTypeMutation.isPending &&
                Boolean(
                    this.authTypeMutation.mutation?.state?.data
                        ?.identityProviderType ||
                        this.authTypeMutation.mutation?.state?.error,
                ) &&
                !this.accountTypeQuery.isLoading &&
                Boolean(
                    this.accountTypeQuery.data?.accountTypes ??
                        this.accountTypeQuery.error,
                ),
            () => {
                this.handleAuthenticationAttempt();
            },
        );
    }

    acceptTermsAndConditions(): void {
        this.agreeToTermsMutation.mutate();

        when(
            () => !this.agreeToTermsMutation.isPending,
            () => {
                if (this.agreeToTermsMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'auditor-accept-terms-error',
                        props: {
                            title: t`There was an error accepting the terms.`,
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });
                }
            },
        );
    }

    finalizeLogin(): void {
        console.error('Cannot finalize login with the builder authenticator');
    }
}
