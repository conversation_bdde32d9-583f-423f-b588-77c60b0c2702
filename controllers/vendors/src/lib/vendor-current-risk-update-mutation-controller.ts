import { uniqueId } from 'lodash-es';
import { panelController } from '@controllers/panel';
import { snackbarController } from '@controllers/snackbar';
import {
    sharedVendorsProfileRisksController,
    sharedVendorsRisksController,
} from '@controllers/vendors';
import { vendorsControllerUpdateRiskPartiallyMutation } from '@globals/api-sdk/queries';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';

export class VendorCurrentRiskUpdateMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    riskUpdateMutation = new ObservedMutation(
        vendorsControllerUpdateRiskPartiallyMutation,
    );

    get isPending(): boolean {
        return this.riskUpdateMutation.isPending;
    }

    get hasError(): boolean {
        return this.riskUpdateMutation.hasError;
    }

    updateRiskPartially = (
        riskId: string,
        requestBody: NonNullable<
            NonNullable<
                Parameters<
                    typeof vendorsControllerUpdateRiskPartiallyMutation
                >[0]
            >['body']
        >,
    ): void => {
        this.riskUpdateMutation.mutate({
            path: { risk_id: riskId },
            body: requestBody,
        });

        // close the panel for risks if active
        panelController.closePanel();

        when(
            () => !this.isPending,
            () => {
                sharedVendorsRisksController.risksQuery.invalidate();
                sharedVendorsRisksController.riskDashboardQuery.invalidate();
                sharedVendorsProfileRisksController.allVendorsRisksQuery.invalidate();

                if (this.hasError) {
                    snackbarController.addSnackbar({
                        id: uniqueId('vendor-risk-update-error'),
                        props: {
                            title: 'Error updating vendor risk data',
                            description:
                                'An error occurred while updating the risk data. Try again later.',
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: uniqueId('vendor-risk-update-success'),
                    props: {
                        title: 'Risk updated',
                        description:
                            'The vendor risk was updated successfully.',
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            },
        );
    };
}

export const sharedVendorCurrentRiskUpdateMutationController =
    new VendorCurrentRiskUpdateMutationController();
