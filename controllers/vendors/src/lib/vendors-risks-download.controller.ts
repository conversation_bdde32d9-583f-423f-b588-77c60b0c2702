import { isNil } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import { vendorsControllerGetRiskReportOptions } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { fileNameDate } from '@helpers/date-time';
import { downloadBlob } from '@helpers/download-file';

class VendorsRisksDownloadController {
    constructor() {
        makeAutoObservable(this);
    }

    vendorRisksReportQuery = new ObservedQuery(
        vendorsControllerGetRiskReportOptions,
    );

    get isLoading(): boolean {
        return this.vendorRisksReportQuery.isLoading;
    }

    get hasError(): boolean {
        return this.vendorRisksReportQuery.hasError;
    }

    get vendorRisksReport() {
        return this.vendorRisksReportQuery.data;
    }

    downloadVendorRisksReport = (): void => {
        this.vendorRisksReportQuery.load();

        when(
            () => !this.isLoading,
            () => {
                const reportData = this.vendorRisksReport;

                if (isNil(reportData)) {
                    snackbarController.addSnackbar({
                        id: 'vendor-risks-report-download-empty',
                        props: {
                            title: t`No vendor risks data available for download.`,
                            severity: 'warning',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                try {
                    const blob = new Blob([reportData as BlobPart], {
                        type: 'text/csv',
                    });

                    const fileName = `Risks-${fileNameDate()}.csv`;

                    downloadBlob(blob, fileName);

                    snackbarController.addSnackbar({
                        id: 'vendor-risks-report-download-success',
                        props: {
                            title: t`Vendor risks report downloaded successfully.`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } catch (error) {
                    console.error(
                        'Failed to download vendor risks report:',
                        error,
                    );
                    snackbarController.addSnackbar({
                        id: 'vendor-risks-report-download-error',
                        props: {
                            title: t`Failed to download vendor risks report.`,
                            description: t`Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
        );

        when(
            () => this.hasError,
            () => {
                snackbarController.addSnackbar({
                    id: 'vendor-risks-report-download-api-error',
                    props: {
                        title: t`There was an error downloading vendor risks report.`,
                        description: t`Please try again later.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };
}

export const sharedVendorsRisksDownloadController =
    new VendorsRisksDownloadController();
