import { isNaN, isNil, uniqBy } from 'lodash-es';
import type { CreateRiskCustomMutationType } from '@controllers/risk';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type {
    ControlListResponseDto,
    RiskThresholdResponseDto,
    RiskWithCustomFieldsResponseDto,
} from '@globals/api-sdk/types';
import { formatDate } from '@helpers/date-time';
import { getScoreIntensityByThresholds } from '@helpers/risk-score';
import type { VendorsRisksTableItem } from '../types/vendor-risk.type';

export const adaptRiskScoreSeverity = (
    risk: RiskWithCustomFieldsResponseDto,
    thresholds: RiskThresholdResponseDto[],
): VendorsRisksTableItem => {
    const {
        riskId,
        identifiedAt,
        title,
        description,
        vendor,
        impact,
        likelihood,
        score,
        residualImpact,
        residualLikelihood,
        residualScore,
        owners,
        treatmentPlan,
        anticipatedCompletionDate,
    } = risk;

    return {
        riskId,
        identifiedAt: isNil(identifiedAt)
            ? ''
            : formatDate('sentence', identifiedAt.split('T')[0]),
        title,
        description,
        vendorName: vendor?.name ?? null,
        impact,
        likelihood,
        score,
        residualImpact,
        residualLikelihood,
        residualScore,
        owners,
        treatmentPlan,
        anticipatedCompletionDate: isNil(anticipatedCompletionDate)
            ? ''
            : formatDate('table', anticipatedCompletionDate.split('T')[0]),
        inherentSeverity: getScoreIntensityByThresholds(score, thresholds),
        residualSeverity: getScoreIntensityByThresholds(
            residualScore,
            thresholds,
        ),
    };
};

export const getUniqueIdsList = (
    selectedItems: ListBoxItemData[],
): {
    id: number;
}[] => {
    return uniqBy(
        selectedItems.map((item) => ({ id: Number(item.id) })),
        'id',
    );
};

export const getInherentValue = (
    values: CreateRiskCustomMutationType,
    field: 'impact' | 'likelihood',
): number | null => {
    const { inherentScoreGroup } = values;

    const valueStr = inherentScoreGroup?.[field]?.value;

    const numericValue = Number(valueStr);

    return isNaN(numericValue) ? null : numericValue;
};

export const getResidualValue = (
    values: NonNullable<CreateRiskCustomMutationType['treatmentGroup']>,
    field: 'residualImpact' | 'residualLikelihood',
): number | null => {
    const { residualScoreGroup } = values;

    const valueStr = residualScoreGroup?.[field]?.value;

    const numericValue = Number(valueStr);

    return isNaN(numericValue) ? null : numericValue;
};

export const getScore = (
    values: CreateRiskCustomMutationType,
): number | null => {
    const impact = getInherentValue(values, 'impact');

    const likelihood = getInherentValue(values, 'likelihood');

    if (!impact || !likelihood) {
        return null;
    }

    return Number(impact) * Number(likelihood);
};

export const getResidualScore = (
    values: CreateRiskCustomMutationType['treatmentGroup'],
): number | null => {
    const residualImpact = values
        ? getResidualValue(values, 'residualImpact')
        : null;

    const residualLikelihood = values
        ? getResidualValue(values, 'residualLikelihood')
        : null;

    if (!residualImpact || !residualLikelihood) {
        return null;
    }

    return Number(residualImpact) * Number(residualLikelihood);
};

export const getAdaptedControls = (
    controls: ControlListResponseDto[],
): {
    id: number;
}[] => {
    return Array.isArray(controls)
        ? controls.map((control) => ({
              id: Number(control.id),
          }))
        : [];
};
