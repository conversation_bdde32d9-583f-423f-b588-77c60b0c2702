import { isEmpty, isNil, omit } from 'lodash-es';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { grcControllerSearchControlsOptions } from '@globals/api-sdk/queries';
import type { ControlListResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

export type ControlsSearchQuery = NonNullable<
    Required<Parameters<typeof grcControllerSearchControlsOptions>>[0]['query']
>;

class ControlsController {
    currentAppliedFilters: ControlsSearchQuery | null = null;
    hasEverHadData = false;
    emptyState: 'first-time' | 'no-results' | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    controlsListQuery = new ObservedQuery(grcControllerSearchControlsOptions);

    get controls(): ControlListResponseDto[] {
        return this.controlsListQuery.data?.data ?? [];
    }

    get total(): number {
        return this.controlsListQuery.data?.total ?? 0;
    }

    get isLoading(): boolean {
        return (
            this.controlsListQuery.isReady ||
            this.controlsListQuery.isLoading ||
            this.controlsListQuery.isFetching
        );
    }

    load = (query?: ControlsSearchQuery) => {
        const { currentWorkspace, isLoading } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        when(
            () => !isLoading,
            () => {
                this.controlsListQuery.load({
                    path: {
                        xProductId: currentWorkspace.id,
                    },
                    query: { ...query },
                });
            },
        );
    };

    #buildFilters = (
        query: ControlsSearchQuery,
        filters: Partial<FetchDataResponseParams['globalFilter']['filters']>,
    ): void => {
        if (!isNil(filters.isArchived?.value)) {
            query.isArchived = filters.isArchived.value === 'true';
        }

        if (!isNil(filters.isReady?.value)) {
            query.isReady = filters.isReady
                .value as ControlsSearchQuery['isReady'];
        }

        if (!isNil(filters.isMonitored?.value)) {
            query.isMonitored = filters.isMonitored
                .value as ControlsSearchQuery['isMonitored'];
        }

        if (!isNil(filters.requiredApprovals?.value)) {
            query.requiredApprovals = filters.requiredApprovals
                .value as ControlsSearchQuery['requiredApprovals'];
        }

        if (!isNil(filters.hasEvidence?.value)) {
            query.hasEvidence = filters.hasEvidence
                .value as ControlsSearchQuery['hasEvidence'];
        }

        if (!isNil(filters.hasPolicy?.value)) {
            query.hasPolicy = filters.hasPolicy
                .value as ControlsSearchQuery['hasPolicy'];
        }

        if (!isNil(filters.isOwned?.value)) {
            query.isOwned = filters.isOwned
                .value as ControlsSearchQuery['isOwned'];
        }

        if (!isNil(filters.approversAssigned?.value)) {
            query.approversAssigned =
                filters.approversAssigned.value === 'true';
        }

        if (!isNil(filters.frameworkTags?.value)) {
            query.frameworkTags = filters.frameworkTags
                .value as ControlsSearchQuery['frameworkTags'];
        }

        if (!isNil(filters.hasTicket?.value)) {
            query.hasTicket = filters.hasTicket
                .value as ControlsSearchQuery['hasTicket'];
        }

        if (!isNil(filters.userIds?.value)) {
            const { value } = filters.userIds.value as ListBoxItemData;

            query.userIds = [Number(value)];
        }
    };

    #buildSorting = (
        query: ControlsSearchQuery,
        sorting: FetchDataResponseParams['sorting'],
    ): void => {
        if (isEmpty(sorting)) {
            return;
        }

        const [{ id, desc }] = sorting;

        query.sort = id as ControlsSearchQuery['sort'];
        query.sortDir = desc ? 'DESC' : 'ASC';
    };

    loadPage = (
        params: FetchDataResponseParams,
        optionalQuery?: ControlsSearchQuery,
    ) => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        const { id: workspaceId } = currentWorkspace;
        const { pagination, globalFilter, sorting } = params;
        const { pageSize, page } = pagination;
        const { search, filters } = globalFilter;

        const query: ControlsSearchQuery = {
            page,
            limit: pageSize,
        };

        if (search) {
            query.q = search;
        }

        this.#buildFilters(query, filters);
        this.#buildSorting(query, sorting);

        this.currentAppliedFilters = query;

        this.controlsListQuery.load({
            path: { xProductId: workspaceId },
            query: { ...query, ...optionalQuery },
        });

        when(
            () => this.total > 0,
            () => {
                if (!this.hasEverHadData) {
                    this.hasEverHadData = true;
                }
            },
        );
        when(
            () => this.total === 0,
            () => {
                if (this.hasEverHadData) {
                    this.emptyState = 'no-results';
                } else {
                    this.emptyState = 'first-time';
                }
            },
        );
    };

    get hasActiveFilters(): boolean {
        if (!this.currentAppliedFilters) {
            return false;
        }

        return !isEmpty(
            Object.values(omit(this.currentAppliedFilters, ['page', 'limit'])),
        );
    }

    invalidateControlsList = () => {
        this.controlsListQuery.invalidate();
    };
}

export const sharedControlsController = new ControlsController();
