import { describe, expect, test, vi } from 'vitest';
import type {
    UpcomingTaskDetailResponseDto,
    UserResponseDto,
} from '@globals/api-sdk/types';
import { getTimeDiff } from '@helpers/date-time';
import { getSeverityByDiffInDays } from '@helpers/severity';
import type { TaskAccordionType } from '../types/tasks.types';
import { getActionHandlersByType } from './get-action-handlers-by-type.helper';
import { getDueDateMessage } from './get-due-date-message.helper';
import { mapNotCompletedTaskResponseToCellData } from './map-not-completed-task-response-to-cell-data.helper';

// Mock dependencies
vi.mock('@helpers/date-time', () => ({
    getTimeDiff: vi.fn(),
}));

vi.mock('./get-due-date-message.helper', () => ({
    getDueDateMessage: vi.fn(),
}));

vi.mock('@helpers/severity', () => ({
    getSeverityByDiffInDays: vi.fn(),
}));

vi.mock('./get-action-handlers-by-type.helper', () => ({
    getActionHandlersByType: vi.fn(),
}));

const mockUsers: UserResponseDto[] = [
    {
        id: 1,
        firstName: 'John',
        lastName: 'Doe',
        avatarUrl: 'avatar1.jpg',
        email: '<EMAIL>',
        drataTermsAgreedAt: '2023-12-31',
        createdAt: '2023-12-31',
        updatedAt: '2023-12-31',
        entryId: '1',
        identities: [],
        backgroundChecks: [],
        roles: [],
        documents: [],
        jobTitle: 'Engineer',
    },
    {
        id: 2,
        firstName: 'Jane',
        lastName: 'Smith',
        avatarUrl: 'avatar2.jpg',
        email: '<EMAIL>',
        drataTermsAgreedAt: '2023-12-31',
        createdAt: '2023-12-31',
        updatedAt: '2023-12-31',
        entryId: '1',
        identities: [],
        backgroundChecks: [],
        roles: [],
        documents: [],
        jobTitle: 'Engineer',
    },
];

describe('mapNotCompletedTaskResponseToCellData', () => {
    test('should map task response to cell data correctly', () => {
        // Setup
        const mockTask: UpcomingTaskDetailResponseDto = {
            id: 123,
            name: 'Test Task',
            renewalDate: '2023-12-31',
            userIds: [1, 2],
            configurationId: 1,
            type: 'CONTROL',
            completedAt: null,
            schedule: {
                id: 1,
                name: 'Daily',
                type: 'DAILY',
                value: 1,
            },
        };

        const mockType: TaskAccordionType = 'control';
        const mockDiffInDays = 5;
        const mockSeverity = 'warning';
        const mockDueDateMessage = 'Due in 5 days';
        const mockHandlers = { onEdit: vi.fn(), onReview: vi.fn() };

        // Mock return values
        vi.mocked(getTimeDiff).mockReturnValue(mockDiffInDays);
        vi.mocked(getSeverityByDiffInDays).mockReturnValue(mockSeverity);
        vi.mocked(getDueDateMessage).mockReturnValue(mockDueDateMessage);
        vi.mocked(getActionHandlersByType).mockReturnValue(mockHandlers);

        // Execute
        const result = mapNotCompletedTaskResponseToCellData(
            mockTask,
            mockUsers,
            mockType,
        );

        // Verify
        expect(result).toStrictEqual({
            id: '123',
            title: 'Test Task',
            dueDateMessage: mockDueDateMessage,
            severity: mockSeverity,
            firstName: 'John',
            lastName: 'Doe',
            avatarUrl: 'avatar1.jpg',
            approvers: undefined,
            ...mockHandlers,
        });

        // Verify helper functions were called with correct arguments
        expect(getTimeDiff).toHaveBeenCalled();
        expect(getSeverityByDiffInDays).toHaveBeenCalledWith(mockDiffInDays);
        expect(getDueDateMessage).toHaveBeenCalledWith(
            mockDiffInDays,
            mockTask.renewalDate,
        );
        expect(getActionHandlersByType).toHaveBeenCalledWith(
            mockType,
            mockTask,
        );
    });

    test('should handle approvals type correctly', () => {
        // Setup
        const mockTask: UpcomingTaskDetailResponseDto = {
            id: 456,
            name: 'Approval Task',
            renewalDate: '2023-12-31',
            userIds: [1, 2],
            configurationId: 1,
            type: 'CONTROL_APPROVALS',
            completedAt: null,
            schedule: {
                id: 1,
                name: 'Daily',
                type: 'DAILY',
                value: 1,
            },
        };

        const mockType: TaskAccordionType = 'controlApprovals';
        const mockDiffInDays = 5;
        const mockSeverity = 'warning';
        const mockDueDateMessage = 'Due in 5 days';
        const mockHandlers = { onReview: vi.fn() };

        // Mock return values
        vi.mocked(getTimeDiff).mockReturnValue(mockDiffInDays);
        vi.mocked(getSeverityByDiffInDays).mockReturnValue(mockSeverity);
        vi.mocked(getDueDateMessage).mockReturnValue(mockDueDateMessage);
        vi.mocked(getActionHandlersByType).mockReturnValue(mockHandlers);

        // Execute
        const result = mapNotCompletedTaskResponseToCellData(
            mockTask,
            mockUsers,
            mockType,
        );

        // Verify
        expect(result).toStrictEqual({
            id: '456',
            title: 'Approval Task',
            dueDateMessage: mockDueDateMessage,
            severity: mockSeverity,
            firstName: 'John',
            lastName: 'Doe',
            avatarUrl: 'avatar1.jpg',
            approvers: [
                {
                    firstName: 'John',
                    lastName: 'Doe',
                    avatarUrl: 'avatar1.jpg',
                },
                {
                    firstName: 'Jane',
                    lastName: 'Smith',
                    avatarUrl: 'avatar2.jpg',
                },
            ],
            ...mockHandlers,
        });
    });
});
