import { isEmpty } from 'lodash-es';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { CustomTaskYearListResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

/**
 * Converts a list of task year ranges into options for a dropdown.
 *
 * @param tasksYearRange - The list of task year ranges from the API.
 * @returns An array of options formatted for a ListBox component.
 */
export const getTasksYearOptions = (
    tasksYearRange: CustomTaskYearListResponseDto['data'] = [],
): ListBoxItemData[] => {
    if (isEmpty(tasksYearRange)) {
        return [];
    }

    return tasksYearRange.map(([year, count]) => {
        const yearString = String(year);

        return {
            id: yearString,
            label: t`${year} (${count} tasks)`,
            value: yearString,
        };
    });
};
