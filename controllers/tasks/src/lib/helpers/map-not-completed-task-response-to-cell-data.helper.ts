import type {
    UpcomingTaskDetailResponseDto,
    UserResponseDto,
} from '@globals/api-sdk/types';
import { getTimeDiff } from '@helpers/date-time';
import { getSeverityByDiffInDays } from '@helpers/severity';
import type { TaskAccordionType, TaskCellData } from '../types/tasks.types';
import { getActionHandlersByType } from './get-action-handlers-by-type.helper';
import { getDueDateMessage } from './get-due-date-message.helper';

/**
 * Maps a not completed task response to cell data.
 *
 * @param task - The task details.
 * @param users - The list of users.
 * @param type - The task accordion type.
 * @returns The mapped task cell data.
 */
export const mapNotCompletedTaskResponseToCellData = (
    task: UpcomingTaskDetailResponseDto,
    users: UserResponseDto[],
    type: TaskAccordionType | undefined,
): TaskCellData => {
    const { renewalDate, name, id, userIds = [] } = task;
    const todayAtMidnight = new Date();

    todayAtMidnight.setHours(0, 0, 0, 0);
    const diffInDays = getTimeDiff(todayAtMidnight, renewalDate, 'days');

    const severity = getSeverityByDiffInDays(diffInDays);
    const dueDateMessage = getDueDateMessage(diffInDays, renewalDate);

    const usersAssigned = userIds.map((userId) => {
        const matchingUser = users.find((user) => user.id === userId);

        return matchingUser;
    });
    const owner = usersAssigned[0];

    return {
        id: String(id),
        title: name,
        dueDateMessage,
        severity,
        firstName: owner?.firstName,
        lastName: owner?.lastName,
        avatarUrl: owner?.avatarUrl ?? null,
        approvers:
            type === 'policyApprovals' || type === 'controlApprovals'
                ? usersAssigned.map((user) => ({
                      firstName: user?.firstName,
                      lastName: user?.lastName,
                      avatarUrl: user?.avatarUrl ?? null,
                  }))
                : undefined,
        ...getActionHandlersByType(type, task),
    };
};
