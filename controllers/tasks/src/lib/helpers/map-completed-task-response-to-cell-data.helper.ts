import { isNil } from 'lodash-es';
import type {
    UpcomingTaskDetailResponseDto,
    UserResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { formatDate } from '@helpers/date-time';
import type { TaskAccordionType, TaskCellData } from '../types/tasks.types';
import { getActionHandlersByType } from './get-action-handlers-by-type.helper';

/**
 * Maps a completed task response to cell data.
 *
 * @param task - The task details.
 * @param users - The list of users.
 * @param type - The task accordion type.
 * @returns The mapped task cell data.
 */
export const mapCompletedTaskResponseToCellData = (
    task: UpcomingTaskDetailResponseDto,
    users: UserResponseDto[],
    type: TaskAccordionType | undefined,
): TaskCellData => {
    const { completedAt, name, id, userIds = [] } = task;
    let completedMessage = t`Completed`;

    if (!isNil(completedAt)) {
        const formattedDate = formatDate('field', completedAt);

        completedMessage = t`Completed on ${formattedDate}`;
    }

    const usersAssigned = userIds.map((userId) => {
        const matchingUser = users.find((user) => user.id === userId);

        return matchingUser;
    });
    const owner = usersAssigned[0];

    return {
        id: String(id),
        title: name,
        dueDateMessage: completedMessage,
        severity: 'success',
        firstName: owner?.firstName,
        lastName: owner?.lastName,
        avatarUrl: owner?.avatarUrl ?? null,
        approvers:
            type === 'policyApprovals' || type === 'controlApprovals'
                ? usersAssigned.map((user) => ({
                      firstName: user?.firstName,
                      lastName: user?.lastName,
                      avatarUrl: user?.avatarUrl ?? null,
                  }))
                : undefined,
        ...getActionHandlersByType(type, task),
    };
};
