import { t } from '@globals/i18n/macro';
import { formatDate } from '@helpers/date-time';

/**
 * Gets a formatted due date message based on the difference in days
 * and the minimum date.
 *
 * @param diffInDays - The difference in days between today and the due date.
 * @param minDate - The minimum date (optional).
 * @returns A formatted due date message.
 */
export const getDueDateMessage = (
    diffInDays: number,
    minDate?: string | null,
): string => {
    if (!minDate) {
        return '';
    }
    if (diffInDays < 0) {
        const overdue = formatDate('overdue', minDate);

        return minDate ? t`Past due ${overdue}` : '';
    }

    const formattedDate = formatDate('field', minDate);

    return minDate ? t`Review by ${formattedDate}` : '';
};
