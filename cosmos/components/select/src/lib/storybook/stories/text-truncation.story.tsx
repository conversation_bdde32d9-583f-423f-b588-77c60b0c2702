import { Box } from '@cosmos/components/box';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { StoryObj } from '@storybook/react-vite';
import { Select } from '../../select';
import { SAMPLE_ITEMS_WITH_LONG_TEXT } from '../constants';

export default {};
type Story = StoryObj<typeof Select>;

export const TextTruncation: Story = {
    argTypes: {
        defaultValue: { control: false, hideNoControlsWarning: true },
        value: { control: false, hideNoControlsWarning: true },
    },
    args: {
        'aria-describedby': 'helpText-id',
        'aria-labelledby': 'label-id',
        'data-id': 'selectTestId',
        defaultValue: SAMPLE_ITEMS_WITH_LONG_TEXT[1] as ListBoxItemData, // Select the long text option by default
        disabled: false,
        id: 'textTruncationSelect',
        isLoading: false,
        loaderLabel: 'Loading context',
        name: 'cosmos-select-truncation',
        options: SAMPLE_ITEMS_WITH_LONG_TEXT,
        placeholderText: 'Select an option with long text',
        required: false,
    },
    render: (args) => (
        <Box data-id="text-truncation-story-container" maxWidth="300px">
            <Select {...args} />
        </Box>
    ),
};
