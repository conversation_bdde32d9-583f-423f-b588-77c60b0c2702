import { FEEDBACK_TYPE_OPTIONS } from '@cosmos/components/field-feedback';
import type { Meta } from '@storybook/react-vite';
import { Select } from '../select';

const meta: Meta<typeof Select> = {
    tags: ['Stable'],
    title: 'Forms/Select',
    component: Select,
    args: {
        isLoading: false,
        loaderLabel: 'Loading context',
    },
    argTypes: {
        isLoading: {
            control: 'boolean',
        },
        feedbackType: {
            control: 'radio',
            options: [...FEEDBACK_TYPE_OPTIONS, undefined],
        },
        onBlur: {
            control: false,
            action: 'onBlur',
        },
        onChange: {
            control: false,
            action: 'onChange',
        },
        onFocus: {
            control: false,
            action: 'onFocus',
        },
        onKeyDown: {
            control: false,
            action: 'onKeyDown',
        },
    },
};

export default meta;

// Playground should be first and have all controls enabled so they are accessible on Docs page

export { DefaultValue, Loading, TextTruncation } from './stories';
export { Playground } from './stories';
