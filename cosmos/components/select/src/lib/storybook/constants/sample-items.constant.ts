import type { ListBoxItems } from '@cosmos/components/list-box';

export const SAMPLE_ITEMS: ListBoxItems = [
    { id: '7', label: 'Pizza', value: 'pizza' },
    { id: '8', label: 'Burger', value: 'burger' },
    {
        groupHeader: 'Vegetables',
        items: [
            { id: '1', label: 'Carrot', value: 'carrot' },
            { id: '2', label: '<PERSON><PERSON>ccoli', value: 'broccoli' },
            { id: '3', label: 'Tomato', value: 'tomato' },
        ],
    },
    {
        groupHeader: 'Fruit',
        items: [
            { id: '4', label: 'Apple', value: 'apple' },
            { id: '5', label: 'Banana', value: 'banana' },
            { id: '6', label: 'Orange', value: 'orange' },
        ],
    },
];

export const SAMPLE_ITEMS_WITH_LONG_TEXT: ListBoxItems = [
    { id: '1', label: 'Short option', value: 'short' },
    {
        id: '2',
        label: 'This is a very long option text that should be truncated when displayed in the select trigger to prevent overflow issues',
        value: 'long',
    },
    {
        id: '3',
        label: 'Another extremely long option that demonstrates the truncation behavior in select components when the text exceeds the available width',
        value: 'very-long',
    },
    { id: '4', label: 'Normal option', value: 'normal' },
];
