import {
    type AriaAttributes,
    type FocusEvent,
    type FocusEventHandler,
    type ForwardedRef,
    forwardRef,
    type KeyboardEvent,
    type KeyboardEventHandler,
    type <PERSON><PERSON>vent,
    type MouseEventHandler,
} from 'react';
import {
    FEEDBACK_BORDER_COLORS,
    type FeedbackType,
} from '@cosmos/components/field-feedback';
import { Icon } from '@cosmos/components/icon';
import {
    StructuredListItem,
    type StructuredListItemData,
} from '@cosmos/components/list-box';
import { Text } from '@cosmos/components/text';
import { StyledContentWrapperDiv, StyledTriggerDiv } from './styles';

export interface SelectTriggerProps {
    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;
    /**
     * Whether the form control is disabled.
     */
    disabled?: boolean;
    /**
     * Whether the form control is read-only.
     */
    readOnly?: boolean;
    /**
     * The type of feedback given from an external source. Maps to the FieldFeedback component.
     */
    feedbackType?: FeedbackType;
    /**
     * A string used by CSS Grid for grid-template layouts.
     */
    gridArea?: string;
    /**
     * Name of the form control. Submitted with the form as part of a name/value pair.
     */
    name: string;
    /**
     * Function called when input is blurred.
     */
    onBlur?: FocusEventHandler<HTMLElement>;
    onClick?: MouseEventHandler<HTMLElement>;
    /**
     * Function called when input is focused.
     */
    onFocus?: FocusEventHandler<HTMLElement>;
    /**
     * Function called when key down.
     */
    onKeyDown?: KeyboardEventHandler<HTMLElement>;
    /**
     * Text shown when no options are selected.
     */
    placeholderText?: string;
    /**
     * A value is required for the form to be submittable.
     */
    required?: boolean;
    /**
     * Item shown when an option is selected.
     */
    selectedItem?: StructuredListItemData;

    /**
     * From downshift, explicitly redeclared for sake of dependency inversion.
     */
    'aria-activedescendant': AriaAttributes['aria-activedescendant'];
    'aria-controls': AriaAttributes['aria-controls'];
    'aria-expanded': AriaAttributes['aria-expanded'];
    'aria-haspopup': 'listbox';
    'aria-labelledby': AriaAttributes['aria-labelledby'];
    'aria-describedby': AriaAttributes['aria-describedby'];
    id: string;
    role: 'combobox';
    tabIndex: 0;
}
/**
 * An individual option in an option list.  Presentation only.
 */
const BaseSelectTrigger = (
    {
        'aria-activedescendant': ariaActiveDescendant,
        'aria-controls': ariaControls,
        'aria-describedby': ariaDescribedBy,
        'aria-expanded': ariaExpanded,
        'aria-haspopup': ariaHasPopup,
        'aria-labelledby': ariaLabelledBy,
        'data-id': dataId = 'select-trigger',
        disabled = false,
        readOnly = false,
        feedbackType = undefined,
        gridArea = undefined,
        id,
        name,
        onBlur = (e: FocusEvent<HTMLElement>) => e,
        onClick = (e: MouseEvent<HTMLElement>) => e,
        onFocus = (e: FocusEvent<HTMLElement>) => e,
        onKeyDown = (e: KeyboardEvent<HTMLElement>) => e,
        placeholderText = undefined,
        required = false,
        role = 'combobox',
        selectedItem = undefined,
        tabIndex = 0,
    }: SelectTriggerProps,
    ref: ForwardedRef<HTMLDivElement>,
) => {
    const {
        avatar,
        endIconName,
        label: selectedItemLabel,
        startIconName,
        description,
    } = { ...selectedItem };

    const feedbackBorderColor =
        feedbackType && FEEDBACK_BORDER_COLORS[feedbackType];

    return (
        <StyledTriggerDiv
            $feedbackBorderColor={feedbackBorderColor}
            aria-activedescendant={ariaActiveDescendant}
            aria-controls={ariaControls}
            aria-expanded={ariaExpanded}
            aria-describedby={ariaDescribedBy}
            aria-haspopup={ariaHasPopup}
            aria-labelledby={ariaLabelledBy}
            data-id={dataId}
            $gridArea={gridArea}
            disabled={disabled}
            readOnly={readOnly}
            aria-disabled={readOnly}
            id={id}
            name={name}
            ref={ref}
            required={required}
            role={role}
            tabIndex={tabIndex}
            data-testid="BaseSelectTrigger"
            onBlur={onBlur}
            onClick={onClick}
            onFocus={onFocus}
            onKeyDown={onKeyDown}
        >
            <StyledContentWrapperDiv>
                {selectedItemLabel ? (
                    <StructuredListItem
                        hideDescription
                        shouldWrap={false}
                        avatar={avatar}
                        endIconName={endIconName}
                        label={selectedItemLabel}
                        startIconName={startIconName}
                        description={description}
                    />
                ) : (
                    <Text colorScheme="faded" data-id={`${dataId}-placeholder`}>
                        {placeholderText}
                    </Text>
                )}

                <Icon name="ChevronDown" size="200" />
            </StyledContentWrapperDiv>
        </StyledTriggerDiv>
    );
};

export const SelectTrigger = forwardRef(BaseSelectTrigger);
