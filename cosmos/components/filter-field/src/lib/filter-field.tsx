import { isNil } from 'lodash-es';
import type { ChangeEvent } from 'react';
import {
    CheckboxFieldGroup,
    type CheckboxFieldGroupProps,
} from '@cosmos/components/checkbox-field-group';
import type { ComboboxImperativeHandle } from '@cosmos/components/combobox';
import {
    ComboboxField,
    type ComboboxFieldProps,
} from '@cosmos/components/combobox-field';
import {
    DatePickerField,
    type DatePickerFieldProps,
} from '@cosmos/components/date-picker-field';
import {
    FileUploadField,
    type FileUploadFieldProps,
} from '@cosmos/components/file-upload-field';
import type { FormFieldProps } from '@cosmos/components/form-field';
import {
    RadioFieldGroup,
    type RadioFieldGroupProps,
} from '@cosmos/components/radio-field-group';
import {
    SelectField,
    type SelectFieldProps,
} from '@cosmos/components/select-field';
import { SliderField } from '@cosmos/components/slider-field';
import { TextField } from '@cosmos/components/text-field';
import { TextareaField } from '@cosmos/components/textarea-field';
import { ToggleField } from '@cosmos/components/toggle-field';
import type { Filter, FilterState } from './types';

export interface FilterFieldProps {
    'data-id'?: string;
    comboboxResetTriggers?: React.MutableRefObject<Record<string, number>>;
    filter: Filter;
    formId: string;
    imperativeHandleRef?: React.MutableRefObject<ComboboxImperativeHandle | null>;
    onChange: ({ id, value }: FilterState) => void;
    value: FilterState['value'];
}

/**
 * The FilterField component renders different types of form fields based on the filter type for data filtering functionality.
 *
 * 🚧 Needs Figma Link.
 */
export const FilterField = ({
    'data-id': dataId = undefined,
    comboboxResetTriggers = undefined,
    filter,
    formId,
    imperativeHandleRef = undefined,
    onChange,
    value,
}: FilterFieldProps): React.JSX.Element => {
    const labelStyleOverrides: FormFieldProps['labelStyleOverrides'] = {
        size: 'md',
        type: 'title',
    };

    const { filterType, id, label } = filter;

    switch (filterType) {
        case 'checkbox': {
            const { options } = filter;
            const handleChange = (
                selections: CheckboxFieldGroupProps['value'],
            ) => {
                onChange({ id, value: selections });
            };

            return (
                <CheckboxFieldGroup
                    data-id={dataId}
                    formId={formId}
                    name={`${formId}-${id}`}
                    label={label}
                    labelStyleOverrides={labelStyleOverrides}
                    options={options as CheckboxFieldGroupProps['options']}
                    value={value as CheckboxFieldGroupProps['value']}
                    onChange={handleChange}
                />
            );
        }
        case 'combobox': {
            const { options } = filter;
            const handleChange = (
                selections:
                    | ComboboxFieldProps['defaultValue']
                    | ComboboxFieldProps['defaultSelectedOptions'],
            ) => {
                onChange({ id, value: selections });
            };

            const {
                isMultiSelect,
                itemToString,
                getRemoveIndividualSelectedItemClickLabel,
                getSearchEmptyState,
                onFetchOptions,
                placeholder,
                removeAllSelectedItemsLabel = 'Remove all',
                searchDebounce,
                tagGroupColorScheme,
                clearSelectedItemButtonLabel,
                isLoading,
                hasMore,
            } = filter;

            return (
                <ComboboxField
                    key={`${id}-${comboboxResetTriggers?.current[id] || 0}`}
                    data-id={dataId}
                    formId={formId}
                    isLoading={isLoading}
                    hasMore={hasMore}
                    isMultiSelect={isMultiSelect}
                    itemToString={itemToString}
                    label={label}
                    labelStyleOverrides={labelStyleOverrides}
                    loaderLabel="Loading results"
                    name={`${formId}-${id}`}
                    options={options}
                    placeholder={placeholder}
                    removeAllSelectedItemsLabel={removeAllSelectedItemsLabel}
                    searchDebounce={searchDebounce}
                    tagGroupColorScheme={tagGroupColorScheme}
                    clearSelectedItemButtonLabel={clearSelectedItemButtonLabel}
                    getSearchEmptyState={getSearchEmptyState}
                    imperativeHandleRef={imperativeHandleRef}
                    getRemoveIndividualSelectedItemClickLabel={({
                        itemLabel,
                    }) => {
                        return (
                            getRemoveIndividualSelectedItemClickLabel?.({
                                itemLabel,
                            }) || `Remove ${itemLabel}`
                        );
                    }}
                    onChange={handleChange}
                    onFetchOptions={onFetchOptions}
                    {...(isMultiSelect
                        ? {
                              defaultSelectedOptions:
                                  value as ComboboxFieldProps['defaultSelectedOptions'],
                          }
                        : {
                              defaultValue:
                                  value as ComboboxFieldProps['defaultValue'],
                          })}
                />
            );
        }
        case 'radio': {
            const { options } = filter;
            const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
                onChange({ id, value: event.target.value });
            };

            return (
                <RadioFieldGroup
                    data-id={dataId}
                    formId={formId}
                    name={`${formId}-${id}`}
                    label={label}
                    labelStyleOverrides={labelStyleOverrides}
                    options={options as RadioFieldGroupProps['options']}
                    value={value as RadioFieldGroupProps['value']}
                    onChange={handleChange}
                />
            );
        }
        case 'select': {
            const { options, placeholder } = filter;
            const handleChange = (selectedItem: SelectFieldProps['value']) => {
                onChange({ id, value: selectedItem });
            };

            return (
                <SelectField
                    data-id={dataId}
                    formId={formId}
                    name={`${formId}-${id}`}
                    label={label}
                    placeholder={placeholder}
                    labelStyleOverrides={labelStyleOverrides}
                    loaderLabel="Loading options"
                    options={options}
                    value={value as SelectFieldProps['value']}
                    onChange={handleChange}
                />
            );
        }
        case 'slider': {
            const {
                min,
                max,
                step,
                defaultValue,
                inputLabels = ['Minimum', 'Maximum'],
            } = filter;
            const handleChange = (values: number[]) => {
                onChange({ id, value: values });
            };

            return (
                <SliderField
                    formId={formId}
                    label={label}
                    labelStyleOverrides={labelStyleOverrides}
                    name={`${formId}-${id}`}
                    data-id="cosmos-slider-field"
                    defaultValue={defaultValue as number[]}
                    value={value as number[]}
                    inputLabels={inputLabels}
                    min={min}
                    max={max}
                    step={step}
                    onCommit={handleChange}
                    onInputChange={handleChange}
                />
            );
        }
        case 'date': {
            const {
                locale,
                monthSelectionFieldLabel,
                yearSelectionFieldLabel,
                calendarPlacement,
                defaultValue,
                dateUnavailableText,
                getIsDateUnavailable,
            } = filter;

            const handleChange = (date: DatePickerFieldProps['value']) => {
                onChange({ id, value: date });
            };

            const dateUnavailableProps =
                !isNil(dateUnavailableText) && !isNil(getIsDateUnavailable)
                    ? {
                          dateUnavailableText,
                          getIsDateUnavailable,
                      }
                    : {};

            return (
                <DatePickerField
                    formId={formId}
                    label={label}
                    locale={locale}
                    monthSelectionFieldLabel={monthSelectionFieldLabel}
                    name={`${formId}-${id}`}
                    yearSelectionFieldLabel={yearSelectionFieldLabel}
                    calendarPlacement={calendarPlacement}
                    data-id={dataId}
                    defaultValue={defaultValue}
                    labelStyleOverrides={labelStyleOverrides}
                    value={value as DatePickerFieldProps['value']}
                    onChange={handleChange}
                    {...dateUnavailableProps}
                />
            );
        }
        case 'file': {
            const {
                acceptedFormats,
                errorCodeMessages,
                innerLabel,
                isMulti,
                maxFileSizeInBytes,
                removeButtonText,
                selectButtonText,
                showDropzone,
                showFileList,
            } = filter;

            const handleChange = (
                ...args: Parameters<
                    NonNullable<FileUploadFieldProps['onUpdate']>
                >
            ) => {
                onChange({ id, value: args });
            };

            return (
                <FileUploadField
                    acceptedFormats={acceptedFormats}
                    data-id={dataId}
                    errorCodeMessages={errorCodeMessages}
                    formId={formId}
                    innerLabel={innerLabel}
                    isMulti={isMulti}
                    label={label}
                    labelStyleOverrides={labelStyleOverrides}
                    maxFileSizeInBytes={maxFileSizeInBytes}
                    name={`${formId}-${id}`}
                    removeButtonText={removeButtonText}
                    selectButtonText={selectButtonText}
                    showDropzone={showDropzone}
                    showFileList={showFileList}
                    onUpdate={handleChange}
                />
            );
        }
        case 'text': {
            const { onBlur, onFocus, onKeyDown } = filter;

            const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
                onChange({ id, value: event.target.value });
            };

            return (
                <TextField
                    data-id={dataId}
                    formId={formId}
                    label={label}
                    labelStyleOverrides={labelStyleOverrides}
                    name={`${formId}-${id}`}
                    value={value as string}
                    onChange={handleChange}
                    onBlur={onBlur}
                    onFocus={onFocus}
                    onKeyDown={onKeyDown}
                />
            );
        }
        case 'textarea': {
            const { maxCharacters, rows, onClick, onBlur, onFocus, onKeyDown } =
                filter;

            const handleChange = (event: ChangeEvent<HTMLTextAreaElement>) => {
                onChange({ id, value: event.target.value });
            };

            return (
                <TextareaField
                    data-id={dataId}
                    formId={formId}
                    label={label}
                    labelStyleOverrides={labelStyleOverrides}
                    name={`${formId}-${id}`}
                    maxCharacters={maxCharacters}
                    rows={rows}
                    value={value as string}
                    onChange={handleChange}
                    onClick={onClick}
                    onBlur={onBlur}
                    onFocus={onFocus}
                    onKeyDown={onKeyDown}
                />
            );
        }
        case 'toggle': {
            const { checked, defaultChecked, layout } = filter;

            const handleChange = (isChecked: boolean) => {
                onChange({ id, value: isChecked });
            };

            return (
                <ToggleField
                    checked={checked}
                    defaultChecked={defaultChecked}
                    data-id={dataId}
                    formId={formId}
                    label={label}
                    layout={layout}
                    name={`${formId}-${id}`}
                    value={value as string}
                    onChange={handleChange}
                />
            );
        }
        default: {
            return <>{null}</>;
        }
    }
};
