import { isEmpty } from 'lodash-es';
import { useState } from 'react';
import { Button } from '@cosmos/components/button';
import type {
    Filter,
    FilterState,
    FilterStateValue,
} from '@cosmos/components/filter-field';
import { ResetContainer } from './components';
import { ViewModeToggle } from './components/ViewModeToggle';
import {
    CLEAR_ALL_BUTTON_TEST_ID,
    DEFAULT_TEST_ID,
    FILTER_FORM_WRAPPER_ID,
} from './constants';
import {
    StyledFilterContentWrapperDiv,
    StyledRowDiv,
    StyledWrapperDiv,
} from './styles';
import type { LimitedViewModeToggleProps } from './types';

/* NOTE: Updating this type? Make sure to update the docs:
 * libs/filters/storybook/constants/arg-types.constant.ts */
export interface BaseFilterFormProps<
    TViewModeToggle extends boolean | undefined,
> {
    /**
     * The label text displayed on the "Clear All" button. This text should clearly indicate the action of clearing or resetting all selected filters.
     */
    clearAllButtonLabel: string;
    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;
    /**
     * An array of filter objects that define the available filtering options. Each filter object specifies the type of filter and its associated properties, allowing users to narrow down or refine their selections.
     */
    filters: Filter[];
    /**
     * The values of the filters, most likely derived from external state.
     */
    filterValues: Record<Filter['id'], FilterStateValue>;
    /**
     * A unique identifier for this form.
     */
    formId: string;
    /**
     * The callback that runs when a filter is changed.
     */
    onChange: (res: FilterState) => void;
    /**
     * The callback that runs when the "Clear all" button is clicked.
     */
    onClearAll: (event: React.MouseEvent<HTMLButtonElement>) => void;
    /**
     * If `true`, then the view mode (pinned/unpinned) toggle group will be shown. Must be used with `viewModeToggleProps`.
     */
    showViewModeToggle?: TViewModeToggle;
    /**
     * Props for the view mode toggle group.  If `showViewModeToggle` is `true`, `viewModeToggleProps` must be provided.
     */
    viewModeToggleProps?: TViewModeToggle extends true
        ? LimitedViewModeToggleProps
        : never;
    /**
     * Optional reset triggers for combobox filters to force re-mounting when clearing.
     */
    comboboxResetTriggers?: React.MutableRefObject<Record<string, number>>;
}

export type FilterFormProps = BaseFilterFormProps<boolean | undefined>;

/**
 * The FilterForm component provides a form interface for managing multiple filters with clear all functionality.
 *
 * 🚧 Needs Figma Link.
 */
export const FilterForm = ({
    clearAllButtonLabel,
    'data-id': dataId = DEFAULT_TEST_ID,
    filters,
    filterValues,
    onChange,
    onClearAll,
    formId,
    showViewModeToggle = false,
    viewModeToggleProps = undefined,
    comboboxResetTriggers = undefined,
}: Readonly<FilterFormProps>): React.JSX.Element => {
    const [resetKey, setResetKey] = useState<number>(0);

    const handleClearAll = (event: React.MouseEvent<HTMLButtonElement>) => {
        event.preventDefault();
        onClearAll(event);
        setResetKey(resetKey + 1);
    };

    const resetButtonLabel = isEmpty(clearAllButtonLabel)
        ? 'Reset'
        : clearAllButtonLabel;

    return (
        <StyledWrapperDiv
            id={`${formId}-${FILTER_FORM_WRAPPER_ID}`}
            data-id={dataId}
            data-testid="FilterForm"
        >
            <StyledFilterContentWrapperDiv>
                <StyledRowDiv>
                    <Button
                        colorScheme="neutral"
                        data-id={`${dataId}-${CLEAR_ALL_BUTTON_TEST_ID}`}
                        label={resetButtonLabel}
                        level="secondary"
                        onClick={handleClearAll}
                    />
                    {showViewModeToggle && viewModeToggleProps ? (
                        <ViewModeToggle
                            data-id={`${dataId}-viewModeToggle`}
                            {...viewModeToggleProps}
                            id={`${formId}-viewModeToggle`}
                        />
                    ) : null}
                </StyledRowDiv>
                <ResetContainer
                    key={`resetKey-${resetKey}`}
                    data-id={dataId}
                    filters={filters}
                    formId={formId}
                    filterValues={filterValues}
                    comboboxResetTriggers={comboboxResetTriggers}
                    onChange={onChange}
                />
            </StyledFilterContentWrapperDiv>
        </StyledWrapperDiv>
    );
};
