import { isEmpty } from 'lodash-es';
import { useEffect, useRef } from 'react';
import {
    type Filter,
    FilterField,
    type FilterState,
} from '@cosmos/components/filter-field';
import { DEFAULT_RESET_TEST_ID } from '../constants';

export interface ResetContainerProps {
    'data-id'?: string;
    filters: Filter[];
    filterValues: Record<Filter['id'], FilterState['value']>;
    formId: string;
    onChange: ({ id, value }: FilterState) => void;
    comboboxResetTriggers?: React.MutableRefObject<Record<string, number>>;
}

export const ResetContainer = ({
    'data-id': dataId = DEFAULT_RESET_TEST_ID,
    filters,
    filterValues,
    formId,
    onChange,
    comboboxResetTriggers = undefined,
}: ResetContainerProps): React.JSX.Element => {
    // Track previous values to detect when combobox values are cleared
    const prevFilterValues = useRef<Record<string, FilterState['value']>>({});

    /**
     * Update reset triggers when combobox values change from selected to cleared state.
     *
     * This effect is necessary because combobox components need to be remounted when their
     * value transitions from a selected state to cleared state to properly clear internal state.
     * For single-select: cleared state is undefined
     * For multi-select: cleared state is empty array [].
     *
     * The effect mutates the comboboxResetTriggers ref which is used as a key prop in FilterField
     * to force remounting of ComboboxField components. This is an intentional side effect that
     * cannot be avoided without breaking the combobox reset functionality.
     *
     * Alternative approaches like lifting this logic to parent components would require
     * significant architectural changes and would not have access to the previous values
     * needed to detect the transition from selected to cleared state.
     */
    useEffect(() => {
        // Early return if no reset triggers provided
        if (!comboboxResetTriggers) {
            return;
        }

        // Process each filter to detect value changes
        for (const filter of filters) {
            // Only process combobox filters
            if (filter.filterType !== 'combobox') {
                continue;
            }

            const currentValue = filterValues[filter.id];
            const prevValue = prevFilterValues.current[filter.id];

            // Detect transition from selected to cleared state (filter cleared)
            // Single-select: something → undefined
            // Multi-select: non-empty array → empty array
            const wasSelected = filter.isMultiSelect
                ? Array.isArray(prevValue) && !isEmpty(prevValue)
                : prevValue !== undefined;

            const isNowCleared = filter.isMultiSelect
                ? Array.isArray(currentValue) && isEmpty(currentValue)
                : currentValue === undefined;

            if (wasSelected && isNowCleared) {
                // Increment reset trigger to force component remount
                // This is necessary because combobox internal state doesn't reset properly
                // when value changes from selected state to cleared state
                comboboxResetTriggers.current[filter.id] =
                    (comboboxResetTriggers.current[filter.id] || 0) + 1;
            }

            // Update previous value for next comparison
            prevFilterValues.current[filter.id] = currentValue;
        }
    }, [filters, filterValues, comboboxResetTriggers]);

    return (
        <>
            {filters.map((filter) => {
                const { id } = filter;
                const value = filterValues[id];

                return (
                    <FilterField
                        key={id}
                        data-id={`${dataId}-${id}Field`}
                        formId={formId}
                        filter={filter}
                        value={value}
                        comboboxResetTriggers={comboboxResetTriggers}
                        onChange={onChange}
                    />
                );
            })}
        </>
    );
};
