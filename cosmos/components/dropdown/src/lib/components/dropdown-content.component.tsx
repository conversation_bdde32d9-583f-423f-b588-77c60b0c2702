import {
    Content as RadixDropdownContent,
    type DropdownMenuContentProps as RadixDropdownMenuContentProps,
    type DropdownMenuPortalProps as RadixDropdownMenuPortalProps,
    Portal as RadixDropdownPortal,
} from '@radix-ui/react-dropdown-menu';
import { SIDE_OFFSET } from '../constants/side-offset.constant';
import styles from '../dropdown.module.css';

export interface DropdownContentProps
    extends Omit<
            RadixDropdownMenuContentProps,
            'loop' | 'asChild' | 'sideOffset' | 'arrowPadding'
        >,
        Omit<RadixDropdownMenuPortalProps, 'forceMount' | 'container'> {
    'data-id'?: string;
    children: React.ReactNode;
    forceMountPortal?: RadixDropdownMenuPortalProps['forceMount'];
    portalContainer?: RadixDropdownMenuPortalProps['container'];
}

export const DropdownContent = ({
    'data-id': dataId,
    align = 'start',
    children,
    side = 'bottom',
    ...restProps
}: DropdownContentProps): JSX.Element => {
    return (
        <RadixDropdownPortal
            data-testid="DropdownContent"
            data-id={`${dataId}-portal`}
        >
            <RadixDropdownContent
                asChild
                loop
                align={align}
                className={styles.content}
                data-id={dataId}
                data-testid="DropdownContent"
                side={side}
                sideOffset={SIDE_OFFSET}
                {...restProps}
            >
                <ul
                    className={styles['list-box']}
                    data-id={`${dataId}-listBox`}
                    data-testid="DropdownContentListBox"
                >
                    {children}
                </ul>
            </RadixDropdownContent>
        </RadixDropdownPortal>
    );
};
