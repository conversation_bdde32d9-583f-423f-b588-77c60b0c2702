import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { Meta } from '@storybook/react-vite';
import { ComboboxField } from '../combobox-field';
import { ARG_TYPES, SAMPLE_ITEMS } from './constants';

const meta: Meta<typeof ComboboxField> = {
    tags: ['Stable'],
    title: 'Forms/ComboboxField',
    component: ComboboxField,
    argTypes: ARG_TYPES as unknown as Meta['argTypes'],
    args: {
        'data-id': 'selectTestId',
        defaultSelectedOptions: undefined,
        defaultValue: undefined,
        disabled: false,
        feedback: undefined,
        formId: 'options',
        getRemoveIndividualSelectedItemClickLabel: undefined,
        helpText: 'Choose an option',
        isMultiSelect: false,
        label: 'Options',
        labelStyleOverrides: { size: undefined, type: undefined },
        loaderLabel: 'Loading results',
        name: 'cosmos-combobox',
        onBlur: undefined,
        onChange: (value: ListBoxItemData | undefined) => {
            // eslint-disable-next-line no-console -- need this
            console.log('onChange', value);
        },
        onFetchOptions: undefined,

        onFocus: undefined,
        onKeyDown: undefined,
        optionalText: 'optional',
        options: SAMPLE_ITEMS,
        placeholder: undefined,
        removeAllSelectedItemsLabel: undefined,
        required: false,
        searchDebounce: undefined,
        readOnly: false,
        clearSelectedItemButtonLabel: 'Clear',
    },
};

export default meta;

// Playground should be first and have all controls enabled so they are accessible on Docs page

export { DefaultValue, MultiSelect, Playground } from './stories';
