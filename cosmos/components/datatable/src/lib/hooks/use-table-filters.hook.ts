import { isEmpty, isNil } from 'lodash-es';
import { useMemo, useRef } from 'react';
import type { FilterState } from '@cosmos/components/filter-field';
import { t } from '@globals/i18n/macro';
// eslint-disable-next-line no-restricted-imports -- Only allowed in Datatable
import type { RowData, Table } from '@tanstack/react-table';
import { getActiveFilterLabel } from '../helpers/get-active-filter-label/get-active-filter-label.helper';
import type { ActiveFilter } from '../types/active-filter.type';
import type { DatatableTableMeta } from '../types/datatable-table-meta.type';
import type { GlobalFilterState } from '../types/global-filter-state.type';
import type { SupportedFilterTypes } from '../types/supported-filter-types.type';

// Utility type that makes K required but other properties optional
type RequireOnly<T, K extends keyof T> = Pick<T, K> & Partial<Omit<T, K>>;

// Define PartialTableMeta for the `meta` object
type PartialTableMeta<TData> = RequireOnly<
    DatatableTableMeta<TData>,
    'tableId' | 'filterProps' | 'filterViewModeProps'
>;

// Define PartialTableOptions for the `options` object
type PartialTableOptions<TData> = Partial<
    Omit<Table<TData>['options'], 'meta'>
> & {
    meta?: PartialTableMeta<TData>;
};

// Define PartialTable that requires only certain properties
export type PartialTable<TData> = RequireOnly<
    Omit<Table<TData>, 'options'>,
    'getState' | 'setGlobalFilter'
> & {
    options: PartialTableOptions<TData>;
};

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types -- need this
export function useTableFilters<TData extends RowData>(
    table: PartialTable<TData>,
) {
    const {
        getState,
        setGlobalFilter,
        options: {
            meta: {
                tableId = '',
                filterProps: {
                    clearAllButtonLabel = '',
                    filters: filterConfigs = [],
                    triggerLabel = t`Filter`,
                } = {},
                filterViewModeProps = {
                    viewMode: 'toggleable',
                    props: undefined,
                },
            } = {},
        } = {},
    } = table;

    const {
        viewMode,
        props: viewModeProps = {
            togglePinnedLabel: 'Pin filters to left of table',
            toggleUnpinnedLabel: 'Move filters to collapsible menu',
        },
    } = filterViewModeProps;

    const {
        globalFilter: { filters: currentFilters },
    }: { globalFilter: GlobalFilterState } = getState();

    const showViewModeToggle = viewMode === 'toggleable';

    const handleClearAll = (event: React.MouseEvent<HTMLButtonElement>) => {
        event.preventDefault();
        setGlobalFilter({ type: 'RESET_FILTERS' });
    };

    const handleChange = (res: FilterState) => {
        setGlobalFilter({
            type: 'UPDATE_FILTERS',
            payload: res,
        });
    };

    const activeFilters: ActiveFilter[] = useMemo(
        () =>
            Object.entries(currentFilters)
                .filter(([, filter]) => !isNil(filter.value))
                .map(([id, filter]) => {
                    const matchingFilter = filterConfigs.find(
                        (filterItem) => filterItem.id === id,
                    );

                    const displayText = matchingFilter
                        ? getActiveFilterLabel(filter.value, matchingFilter)
                        : '';

                    return {
                        id,
                        label: displayText,
                        filterType: filter.filterType as SupportedFilterTypes,
                    };
                })
                .filter(({ label }) => !isEmpty(label)),
        [currentFilters, filterConfigs],
    );

    const filterValues: Record<string, FilterState['value']> = useMemo(
        () =>
            Object.fromEntries(
                Object.entries(currentFilters).map(([key, value]) => [
                    key,
                    value.value,
                ]),
            ),
        [currentFilters],
    );

    // Track reset triggers for combobox filters to force re-mounting
    const comboboxResetTriggers = useRef<Record<string, number>>({});

    const handleRemoveFilter = (id: string) => {
        // Increment reset trigger for combobox filters to force re-mounting

        // Find the filter configuration to determine the appropriate clear value
        const filterConfig = filterConfigs.find((filter) => filter.id === id);
        let clearValue: FilterState['value'] = undefined;

        // For combobox filters, use appropriate clear value based on multi-select
        if (filterConfig?.filterType === 'combobox') {
            comboboxResetTriggers.current[id] =
                (comboboxResetTriggers.current[id] || 0) + 1;
            clearValue = filterConfig.isMultiSelect ? [] : undefined;
        }

        handleChange({ id, value: clearValue });
    };

    return {
        activeFilters,
        activeFiltersCount: activeFilters.length,
        clearAllButtonLabel,
        filters: filterConfigs,
        filterValues,
        showViewModeToggle,
        tableId,
        triggerLabel,
        viewModeProps,
        comboboxResetTriggers,
        handleChange,
        handleClearAll,
        handleRemoveFilter,
    };
}
