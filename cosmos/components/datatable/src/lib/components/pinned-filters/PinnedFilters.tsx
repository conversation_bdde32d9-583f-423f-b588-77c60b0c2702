import { styled } from 'styled-components';
import {
    FilterForm,
    type FilterFormProps,
} from '@cosmos/components/filter-form';
import {
    borderRadiusLg,
    borderWidthSm,
    neutralBackgroundSurfaceInitial,
    neutralBorderFaded,
} from '@cosmos/constants/tokens';
// eslint-disable-next-line no-restricted-imports -- Only allowed in Datatable
import type { RowData, Table } from '@tanstack/react-table';
import { useFiltersViewModeContext } from '../../context/useFiltersViewModeContext';
import { useTableFilters } from '../../hooks/use-table-filters.hook';

const StyledPinnedFilterWrapperDiv = styled.div`
    background-color: ${neutralBackgroundSurfaceInitial};
    border: ${borderWidthSm} solid ${neutralBorderFaded};
    border-radius: ${borderRadiusLg};
    width: fit-content;
`;

export interface PinnedFiltersProps<TData extends RowData> {
    'data-id'?: string;
    table: Table<TData>;
}

export const PinnedFilters = <TData extends RowData>({
    'data-id': dataId = undefined,
    table,
}: Readonly<PinnedFiltersProps<TData>>): React.JSX.Element | null => {
    const {
        clearAllButtonLabel,
        comboboxResetTriggers,
        filters,
        filterValues,
        handleChange,
        handleClearAll,
        showViewModeToggle,
        tableId,
        viewModeProps,
    } = useTableFilters(table);

    const {
        handleToggleViewMode,
        resetKeyPinnedFilters,
        selectedOption,
        areFiltersPinnedOpen,
    } = useFiltersViewModeContext();

    const { filterProps } = table.getMeta();

    /**
     * NOTE:
     * showPinnedFilters logic was moved from TableContent to reduce complexity and simplify setup,
     * as PinnedFilters is exported for custom solutions and requires
     * useFiltersViewModeContext to be used within the filter context provider.
     */
    const showPinnedFilters = areFiltersPinnedOpen && filterProps;

    const updatedViewModeToggleProps = {
        ...viewModeProps,
        onChange: handleToggleViewMode,
        initialSelectedOption: undefined,
        selectedOption,
    } satisfies FilterFormProps['viewModeToggleProps'];

    return showPinnedFilters ? (
        <StyledPinnedFilterWrapperDiv
            data-id={dataId}
            key={`resetKey-${resetKeyPinnedFilters}`}
            data-testid="PinnedFilters"
        >
            <FilterForm
                data-id={`${dataId}-form`}
                clearAllButtonLabel={clearAllButtonLabel}
                filters={filters}
                filterValues={filterValues}
                formId={tableId}
                showViewModeToggle={showViewModeToggle}
                viewModeToggleProps={updatedViewModeToggleProps}
                comboboxResetTriggers={comboboxResetTriggers}
                onClearAll={handleClearAll}
                onChange={handleChange}
            />
        </StyledPinnedFilterWrapperDiv>
    ) : null;
};
