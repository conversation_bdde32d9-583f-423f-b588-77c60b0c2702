import type { FilterFormProps } from '@cosmos/components/filter-form';
import { Filters } from '@cosmos/components/filters';
// eslint-disable-next-line no-restricted-imports -- Only allowed in Datatable
import type { RowData, Table } from '@tanstack/react-table';
import { useFiltersViewModeContext } from '../../context/useFiltersViewModeContext';
import { useTableFilters } from '../../hooks/use-table-filters.hook';

export interface UnpinnedFiltersProps<TData extends RowData> {
    'data-id'?: string;
    table: Table<TData>;
}

export const UnpinnedFilters = <TData extends RowData>({
    'data-id': dataId = undefined,
    table,
}: Readonly<UnpinnedFiltersProps<TData>>): React.JSX.Element => {
    const {
        clearAllButtonLabel,
        comboboxResetTriggers,
        filters,
        filterValues,
        handleChange,
        handleClearAll,
        showViewModeToggle,
        tableId,
        triggerLabel,
        viewModeProps,
    } = useTableFilters(table);

    const { handleToggleViewMode, selectedOption, initialIsPopoverOpen } =
        useFiltersViewModeContext();

    const updatedViewModeToggleProps = {
        ...viewModeProps,
        onChange: handleToggleViewMode,
        initialSelectedOption: undefined,
        selectedOption,
    } satisfies FilterFormProps['viewModeToggleProps'];

    return (
        <Filters
            data-id={`${dataId}-form`}
            clearAllButtonLabel={clearAllButtonLabel}
            filters={filters}
            filterValues={filterValues}
            initialIsOpen={initialIsPopoverOpen}
            formId={tableId}
            showViewModeToggle={showViewModeToggle}
            triggerLabel={triggerLabel}
            triggerId={`${tableId}-unpinnedFiltersTrigger`}
            viewModeToggleProps={updatedViewModeToggleProps}
            comboboxResetTriggers={comboboxResetTriggers}
            data-testid="UnpinnedFilters"
            onClearAll={handleClearAll}
            onChange={handleChange}
        />
    );
};
