/* eslint-disable no-negated-condition -- Fix in ENG-67743 */
/* eslint-disable @typescript-eslint/no-unnecessary-condition -- Fix in ENG-67743 */
/* eslint-disable @regru/prefer-early-return/prefer-early-return -- Fix in ENG-67743 */
import {
    useCombobox,
    type UseComboboxInputValueChange,
    type UseComboboxSelectedItemChange,
} from 'downshift';
import { debounce, isEmpty } from 'lodash-es';
import {
    type FocusEvent,
    type KeyboardEvent,
    useCallback,
    useEffect,
    useRef,
    useState,
} from 'react';
import { flushSync } from 'react-dom';
import {
    ListBox,
    type ListBoxItemData,
    type StructuredListItemData,
} from '@cosmos/components/list-box';
import { Metadata } from '@cosmos/components/metadata';
import { Popover } from '@cosmos/components/popover';
import { TagGroup } from '@cosmos/components/tag-group';
import { useDownshiftOptions } from '@cosmos/hooks/use-downshift-options';
import { useFlatOptions } from '@cosmos/hooks/use-flat-options';
import { useMatchWidth } from '@cosmos/hooks/use-match-width';
import { flip, shift, size } from '@floating-ui/react-dom';
import { ComboboxTrigger } from './components';
import { ComboboxFilterEmptyState } from './components/combobox-filter-empty-state.component';
import {
    DEFAULT_ITEM_IDENTIFIER,
    DEFAULT_SEARCH_DEBOUNCE,
    MENU_MIN_WIDTH,
} from './constants';
import {
    createElementIds,
    defaultItemToString,
    multiSelectStateReducer,
    singleSelectStateReducer,
} from './helpers';
import { useComboboxImperativeHandle } from './hooks/use-combobox-imperative-handle.hook';
import { StyledHeightWrapperDiv } from './styles';
import { StyledSpan } from './styles/StyledSpan.style';
import type {
    ComboboxProps,
    ComboboxStandardConfig,
    MutuallyExclusiveComboboxConfig,
} from './types';

/**
 * A form input which allows users to select from a short list of options.
 *
 * 🚧 Needs Figma Link.
 */
export const Combobox = ({
    imperativeHandleRef,
    'aria-describedby': ariaDescribedBy = undefined,
    'aria-labelledby': ariaLabelledBy = undefined,
    'data-id': dataId = 'cosmos-combobox',
    clearSelectedItemButtonLabel = undefined,
    defaultSelectedOptions = [],
    defaultValue = undefined,
    disabled = false,
    feedbackType = undefined,
    getRemoveIndividualSelectedItemClickLabel,
    getSearchEmptyState,
    gridArea = undefined,
    hasMore = false,
    id,
    isLoading = false,
    isMultiSelect = false,
    itemIdentifier = DEFAULT_ITEM_IDENTIFIER,
    itemToString = defaultItemToString,
    loaderLabel,
    name,
    onBlur = (e: FocusEvent<HTMLElement>) => e,
    onChange,
    onFetchOptions = undefined,
    onFocus = (e: FocusEvent<HTMLElement>) => e,
    onKeyDown = (e: KeyboardEvent<HTMLElement>) => e,
    options = [],
    placeholderText = undefined,
    readOnly = false,
    removeAllSelectedItemsLabel,
    required = false,
    searchDebounce = DEFAULT_SEARCH_DEBOUNCE,
    tagGroupColorScheme = 'primary',
}: ComboboxProps): React.JSX.Element => {
    const [selectedItems, setSelectedItems] = useState<ListBoxItemData[]>(
        defaultSelectedOptions,
    );
    const [popoverElement, setPopoverElement] = useState<HTMLDivElement | null>(
        null,
    );
    const [focusIndex, setFocusIndex] = useState<number | null>(null);

    const isFirstRender = useRef(true);
    const triggerRef = useRef(null);
    const inputRef = useRef<HTMLInputElement>(null);
    const listBoxRef = useRef<HTMLUListElement>(null);
    const itemRefs = useRef<(HTMLButtonElement | HTMLDivElement | null)[]>([]);

    const handlePopoverRef = useCallback((element: HTMLDivElement | null) => {
        setPopoverElement(element);
    }, []);

    const [maxHeight, setMaxHeight] = useState<number | undefined>(undefined);
    const MIDDLEWARE_PADDING = 24;

    const middleware = [
        flip(),
        shift(),
        size({
            apply({ availableHeight }) {
                flushSync(() => {
                    setMaxHeight(availableHeight - MIDDLEWARE_PADDING);
                });
            },
            rootBoundary: 'viewport',
            padding: MIDDLEWARE_PADDING,
        }),
    ];

    /*
     * Downshift tracks items by index, and it cannot assign an index
     * to nested items.
     */
    const flatOptions = useFlatOptions({ options });

    /**
     * TODO: Think about how multiselect changes the return value of onChange
     * Should we always return an array of objects?
     */
    const handleSingleSelectedItemChange = useCallback(
        (changes: UseComboboxSelectedItemChange<ListBoxItemData>) => {
            if (changes.selectedItem) {
                (onChange as (value: ListBoxItemData | undefined) => void)(
                    changes.selectedItem,
                );
            }
        },
        [onChange],
    );

    /**
     * Either add or remove the selected item from selectedItems.
     */
    const handleMultiSelectedItemChange = useCallback(
        (changes: UseComboboxSelectedItemChange<ListBoxItemData>) => {
            const { selectedItem: itemToChange } = changes;

            if (itemToChange) {
                const index = selectedItems.findIndex(
                    (item) => item.id === itemToChange.id,
                );

                switch (true) {
                    /**
                     * If item exists and is not the first index:
                     * Slice around the items index and re-add remaining items.
                     */
                    case index > 0: {
                        setSelectedItems((prevSelectedItems) => {
                            return [
                                ...prevSelectedItems.slice(0, index),
                                ...prevSelectedItems.slice(index + 1),
                            ];
                        });

                        break;
                    }

                    /**
                     * If item exists and IS the first index:
                     * Slice out the first index and re-add the rest.
                     */
                    case index === 0: {
                        setSelectedItems((prevSelectedItems) =>
                            prevSelectedItems.slice(1),
                        );

                        break;
                    }

                    /**
                     * Item does not exist, add it.
                     */
                    default: {
                        setSelectedItems((prevSelectedItems) => [
                            ...prevSelectedItems,
                            itemToChange,
                        ]);

                        break;
                    }
                }
            }
        },
        [selectedItems],
    );

    // eslint-disable-next-line react-you-might-not-need-an-effect/you-might-not-need-an-effect -- TODO: this is correct and needs fixing
    useEffect(() => {
        /**
         * Prevent onChange from being called when the component mounts.
         */
        if (isFirstRender.current) {
            isFirstRender.current = false;

            return;
        }

        /**
         * Note:
         * For MultiSelect only.
         *
         * We keep multi selected items in our own state.
         *
         * In order to prevent off-by-one state updates, state setter callbacks are used.
         * This prevents us from calling onChange imperatively inside of handleMultiSelectedItemChange.
         */
        if (isMultiSelect) {
            (onChange as (value: ListBoxItemData[]) => void)(selectedItems);
        }
        /* eslint-disable-next-line react-hooks/exhaustive-deps --
         * TODO: [ENG-48578]
         * This is intended to be a quick fix because formik useField helpers are not memoized
         * onChange is excluded from the dependency array
         * add it back when we update formik to the latest version
         */
    }, [isMultiSelect, selectedItems]);

    useEffect(() => {
        if (focusIndex !== null) {
            if (focusIndex >= 0) {
                const nextRef = itemRefs.current[focusIndex];

                if (nextRef instanceof HTMLButtonElement) {
                    nextRef.focus();
                }
            } else {
                inputRef.current?.focus();
            }
            setFocusIndex(null);
        }
    }, [focusIndex]);

    /**
     * TODO:
     * Consider how handleRemoveSelectedItem can be combined with the logic from handleMultiSelectedItemChange.
     */
    const handleRemoveSelectedItem = (item: ListBoxItemData) => {
        if (isMultiSelect && item) {
            const index = selectedItems.indexOf(item);

            setSelectedItems((prevSelectedItems) => {
                const newItems = [
                    ...prevSelectedItems.slice(0, index),
                    ...prevSelectedItems.slice(index + 1),
                ];

                if (!isEmpty(newItems)) {
                    const nextIndex =
                        index >= newItems.length ? newItems.length - 1 : index;

                    setFocusIndex(nextIndex);
                } else {
                    setFocusIndex(-1);
                }

                return newItems;
            });
        }
    };

    const handleInputValueChange = useCallback(
        (changes: UseComboboxInputValueChange<ListBoxItemData>) => {
            onFetchOptions?.({
                search: changes.inputValue.trim(),
            });
        },
        [onFetchOptions],
    );

    /* eslint-disable-next-line react-hooks/exhaustive-deps --
     *
     * Why we disable the "exhaustive-deps" rule:
     * - React says "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead"
     * - BUT "debounce" is an external library and does not contain any dependencies that useCallback cares about
     *
     * Why we need "useCallback":
     * - "useCombobox" causes "Combobox" to re-render every time inputValue changes
     * - When we define a debounced function in the body of a component that re-renders on every input change, that debounced function is re-created every time so nothing is ever debounced
     */
    const debouncedHandleSynchronousInputValueChange = useCallback(
        debounce(handleInputValueChange, searchDebounce),
        [handleInputValueChange, searchDebounce],
    );
    /**
     * TODO:
     * Move combobox/multiselect logic into custom hook.
     */
    const STANDARD_COMBOBOX_CONFIG: ComboboxStandardConfig = {
        /**
         * IMPORTANT
         * Never change initialInputValue or combobox search will break.
         *
         * Setting initialInputValue to an empty string prevents
         * combobox from setting the initially selected object's
         * label as the input string.
         */
        initialInputValue: '',
        items: flatOptions,
        defaultIsOpen: false,
        itemToString,
        onInputValueChange: debouncedHandleSynchronousInputValueChange,
    };

    const MUTUALLY_EXCLUSIVE_COMBOBOX_CONFIG: MutuallyExclusiveComboboxConfig =
        {
            singleSelect: {
                initialSelectedItem: defaultValue,
                onSelectedItemChange: handleSingleSelectedItemChange,
                stateReducer: singleSelectStateReducer,
            },
            multiSelect: {
                selectedItem: null,
                initialSelectedItem: null,
                onSelectedItemChange: handleMultiSelectedItemChange,
                stateReducer: multiSelectStateReducer,
            },
        };

    const {
        inputValue,
        isOpen,
        highlightedIndex,
        selectedItem,
        selectItem,
        getInputProps,
        getMenuProps,
        getItemProps,
    } = useCombobox({
        ...STANDARD_COMBOBOX_CONFIG,
        ...MUTUALLY_EXCLUSIVE_COMBOBOX_CONFIG[
            isMultiSelect ? 'multiSelect' : 'singleSelect'
        ],
    });

    useComboboxImperativeHandle(
        imperativeHandleRef,
        setSelectedItems,
        selectItem,
    );

    const handleLazyLoad = useCallback((): Promise<void> => {
        return Promise.resolve(
            onFetchOptions?.({
                search: inputValue.trim(),
                increasePage: true,
            }),
        );
    }, [inputValue, onFetchOptions]);

    const { itemBaseId, listBoxId, triggerId } = createElementIds(id);

    /*
     * Emulates index assignment from `useSelect`, but applies the
     * assignment to our necessarily nested group structure.  Also
     * runs `getItemProps` so those props can be spread on ListBoxItem.
     */
    const downshiftOptions = useDownshiftOptions({
        options,
        itemBaseId,
        getItemProps,
        selectedItems,
        isMultiSelect,
        itemIdentifier,
    });

    /* Ensures anchor assignment is also run during keyboard navigation. */
    const eventHandlerOverrides = {
        onBlur,
        onKeyDown,
        onFocus,
    };

    const menuProps = getMenuProps(
        {
            id: listBoxId,
            ref: listBoxRef,
            'aria-labelledby': ariaLabelledBy,
            'data-id': `${dataId}-listbox`,
            emptyState: getSearchEmptyState?.({ inputValue }) || (
                <ComboboxFilterEmptyState
                    data-id={`${dataId}-filter-empty-state`}
                    inputValue={inputValue}
                />
            ),
            fetchMoreItems: handleLazyLoad,
            hasMore,
            isLoading,
            items: downshiftOptions,
            loaderLabel,
        },
        /*
         * Error: 'downshift: The ref prop "ref" from getMenuProps was not applied correctly on your element.'
         * This error appears in 3 cases: when the ref is not passed correctly to the menu element,
         * when the menu element is not always in the dom/rendered conditionally, and when the menu element
         * is rendered in a portal, which delays its rendering.  The first two cases are fixable,
         * but this is the suggested workaround for rendering in a portal.
         * reference: https://github.com/downshift-js/downshift/issues/1272#issuecomment-1063244446
         */
        { suppressRefError: true },
    );

    /* Matches width of Popover to SelectTrigger. */
    useMatchWidth({
        sourceElement: triggerRef.current,
        targetElement: popoverElement,
        minWidth: MENU_MIN_WIDTH,
    });

    const inputProps = getInputProps({
        ...eventHandlerOverrides,
        disabled,
        id: triggerId,
        ref: inputRef,
    });

    const tagGroupColorSchemeToUse = readOnly ? 'neutral' : tagGroupColorScheme;

    return (
        <StyledSpan data-testid="Combobox" data-id="oT0iDA50">
            <ComboboxTrigger
                {...inputProps}
                aria-controls={listBoxId}
                aria-describedby={ariaDescribedBy}
                aria-labelledby={ariaLabelledBy}
                data-id={`${dataId}-trigger`}
                feedbackType={feedbackType}
                gridArea={gridArea}
                name={name}
                placeholderText={placeholderText}
                isLoading={isLoading}
                required={required}
                selectedItem={selectedItem as StructuredListItemData}
                readOnly={readOnly}
                aria-disabled={readOnly}
                fakeInputRef={triggerRef}
                clearSelectedItemButtonLabel={clearSelectedItemButtonLabel}
                onClear={() => {
                    selectItem(null);
                }}
            />
            <Popover
                // NOTE: This function ref is necessary in a React portal to reliably capture
                /**
                 * Changes to the ref, as portals can cause timing issues with standard refs.
                 */
                data-id={`${dataId}-popover`}
                anchor={triggerRef.current}
                isOpen={isOpen}
                placement="bottom-start"
                cosmosUseWithCaution_shouldTrapFocus={false}
                ref={handlePopoverRef}
                middleware={middleware}
                maxHeight={maxHeight}
                padding="none"
                content={
                    <StyledHeightWrapperDiv
                        $highlightedIndex={highlightedIndex}
                    >
                        <ListBox {...menuProps} />
                    </StyledHeightWrapperDiv>
                }
            />
            {isMultiSelect && (
                <TagGroup
                    clearAllLabel={
                        readOnly ? undefined : removeAllSelectedItemsLabel
                    }
                    onClearAll={() => {
                        if (!readOnly) {
                            setSelectedItems([]);
                        }
                    }}
                >
                    {selectedItems.map((item, index) => {
                        const hasAvatar = Boolean(item.avatar);
                        const clickLabel =
                            getRemoveIndividualSelectedItemClickLabel?.({
                                itemLabel: item.label,
                            });
                        const label = itemToString(item);

                        return (
                            <Metadata
                                key={item.label}
                                label={label}
                                type="tag"
                                colorScheme={tagGroupColorSchemeToUse}
                                clickLabel={clickLabel}
                                data-id={`${dataId}-tag-${item.label}`}
                                iconName={
                                    hasAvatar
                                        ? 'UserCircleSingle'
                                        : item.startIconName
                                }
                                ref={(el) => {
                                    itemRefs.current[index] = el;
                                }}
                                onClick={
                                    readOnly
                                        ? undefined
                                        : () => {
                                              handleRemoveSelectedItem(item);
                                          }
                                }
                            />
                        );
                    })}
                </TagGroup>
            )}
        </StyledSpan>
    );
};
