import { styled } from 'styled-components';
import { StructuredListItem } from '@cosmos/components/list-box';

export const StyledFlexDiv = styled.div`
    display: flex;
    flex-direction: row;
    flex: auto;
    min-width: 0;
    align-items: center;

    // The children of StructuredListItem should only ever be center-aligned when in this context.
    & > ${StructuredListItem} {
        align-items: center;
    }
`;
