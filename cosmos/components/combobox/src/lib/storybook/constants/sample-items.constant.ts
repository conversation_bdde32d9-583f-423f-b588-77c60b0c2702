import type { ListBoxItems } from '@cosmos/components/list-box';

export const SAMPLE_ITEMS: ListBoxItems = Array.from(
    { length: 10 },
    (_, index) => {
        return {
            avatar: {
                fallbackText: 'P',
                imgSrc: 'https://img-dev.dratacdn.com/site-admin/avatars/36f07743-bcf4-4483-a765-e86c3b7d7266-DanielMarashlian.jpg',
                imgAlt: 'option avatar',
            },
            label: `First Set Option ${index}`,
            description: `email-${index}@domain.com`,
            id: `option-${index}`,
        };
    },
);
