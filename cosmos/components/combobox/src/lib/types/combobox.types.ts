/**
 * TODO:
 * Clean this file up
 * Move types to individual files.
 */
import type { UseComboboxInputValueChange, UseComboboxProps } from 'downshift';
import type {
    <PERSON><PERSON>ttri<PERSON>es,
    FocusEventHandler,
    KeyboardEventHandler,
    ReactElement,
} from 'react';
import type { FeedbackType } from '@cosmos/components/field-feedback';
import type {
    ListBoxGroupData,
    ListBoxItemData,
    ListBoxItems,
} from '@cosmos/components/list-box';
import type { MetadataProps } from '@cosmos/components/metadata';
import type { FilterOptionsParams } from '../helpers';
import type { ComboboxImperativeHandle } from '../hooks/use-combobox-imperative-handle.hook';

interface CosmosCommonProps {
    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;
}

interface CosmosAriaProps {
    /**
     * Identifies the element (or elements) that describes this one.
     */
    ['aria-describedby']?: AriaAttributes['aria-describedby'];
    /**
     * Identifies the element (or elements) that labels this one.
     */
    ['aria-labelledby']: AriaAttributes['aria-labelledby'];
}

export interface BaseComboboxProps<TMultiple extends boolean | undefined>
    extends CosmosAriaProps,
        CosmosCommonProps {
    /**
     * An optional item which will be selected on Combobox initialization.
     */
    defaultValue?: ListBoxItemData;

    /**
     * List of initial selected items when multi select is enabled.
     */
    defaultSelectedOptions?: TMultiple extends true ? ListBoxItemData[] : never;

    /**
     * Whether the form control is disabled.
     */
    disabled?: boolean;

    /**
     * Provide optional feedback to the user based on their interaction with the field.
     */
    feedbackType?: FeedbackType;

    /**
     * Function called to retrieve the empty state when searching.
     */
    getSearchEmptyState?: ({
        inputValue,
    }: {
        inputValue: UseComboboxInputValueChange<ListBoxItemData>['inputValue'];
    }) => ReactElement | string;

    /**
     * Function that retrieves the "Remove" button label for an individual item in the TagGroup when multi select is enabled.
     */
    getRemoveIndividualSelectedItemClickLabel?: TMultiple extends true
        ? ({ itemLabel }: { itemLabel: string }) => MetadataProps['clickLabel']
        : never;

    /**
     * A string used by CSS Grid for grid-template layouts.
     */
    gridArea?: string;

    /**
     * Whether there are more options to load.
     */
    hasMore?: boolean;

    /**
     * A unique ID.
     */
    id: string;

    /**
     * Whether the component is in a loading state.
     */
    isLoading?: boolean;

    /**
     * Toggles between single and multi select.
     */
    isMultiSelect?: boolean;

    /**
     * The key or identifier used to uniquely identify an item in the collection.
     * This property is dynamic and refers to a field in the item object (e.g., 'id', or any other unique field).
     * It is used to match items in the selected list and must correspond to a valid key in the item object.
     */
    itemIdentifier?: string;

    /**
     * A function that is used to provide a string representation for each option.
     *
     * Https://github.com/downshift-js/downshift?tab=readme-ov-file#itemtostring.
     */
    itemToString?: UseComboboxProps<
        ListBoxItemData | ListBoxGroupData
    >['itemToString'];

    /**
     * Label shown in the loading state of the menu when loading or searching. Required for accessibility.
     */
    loaderLabel: string;

    /**
     * Name of the form control. Submitted with the form as part of a name/value pair.
     */
    name: string;

    /**
     * Function called when input is blurred.
     */
    onBlur?: FocusEventHandler<HTMLElement>;

    /**
     * Called when the selected item(s) change. Returns the changed item.
     * For single-select, undefined indicates no selection (cleared).
     * For multi-select, an empty array indicates no selections.
     */
    onChange: TMultiple extends true
        ? (value: ListBoxItemData[]) => void
        : (value: ListBoxItemData | undefined) => void;

    /**
     * Fetch options from external source.
     */
    onFilterOptions?: ({
        options,
        itemToString,
        inputValue,
    }: FilterOptionsParams) => ListBoxItems;

    /**
     * Function called when input is focused.
     */
    onFocus?: FocusEventHandler<HTMLElement>;

    /**
     * Function called when key down.
     */
    onKeyDown?: KeyboardEventHandler<HTMLElement>;

    /**
     * Function called when fetching options.
     */
    onFetchOptions?: ({
        search,
        increasePage,
    }: {
        search?: UseComboboxInputValueChange<ListBoxItemData>['inputValue'];
        increasePage?: boolean;
    }) => void;

    /**
     * Array of individual options and option groups for the user to choose from.
     */
    options?: ListBoxItems;

    /**
     * Optional text displayed in the input when no item is selected.
     */
    placeholderText?: string;

    /**
     * Whether the form control is read-only.
     */
    readOnly?: boolean;

    /**
     * Label applied to the "Remove all" button in the TagGroup when multi select is enabled.
     */
    removeAllSelectedItemsLabel?: TMultiple extends true ? string : never;

    /**
     * A value is required or must be checked for the form to be submittable.
     */
    required?: boolean;

    /**
     * The debounce time for the search input.
     */
    searchDebounce?: number;

    /**
     * ColorScheme of the TagGroup rendered when multi select is enabled.
     */
    tagGroupColorScheme?: TMultiple extends true
        ? MetadataProps['colorScheme']
        : never;

    /**
     * Enables the clear selection button.
     */
    clearSelectedItemButtonLabel?: string;

    /**
     * Optional ref object to access imperative methods for controlling the combobox externally.
     */
    imperativeHandleRef?: React.MutableRefObject<ComboboxImperativeHandle | null>;
}

export interface ComboboxStandardConfig {
    initialInputValue: UseComboboxProps<ListBoxItemData>['initialInputValue'];
    items: UseComboboxProps<ListBoxItemData>['items'];
    defaultIsOpen: UseComboboxProps<ListBoxItemData>['defaultIsOpen'];
    itemToString: UseComboboxProps<ListBoxItemData>['itemToString'];
    onInputValueChange: UseComboboxProps<ListBoxItemData>['onInputValueChange'];
}

export interface MutuallyExclusiveComboboxConfig {
    singleSelect: Omit<
        UseComboboxProps<ListBoxItemData>,
        | 'initialInputValue'
        | 'items'
        | 'defaultIsOpen'
        | 'itemToString'
        | 'onInputValueChange'
    >;
    multiSelect: Omit<
        UseComboboxProps<ListBoxItemData>,
        | 'initialInputValue'
        | 'items'
        | 'defaultIsOpen'
        | 'itemToString'
        | 'onInputValueChange'
    >;
}

export type ComboboxProps = BaseComboboxProps<boolean | undefined>;
