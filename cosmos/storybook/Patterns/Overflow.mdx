import { Meta } from '@storybook/addon-docs/blocks';
import {
  overflowModalImage,
  popoverOverflowImage,
  popoverOverflowTextImage,
  showMoreImage,
  stackOverflowImage,
} from './assets/overflow';

<Meta title="Patterns/Overflow" />

# Overflow

Overflow patterns provide consistent ways to manage space constraints and ensure access to all content.

## Overflow Patterns
- [`ShowMore` component](https://cosmos.drata.com/?path=/docs/typography-content-showmore--docs)

 <img src={showMoreImage} alt="Show More component" width="640" />

- Button at the bottom of the component with a “Show more” label that triggers a modal or popover
- Tertiary small neutral button horizontally aligned at the end with a “+N” that when clicked, triggers a popover with a list of the objects from such component

## Text
- For long text paragraphs, a `ShowMore` component should be used
- For pieces of text twice the height of the viewport, a button with a “Show more” label should be at the bottom of the paragraph that opens a modal with the rest of the text

    <img src={overflowModalImage} alt="Overflow in a modal" width="640" />

- Tertiary button at the right end with a “+N”. Clicking on this button will trigger a popover with the overflowing objects. Components with +N pattern:
    - Tags
    - Stacks
     <img src={stackOverflowImage} alt="Overflow in a stack component" width="320" />

## KeyValuePairs

**Vertically aligned**

- Use the width of the container to maximize visual area of the KVP
- KVP’s using `Stacks`, `TagGroup` should not use any overflow pattern if there are 24 or less objects in them. If they have over 24 objects then it should use the +N pattern

**Horizontally aligned**

- KVP’s using `Stacks`, `TagGroup` should use the +N pattern when reaching the max width of KVP

## Card

Cards should only grow vertically in certain cases when they don’t push down any content underneath. For the most part if the content is overflowing the card container then a scroll behavior should be used.

When possible always show all values on the Card.

## Table cell

Table cells have a defined maximum height of 96px (`dimension24x` ). When the content within a cell exceeds this height, components should detect the constraint and apply their designated overflow behavior—whether it’s truncation with ellipsis, +N pattern, or expansion to a modal with a button.

 <img src={popoverOverflowImage} alt="Table cell overflow in a popover" width="640" />

 <img src={popoverOverflowTextImage} alt="Table cell overflow in a popover for text" width="640" />

