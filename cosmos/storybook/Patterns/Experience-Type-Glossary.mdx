import { Meta } from '@storybook/addon-docs/blocks';

<Meta title="Patterns/Experience Type Glossary" />

# Experience Type Glossary

The experience type glossary defines standard page and flow types to support consistent, predictable user experiences in Drata.

| Name | Definition | Example | Variations |
| :--- | :--- | :--- | :--- |
| Index | The start page of a top-level navigation section | Compliance/Controls—full template with a table of controls | Table, gallery, dashboard |
| Detail | A detailed experience for a specific Drata object | Compliance/Controls/DCF-1 | Full page, panel peek |
| Creation, add, and edit experiences | Often a stepped process with a wizard or progressive disclosure form. | Setting/Notification Rules/Create notification rule | Sometimes starts in, or is entirely contained in a modal experience |
| Pre-auth | Likely uses the interstitial template and exists outside of the core Drata user experience. | Login or full screen error states |  |