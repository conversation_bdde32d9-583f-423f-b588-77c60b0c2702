import { afterEach, beforeAll, describe, expect, test } from 'vitest';
import { Language } from '@drata/enums';
import { sharedDirectLocaleController } from '@globals/i18n';
import { cleanup, render, screen } from '@testing-library/react';
import { DateTime } from './date-time';

describe('dateTime', () => {
    beforeAll(() => {
        sharedDirectLocaleController.setLocale(Language.ENGLISH_US);
    });

    afterEach(() => {
        cleanup();
    });

    test('should render successfully with required date and default format', () => {
        const testDate = new Date('2024-04-15T12:00:00Z');
        const { baseElement } = render(<DateTime date={testDate} />);

        expect(baseElement).toBeTruthy();

        const spanElement = screen.getByTestId('DateTime');

        expect(spanElement).toBeTruthy();
        expect(spanElement.tagName).toBe('SPAN');

        const timeElement = spanElement.querySelector('time');

        expect(timeElement).toBeTruthy();
        expect(timeElement?.tagName).toBe('TIME');
        expect(timeElement?.hasAttribute('datetime')).toBeTruthy();
        expect(timeElement?.getAttribute('datetime')).toBe(
            testDate.toISOString(),
        );
        expect(timeElement?.getAttribute('data-id')).toBe('pPV2yiML-time');

        // Verify the time element contains text content
        expect(timeElement?.textContent).toBeTruthy();
    });

    test('should render with a specific date', () => {
        const testDate = new Date('2024-04-15T12:00:00Z');
        const { getByTestId } = render(<DateTime date={testDate} />);
        const spanElement = getByTestId('DateTime');

        expect(spanElement).toBeTruthy();
        expect(spanElement.tagName).toBe('SPAN');

        const timeElement = spanElement.querySelector('time');

        expect(timeElement).toBeTruthy();
        expect(timeElement?.tagName).toBe('TIME');
        expect(timeElement?.hasAttribute('datetime')).toBeTruthy();
        expect(timeElement?.getAttribute('datetime')).toBe(
            testDate.toISOString(),
        );
        expect(timeElement?.getAttribute('data-id')).toBe('pPV2yiML-time');

        // Verify the time element contains text content
        expect(timeElement?.textContent).toBeTruthy();
    });

    test('should render with a non-range format', () => {
        const testDate = new Date('2024-04-15T12:00:00Z');
        const { getByTestId } = render(
            <DateTime date={testDate} format="field" />,
        );
        const spanElement = getByTestId('DateTime');

        expect(spanElement).toBeTruthy();
        expect(spanElement.tagName).toBe('SPAN');

        const timeElement = spanElement.querySelector('time');

        expect(timeElement).toBeTruthy();
        expect(timeElement?.tagName).toBe('TIME');
        expect(timeElement?.hasAttribute('datetime')).toBeTruthy();

        // For non-range formats, the datetime attribute should be a simple ISO string
        expect(timeElement?.getAttribute('datetime')).toBe(
            testDate.toISOString(),
        );
        expect(timeElement?.getAttribute('data-id')).toBe('pPV2yiML-time');

        // Verify the time element contains text content
        expect(timeElement?.textContent).toBeTruthy();
    });

    test('should render with a range format and endDate', () => {
        const startDate = new Date('2024-04-15T12:00:00Z');
        const endDate = new Date('2024-04-30T12:00:00Z');
        const { getByTestId } = render(
            <DateTime
                format="sentence_range"
                date={startDate}
                endDate={endDate}
            />,
        );
        const spanElement = getByTestId('DateTime');

        expect(spanElement).toBeTruthy();
        expect(spanElement.tagName).toBe('SPAN');

        const timeElement = spanElement.querySelector('time');

        expect(timeElement).toBeTruthy();
        expect(timeElement?.tagName).toBe('TIME');
        expect(timeElement?.hasAttribute('datetime')).toBeTruthy();

        // For range formats, the datetime attribute should be a duration string
        const expectedDateTimeValue = `${startDate.toISOString()}/${endDate.toISOString()}`;

        expect(timeElement?.getAttribute('datetime')).toBe(
            expectedDateTimeValue,
        );
        expect(timeElement?.getAttribute('data-id')).toBe('pPV2yiML-time');

        // Verify the time element contains text content
        expect(timeElement?.textContent).toBeTruthy();
    });

    // Now we test that endDate is allowed but ignored with non-range formats
    test('should allow endDate with non-range format but ignore it', () => {
        const testDate = new Date('2024-04-15T12:00:00Z');
        const endDate = new Date('2024-04-30T12:00:00Z');

        // This should not throw an error
        const { getByTestId } = render(
            <DateTime date={testDate} format="field" endDate={endDate} />,
        );

        const spanElement = getByTestId('DateTime');

        expect(spanElement).toBeTruthy();
        expect(spanElement.tagName).toBe('SPAN');

        const timeElement = spanElement.querySelector('time');

        expect(timeElement).toBeTruthy();

        // The content should be formatted with the field format, not a range format
        // which would include both dates
        expect(timeElement?.textContent).not.toContain('to');

        // For non-range formats, the datetime attribute should be a simple ISO string
        // even if endDate is provided (it should be ignored)
        expect(timeElement?.getAttribute('datetime')).toBe(
            testDate.toISOString(),
        );
        expect(timeElement?.getAttribute('data-id')).toBe('pPV2yiML-time');

        // Verify the time element contains text content
        expect(timeElement?.textContent).toBeTruthy();
    });
    test('should render with a tooltip for specified formats', () => {
        const testDate = new Date('2024-04-15T12:00:00Z');

        // Test with a format that should have a tooltip
        render(<DateTime date={testDate} format="table" />);

        // Get the span element
        const spanElement = screen.getByTestId('DateTime');

        expect(spanElement).toBeTruthy();
        expect(spanElement.getAttribute('data-id')).toBe('pPV2yiML');

        // The time element should be inside the tooltip
        const timeElement = spanElement.querySelector('time');

        expect(timeElement).toBeTruthy();
        expect(timeElement?.getAttribute('data-id')).toBe('pPV2yiML-time');

        // Verify the time element contains text content
        expect(timeElement?.textContent).toBeTruthy();
    });

    test('should not render a tooltip when isTooltipDisabled is true', () => {
        const testDate = new Date('2024-04-15T12:00:00Z');

        // Test with isTooltipDisabled prop
        render(<DateTime isTooltipDisabled date={testDate} format="table" />);

        // Get the span element
        const spanElement = screen.getByTestId('DateTime');

        expect(spanElement).toBeTruthy();
        expect(spanElement.getAttribute('data-id')).toBe('pPV2yiML');

        // The time element should still be rendered
        const timeElement = spanElement.querySelector('time');

        expect(timeElement).toBeTruthy();
        expect(timeElement?.getAttribute('data-id')).toBe('pPV2yiML-time');

        // Verify the time element contains text content
        expect(timeElement?.textContent).toBeTruthy();
    });

    test('should apply textProps to the Text component', () => {
        const testDate = new Date('2024-04-15T12:00:00Z');
        const textProps = {
            type: 'body' as const,
            size: '300' as const,
            colorScheme: 'primary' as const,
            align: 'center' as const,
        };

        render(<DateTime date={testDate} textProps={textProps} />);

        // Get the span element
        const spanElement = screen.getByTestId('DateTime');

        expect(spanElement).toBeTruthy();
        expect(spanElement.getAttribute('data-id')).toBe('pPV2yiML');

        // The time element should be rendered
        const timeElement = spanElement.querySelector('time');

        expect(timeElement).toBeTruthy();
        expect(timeElement?.getAttribute('data-id')).toBe('pPV2yiML-time');

        expect(timeElement?.textContent).toBeTruthy();
    });
});
