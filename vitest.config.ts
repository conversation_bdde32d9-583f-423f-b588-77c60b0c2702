import { defineConfig, mergeConfig } from 'vitest/config';
import rootConfig from './vite.config';

const config = defineConfig({
    test: {
        globals: false,
        watch: false,
        environment: 'happy-dom',
        setupFiles: ['./vitest.setup.ts'],
        // projects: ['./vite.config.{ts,mts}'],
        include: ['**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
        reporters: ['default'],
        coverage: {
            provider: 'v8' as const,
        },
        passWithNoTests: true,
    },
});

export default mergeConfig(rootConfig, config);
