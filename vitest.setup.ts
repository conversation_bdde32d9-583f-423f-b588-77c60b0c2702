/**
 * Global Vitest setup file.
 * This file runs before all tests and sets up common test environment.
 */

import { Language } from '@drata/enums';
import { i18n, sharedDirectLocaleController } from '@globals/i18n';

// Initialize i18n with empty messages to prevent activation errors in tests
// This matches the pattern used in individual test files
i18n.load(Language.ENGLISH_US, {});
i18n.activate(Language.ENGLISH_US);

sharedDirectLocaleController.setLocale(Language.ENGLISH_US);

// Also set up the 'en' locale for tests that use it
i18n.load('en', {});
