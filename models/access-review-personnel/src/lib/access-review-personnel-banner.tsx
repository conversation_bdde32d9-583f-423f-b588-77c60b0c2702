import { activeAccessReviewsApplicationsController } from '@controllers/access-reviews-applications';
import { Banner } from '@cosmos/components/banner';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { AppLink } from '@ui/app-link';

const HELP_DOCUMENT_URL =
    'https://help.drata.com/en/articles/8895897-access-reviews#h_d792c40a70';

export const AccessReviewPersonnelBanner = observer((): React.JSX.Element => {
    const { warnings, accessReviewsApplicationsDetails } =
        activeAccessReviewsApplicationsController;
    const { hasLimitedAccess } = sharedFeatureAccessModel;
    const { currentWorkspaceId } = sharedWorkspacesController;
    const navigate = useNavigate();

    const { name } = accessReviewsApplicationsDetails;
    const { hasFailed } = warnings ?? {};

    // Critical banner when connection fails
    if (hasFailed) {
        return (
            <Banner
                data-id="AccessReviewPersonnelBanner"
                data-testid="AccessReviewPersonnelBanner"
                displayMode="section"
                severity="critical"
                title={t`${name} couldn't connect.`}
                body={
                    <>
                        {hasLimitedAccess ? (
                            <Text colorScheme="critical">
                                <Trans>
                                    This user list might not be up to date
                                    because a connection is failing. Contact
                                    your administrator to review issue.
                                </Trans>
                            </Text>
                        ) : (
                            <Stack direction="row" gap="xs">
                                <Text colorScheme="critical">
                                    <Trans>
                                        This user list might not be up to date
                                        because a connection is failing.
                                    </Trans>
                                </Text>
                                <Button
                                    level="tertiary"
                                    colorScheme="danger"
                                    hasPadding={false}
                                    label={t`Go to connections`}
                                    onClick={() => {
                                        navigate(
                                            `/workspaces/${currentWorkspaceId}/connections/all/active`,
                                        );
                                    }}
                                />
                                <Text colorScheme="critical">
                                    <Trans>to review issue.</Trans>
                                </Text>
                            </Stack>
                        )}
                    </>
                }
            />
        );
    }

    return (
        <Banner
            data-id="AccessReviewPersonnelBanner"
            data-testid="AccessReviewPersonnelBanner"
            displayMode="section"
            severity="primary"
            title={t`Ensure the accuracy of your data`}
            body={
                <Text colorScheme="primary">
                    {t`Read how Drata pulls information regarding permissions and warnings as well as how to resolve them in the`}{' '}
                    <AppLink
                        isExternal
                        href={HELP_DOCUMENT_URL}
                        label={t`help document.`}
                    />
                </Text>
            }
        />
    );
});
