import { constant } from 'lodash-es';
import {
    activeAccessReviewApplicationDetailsController,
    sharedAccessReviewPeriodApplicationSummaryController,
    sharedAccessReviewPeriodApplicationUsersController,
} from '@controllers/access-reviews';
import { sharedSynchronizationsController } from '@controllers/synchronizations';
import { Banner } from '@cosmos/components/banner';
import { Feedback } from '@cosmos/components/feedback';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { Text } from '@cosmos/components/text';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import { Organization } from '@cosmos-lab/components/organization';
import type { ClientTypeEnum } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t, Trans } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { AppLink } from '@ui/app-link';
import type { NavigationItem } from '@ui/page-content';
import {
    buildActionStack,
    buildKeyValuePairs,
} from '@views/access-review-application-details';

const HELP_DOCUMENT_URL =
    'https://help.drata.com/en/articles/8895897-access-reviews#h_d792c40a70';

export const STUB_isAccessReviewReadOnlyUser = constant(false);

export class AccessReviewApplicationDetailsModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'access-review-application-details-overview';

    get breadcrumbs(): Breadcrumb[] {
        return [
            {
                label: t`Access Review`,
                pathname: 'governance/access-review/active',
            },
        ];
    }

    get title(): string {
        return (
            activeAccessReviewApplicationDetailsController.applicationDetails
                ?.application.name ?? ''
        );
    }

    get isLoading(): boolean {
        return (
            activeAccessReviewApplicationDetailsController.isLoading ||
            sharedAccessReviewPeriodApplicationSummaryController.isLoading ||
            sharedAccessReviewPeriodApplicationUsersController.isLoading
        );
    }

    get slot(): React.JSX.Element {
        return (
            <Organization
                data-id="avatar"
                imgSrc={activeAccessReviewApplicationDetailsController.logo}
                size="md"
                data-testid="AccessReviewDetailsHeaderSlot"
                imgAlt={
                    activeAccessReviewApplicationDetailsController
                        .applicationDetails?.application.name[0] ?? ''
                }
                fallbackText={
                    activeAccessReviewApplicationDetailsController
                        .applicationDetails?.application.name[0] ?? ''
                }
            />
        );
    }

    get actionStack(): React.JSX.Element | undefined {
        const { applicationDetails } =
            activeAccessReviewApplicationDetailsController;

        const { reSyncByClientType, isSyncing } =
            sharedSynchronizationsController;

        const { loadReviewers, usersReviewers: personnelList } =
            activeAccessReviewApplicationDetailsController;

        const { hasLimitedAccess } = sharedFeatureAccessModel;

        loadReviewers(
            applicationDetails?.application.reviewers.map(
                (reviewer) => reviewer.id,
            ) ?? [],
        );

        if (hasLimitedAccess) {
            return undefined;
        }

        const syncCallback = () => {
            reSyncByClientType(
                applicationDetails?.application.clientType as ClientTypeEnum,
                action(() => {
                    sharedAccessReviewPeriodApplicationUsersController.invalidateAccessReviewApplicationUsers();
                }),
            );
        };

        return buildActionStack(
            applicationDetails,
            personnelList,
            syncCallback,
            isSyncing,
        );
    }

    get keyValuePairs(): KeyValuePairProps[] {
        return buildKeyValuePairs(
            activeAccessReviewApplicationDetailsController.applicationDetails,
            sharedAccessReviewPeriodApplicationSummaryController.accessReviewPeriodApplicationSummaryDetails,
            sharedAccessReviewPeriodApplicationUsersController.totalReviewApplicationUsers,
        );
    }

    get banner(): React.JSX.Element {
        const { hasLimitedAccess } = sharedFeatureAccessModel;

        return (
            <>
                <Banner
                    data-id="active-access-review-details-banner-test-id"
                    displayMode="section"
                    title={t`Ensure the accuracy of your data`}
                    body={
                        <Text colorScheme="primary">
                            <Trans>
                                Read how Drata pulls information regarding
                                permissions and warnings as well as how to
                                resolve them in the{' '}
                                <AppLink
                                    isExternal
                                    href={HELP_DOCUMENT_URL}
                                    label={t`help document.`}
                                />
                            </Trans>
                        </Text>
                    }
                />

                {hasLimitedAccess && (
                    <Feedback
                        title={t`Read-only View`}
                        description={t`You have read-only access to the Access Review module. If you need to approve or reject users, please ask your Drata administrator to update your Drata role to include write permissions for Access Review.`}
                        severity="primary"
                    />
                )}
            </>
        );
    }
}

export class AccessReviewApplicationDetailsContentNavModel {
    constructor() {
        makeAutoObservable(this);
    }

    get config(): NavigationItem[] {
        const { applicationDetails } =
            activeAccessReviewApplicationDetailsController;

        const { currentWorkspaceId } = sharedWorkspacesController;

        if (!applicationDetails) {
            return [];
        }

        const { id } = applicationDetails.application;

        return [
            {
                id: 'overview',
                props: {
                    label: 'Overview',
                    href: `/workspaces/${currentWorkspaceId}/governance/access-review/application-details/${id}/period/${applicationDetails.reviewPeriod.id}/overview`,
                },
            },
            {
                id: 'personnel',
                props: {
                    label: 'Personnel',
                    href: `/workspaces/${currentWorkspaceId}/governance/access-review/application-details/${id}/period/${applicationDetails.reviewPeriod.id}`,
                },
            },
        ];
    }
}
