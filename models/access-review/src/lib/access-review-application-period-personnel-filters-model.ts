import { isEmpty, isEqual, isFunction, isNil, noop } from 'lodash-es';
import type {
    FetchDataResponseParams,
    FilterProps,
} from '@cosmos/components/datatable';
import type { FilterState } from '@cosmos/components/filter-field';
import type {
    AccessReviewApplicationSummaryResponseDto,
    ApplicationGroupResponseDto,
    ClientTypeEnum,
    ConnectionResponseDto,
} from '@globals/api-sdk/types';
import { action, makeAutoObservable } from '@globals/mobx';
import {
    filtersByProvider,
    getFilters,
    type WarningFilters,
} from './filter.helper';

// Define more specific types for filter values
interface FilterValue {
    value: unknown;
    filterType: string;
}

// Define known filter keys to improve type safety
type FilterKey =
    | 'warning'
    | 'connections'
    | 'groupIds'
    | 'employmentStatus'
    | 'review-status';

/**
 * Interface defining the contract for the filter model.
 */
interface IAccessReviewApplicationPeriodPersonnelFiltersModel {
    filterValues: Record<string, FilterValue>;
    setFilterValues: (values: Record<string, FilterValue>) => void;
    warningFiltersObject: WarningFilters;
    tableFilters: FilterProps;
    processFilterValues: (
        filters: FetchDataResponseParams['globalFilter']['filters'],
    ) => Record<string, FilterValue>;
    safeCompareFilters: (newValue: unknown, oldValue: unknown) => boolean;
    cleanFilterParams: (
        fetchParams: FetchDataResponseParams,
    ) => FetchDataResponseParams;
    cleanStaleFilters: () => void;
    ensureCleanState: () => void;
    handleFilterChange: (fetchParams: FetchDataResponseParams | null) => void;
}

/**
 * Model for managing access review application period personnel filters.
 */
class AccessReviewApplicationPeriodPersonnelFiltersModel
    implements IAccessReviewApplicationPeriodPersonnelFiltersModel
{
    clientType: ClientTypeEnum | undefined | null;
    source: string | undefined;
    isManuallyAddedApplication: boolean;
    accessReviewApplicationGroupsList: ApplicationGroupResponseDto[];
    loadAccessReviewApplicationGroupsForPersonnel: (params: {
        search?: string;
        applicationId?: number;
    }) => void;
    accessReviewApplicationConnections: ConnectionResponseDto[];
    loadAccessReviewApplicationUsersConnections: () => void;
    updateFilterValues: (
        filters: Record<string, unknown> | ArrayLike<unknown>,
    ) => void;
    accessReviewPeriodApplicationSummaryDetails: AccessReviewApplicationSummaryResponseDto | null;
    hasMoreGroups: boolean;
    isLoadingGroups: boolean;

    /**
     * Local state for filter values.
     */
    #filterValues: Record<string, FilterValue> = {};

    constructor(props: {
        clientType: ClientTypeEnum | undefined | null;
        source: string | undefined;
        isManuallyAddedApplication: boolean;
        accessReviewApplicationGroupsList?: ApplicationGroupResponseDto[];
        loadAccessReviewApplicationGroupsForPersonnel: (params: {
            search?: string;
            applicationId?: number;
        }) => void;
        accessReviewApplicationConnections: ConnectionResponseDto[];
        loadAccessReviewApplicationUsersConnections: () => void;
        updateFilterValues: (
            filters: { [s: string]: unknown } | ArrayLike<unknown>,
        ) => void;
        accessReviewPeriodApplicationSummaryDetails: AccessReviewApplicationSummaryResponseDto | null;
        hasMoreGroups?: boolean;
        isLoadingGroups?: boolean;
    }) {
        this.clientType = props.clientType;
        this.source = props.source;
        this.isManuallyAddedApplication = props.isManuallyAddedApplication;
        this.accessReviewApplicationGroupsList =
            props.accessReviewApplicationGroupsList ?? [];
        this.loadAccessReviewApplicationGroupsForPersonnel =
            props.loadAccessReviewApplicationGroupsForPersonnel;
        this.accessReviewApplicationConnections =
            props.accessReviewApplicationConnections;
        this.loadAccessReviewApplicationUsersConnections =
            props.loadAccessReviewApplicationUsersConnections;
        this.updateFilterValues = props.updateFilterValues;
        this.accessReviewPeriodApplicationSummaryDetails =
            props.accessReviewPeriodApplicationSummaryDetails;
        this.hasMoreGroups = props.hasMoreGroups ?? false;
        this.isLoadingGroups = props.isLoadingGroups ?? false;

        makeAutoObservable(this, {
            handleFilterChange: action,
            cleanStaleFilters: action,
        });
    }

    /**
     * Getter for filter values.
     */
    get filterValues(): Record<string, FilterValue> {
        return this.#filterValues;
    }

    /**
     * Setter for filter values.
     */
    setFilterValues = (values: Record<string, FilterValue>): void => {
        this.#filterValues = values;
    };

    /**
     * Get warning filters based on provider.
     */
    get warningFiltersObject(): WarningFilters {
        return filtersByProvider(
            this.clientType,
            this.isManuallyAddedApplication,
            this.source,
        );
    }

    /**
     * Get table filters configuration with direct update handler.
     */
    get tableFilters(): FilterProps {
        // Provide default warnings object when accessReviewPeriodApplicationSummaryDetails is null
        const warnings = this.accessReviewPeriodApplicationSummaryDetails ?? {
            appId: 0,
            periodId: 0,
            approved: 0,
            rejected: 0,
            notReviewed: 0,
            outOfScope: 0,
            formerPersonnel: 0,
            missingMfa: 0,
            unlinkedIdentities: 0,
            levelChange: 0,
            serviceAccounts: 0,
            totalWarnings: 0,
            id: 0,
            hasFailed: false,
        };

        const baseFilters = getFilters(
            this.accessReviewApplicationGroupsList,
            this.loadAccessReviewApplicationGroupsForPersonnel,
            this.accessReviewApplicationConnections,
            this.loadAccessReviewApplicationUsersConnections,
            this.warningFiltersObject,
            warnings,
            this.isManuallyAddedApplication,
            this.handlePersonnelFilterUpdate,
            this.hasMoreGroups,
            this.isLoadingGroups,
        );

        return {
            ...baseFilters,
            onClearFiltersFn: () => {
                // This will be called by the datatable when it needs to clear filters
                this.#filterValues = {};
                if (isFunction(this.updateFilterValues)) {
                    this.updateFilterValues({});
                }
            },
        };
    }

    /**
     * Process filter values from the Datatable component.
     */
    processFilterValues = (
        filters: FetchDataResponseParams['globalFilter']['filters'],
    ): Record<string, FilterValue> => {
        if (isEmpty(filters)) {
            return {};
        }

        const newFilterValues: Record<string, FilterValue> = {};
        const enabledFilterIds = new Set(
            this.tableFilters.filters.map((filter) => filter.id),
        );

        Object.entries(filters).forEach(([key, filterData]) => {
            // Skip filters that are not enabled for the current provider configuration
            if (!enabledFilterIds.has(key)) {
                return;
            }

            // Handle empty arrays as valid filter values
            if (Array.isArray(filterData.value) || !isNil(filterData.value)) {
                newFilterValues[key] = {
                    value: filterData.value,
                    filterType: filterData.filterType,
                };
            }
        });

        return newFilterValues;
    };

    /**
     * Safely compares two filter values, handling undefined values.
     */
    safeCompareFilters = (newValue: unknown, oldValue: unknown): boolean => {
        // If both are undefined or null, they're equal
        if (!newValue && !oldValue) {
            return true;
        }

        // If only one is undefined/null, they're different
        if (!newValue || !oldValue) {
            return false;
        }

        // Use isEqual for deep comparison
        return isEqual(newValue, oldValue);
    };

    /**
     * Check if a specific filter has changed.
     */
    #hasFilterChanged = (
        key: FilterKey,
        newValues: Record<string, FilterValue>,
    ): boolean => {
        const isInNew = key in newValues;
        const isInCurrent = key in this.#filterValues;

        // If it's in both, compare values
        if (isInNew && isInCurrent) {
            return !this.safeCompareFilters(
                newValues[key].value,
                this.#filterValues[key].value,
            );
        }

        // If it's only in one, it has changed
        return isInNew || isInCurrent;
    };

    /**
     * Implementation of filter change logic.
     */
    #handleFilterChangeImplementation = (
        fetchParams: FetchDataResponseParams | null,
    ): void => {
        if (!fetchParams?.globalFilter.filters) {
            return;
        }

        const newFilterValues = this.processFilterValues(
            fetchParams.globalFilter.filters,
        );

        // Check if any filter changed using our safe comparison
        const filtersChanged =
            Object.keys(newFilterValues).length !==
                Object.keys(this.#filterValues).length ||
            !isEqual(newFilterValues, this.#filterValues);

        // Check specific filters using the helper method
        const warningsFilterChanged = this.#hasFilterChanged(
            'warning',
            newFilterValues,
        );
        const connectionsFilterChanged = this.#hasFilterChanged(
            'connections',
            newFilterValues,
        );
        const groupIdsFilterChanged = this.#hasFilterChanged(
            'groupIds',
            newFilterValues,
        );
        const personnelFilterChanged = this.#hasFilterChanged(
            'employmentStatus',
            newFilterValues,
        );
        const statusFilterChanged = this.#hasFilterChanged(
            'review-status',
            newFilterValues,
        );

        if (
            filtersChanged ||
            warningsFilterChanged ||
            connectionsFilterChanged ||
            groupIdsFilterChanged ||
            personnelFilterChanged ||
            statusFilterChanged
        ) {
            this.setFilterValues(newFilterValues);
            // Check if updateFilterValues exists before calling it
            if (isFunction(this.updateFilterValues)) {
                const filterValues: Record<string, unknown> = {};

                Object.entries(fetchParams.globalFilter.filters).forEach(
                    ([key, filter]) => {
                        if ('value' in filter) {
                            filterValues[key] = filter.value;
                        }
                    },
                );

                this.updateFilterValues(filterValues);
            } else {
                console.error('updateFilterValues is not a function', this);
            }
        }
    };

    /**
     * Clean filter parameters by removing filters that don't have matching configurations.
     */
    cleanFilterParams = (
        fetchParams: FetchDataResponseParams,
    ): FetchDataResponseParams => {
        const enabledFilterIds = new Set(
            this.tableFilters.filters.map((filter) => filter.id),
        );

        const cleanedFilters = Object.fromEntries(
            Object.entries(fetchParams.globalFilter.filters).filter(([key]) =>
                enabledFilterIds.has(key),
            ),
        );

        return {
            ...fetchParams,
            globalFilter: {
                ...fetchParams.globalFilter,
                filters: cleanedFilters,
            },
        };
    };

    /**
     * Clean stale filters that don't match current configuration.
     * This prevents errors when filter configurations change between different
     * application types, providers, or other context switches.
     */
    cleanStaleFilters = (): void => {
        const enabledFilterIds = new Set(
            this.tableFilters.filters.map((filter) => filter.id),
        );

        // Check if there are any stale filters in our model state
        const currentFilters = Object.keys(this.#filterValues);
        const staleFilters = currentFilters.filter(
            (filterId) => !enabledFilterIds.has(filterId),
        );

        // This prevents stale filters from persisting in the datatable's global state
        if (!isEmpty(staleFilters) || !isEmpty(currentFilters)) {
            // Clear internal state first
            this.#filterValues = {};

            // This prevents useTableFilters from processing stale filter configurations
            if (isFunction(this.updateFilterValues)) {
                this.updateFilterValues({});
            }
        }
    };

    /**
     * Ensure clean filter state - can be called explicitly when needed.
     */
    ensureCleanState = (): void => {
        this.cleanStaleFilters();
    };

    /**
     * Handle filter changes with automatic cleanup.
     */
    handleFilterChange = (
        fetchParams: FetchDataResponseParams | null,
    ): void => {
        if (!fetchParams) {
            return;
        }

        // Clean the parameters first
        const cleanedParams = this.cleanFilterParams(fetchParams);

        // Process the cleaned parameters
        this.#handleFilterChangeImplementation(cleanedParams);
    };

    /**
     * Handle personnel filter updates.
     */
    handlePersonnelFilterUpdate = (value: unknown): void => {
        const filters = {
            employmentStatus: { value, filterType: 'combobox' },
        };

        this.updateFilterValues(filters);
    };
}

/**
 * Factory function to create an AccessReviewApplicationPeriodPersonnelFiltersModel with default values.
 */
export const createAccessReviewPeriodFiltersModel = (
    props: Partial<{
        clientType: ClientTypeEnum | undefined | null;
        source: string | undefined;
        isManuallyAddedApplication: boolean;
        accessReviewApplicationGroupsList?: ApplicationGroupResponseDto[];
        loadAccessReviewApplicationGroupsForPersonnel: (params: {
            search?: string;
            applicationId?: number;
        }) => void;
        accessReviewApplicationConnections: ConnectionResponseDto[];
        loadAccessReviewApplicationUsersConnections: (params: {
            search?: string;
        }) => void;
        updateFilterValues: (
            filters: Record<string, FilterState['value']>,
        ) => void;
        accessReviewPeriodApplicationSummaryDetails: AccessReviewApplicationSummaryResponseDto | null;
        hasMoreGroups?: boolean;
        isLoadingGroups?: boolean;
    }> = {},
): AccessReviewApplicationPeriodPersonnelFiltersModel => {
    return new AccessReviewApplicationPeriodPersonnelFiltersModel({
        clientType: props.clientType ?? null,
        source: props.source ?? undefined,
        isManuallyAddedApplication: props.isManuallyAddedApplication ?? false,
        accessReviewApplicationGroupsList:
            props.accessReviewApplicationGroupsList ?? [],
        loadAccessReviewApplicationGroupsForPersonnel:
            props.loadAccessReviewApplicationGroupsForPersonnel ?? noop,
        accessReviewApplicationConnections:
            props.accessReviewApplicationConnections ?? [],
        loadAccessReviewApplicationUsersConnections:
            props.loadAccessReviewApplicationUsersConnections ?? noop,
        updateFilterValues: props.updateFilterValues ?? noop,
        accessReviewPeriodApplicationSummaryDetails:
            props.accessReviewPeriodApplicationSummaryDetails ?? null,
        hasMoreGroups: props.hasMoreGroups,
        isLoadingGroups: props.isLoadingGroups,
    });
};
