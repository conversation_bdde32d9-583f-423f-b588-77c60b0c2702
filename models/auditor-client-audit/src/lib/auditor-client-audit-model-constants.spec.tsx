import { describe, expect, test, vi } from 'vitest';
import { getIsArchiveExpired } from './auditor-client-audit-model-helpers';

vi.mock('@globals/i18n/macro', () => ({
    t: (str: string) => str,
}));
describe('getIsArchiveExpired', () => {
    test('should return true when companyArchiveUpdatedAt is null', () => {
        expect(getIsArchiveExpired(null as unknown as Date)).toBeTruthy();
    });

    test('should return true when companyArchiveUpdatedAt is undefined', () => {
        expect(getIsArchiveExpired(undefined as unknown as Date)).toBeTruthy();
    });

    test('should return true when current date is after expiration date', () => {
        // Mock current date to a fixed value
        const mockDate = new Date('2023-01-02T12:00:00Z');

        vi.spyOn(global, 'Date').mockImplementation(() => mockDate);

        // Create a date 25 hours in the past (beyond 24 hour expiration)
        const pastDate = new Date('2023-01-01T10:00:00Z');

        expect(getIsArchiveExpired(pastDate)).toBeTruthy();

        vi.restoreAllMocks();
    });

    test('should return false when current date is in the future', () => {
        const twoDaysLater = new Date();

        twoDaysLater.setDate(twoDaysLater.getDate() + 2);

        expect(getIsArchiveExpired(twoDaysLater)).toBeFalsy();

        vi.restoreAllMocks();
    });

    test('should return true when current date equals expiration date', () => {
        // Mock current date to exactly match expiration
        const mockDate = new Date('2023-01-01T12:00:00Z');

        vi.spyOn(global, 'Date').mockImplementation(() => mockDate);

        // Create a date that will expire exactly at the mock current time
        const expirationDate = new Date('2023-01-01T12:00:00Z');

        expirationDate.setHours(expirationDate.getHours() - 24);

        expect(getIsArchiveExpired(expirationDate)).toBeTruthy();

        vi.restoreAllMocks();
    });
});
