import type { ComponentProps } from 'react';
import { sharedAssetsProviderListController } from '@controllers/assets';
import {
    sharedUsersController,
    sharedUsersInfiniteController,
} from '@controllers/users';
import type { Datatable } from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import type {
    ListBoxItemData,
    ListBoxItems,
} from '@cosmos/components/list-box';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { providers } from '@globals/providers';
import {
    getAssetsClassFilterOptions,
    getAssetsProviderFilterOptions,
    getAssetsStatusFilterOptions,
    getAssetsTypeFilterOptions,
} from './constants/filters.constants';

export type FilterDataTableProps = ComponentProps<
    typeof Datatable
>['filterProps'];

export class AssetsModel {
    constructor() {
        makeAutoObservable(this);
    }

    get userSelect(): ListBoxItems {
        return sharedUsersController.fullUsers;
    }
    get currentUser(): ListBoxItemData {
        const { user: meAsUser } = sharedCurrentUserController;

        return (
            sharedUsersController.users.find(
                (user) => Number(user.id) === meAsUser?.id,
            ) ?? ({} as ListBoxItemData)
        );
    }
}

export class AssetsFiltersModel {
    constructor() {
        makeAutoObservable(this);
    }

    get assetsProviderSelectFilter(): Filter {
        const { assetProviders } = sharedAssetsProviderListController;

        return {
            filterType: 'select',
            id: 'assetProvider',
            label: t`Source`,
            placeholder: t`Search by source`,
            options: [
                ...assetProviders.map((provider: string) => ({
                    id: provider,
                    label: providers[provider as keyof typeof providers].name,
                    value: provider,
                })),
                ...getAssetsProviderFilterOptions(),
            ].sort((a, b) => a.label.localeCompare(b.label)),
        };
    }

    get userSelectFilter(): Filter {
        const { options, hasNextPage, isFetching, isLoading, onFetchUsers } =
            sharedUsersInfiniteController;

        return {
            filterType: 'combobox',
            id: 'userId',
            label: t`Owner`,
            options,
            hasMore: hasNextPage,
            isLoading: isFetching && isLoading,
            onFetchOptions: (params) => {
                onFetchUsers({
                    ...params,
                    withAllUsers: true,
                });
            },
            placeholder: t`Search by owner`,
            clearSelectedItemButtonLabel: t`Clear`,
        };
    }

    get classFilter(): Filter {
        return {
            filterType: 'select',
            id: 'assetClassType',
            placeholder: t`Search by class`,
            label: t`Class`,
            defaultValue: getAssetsClassFilterOptions().find(
                (option) => option.id === 'ALL',
            ),
            options: getAssetsClassFilterOptions(),
        };
    }

    get statusFilter(): Filter {
        return {
            filterType: 'select',
            id: 'employmentStatus',
            placeholder: t`Search by status`,
            label: t`Owner status`,
            defaultValue: getAssetsStatusFilterOptions().find(
                (option) => option.id === 'ALL',
            ),
            options: getAssetsStatusFilterOptions(),
        };
    }

    get filters(): FilterDataTableProps {
        return {
            clearAllButtonLabel: t`Reset`,
            triggerLabel: t`Filters`,
            filters: [
                this.classFilter,
                {
                    filterType: 'radio',
                    id: 'assetType',
                    label: t`Type`,
                    value: 'ALL',
                    options: getAssetsTypeFilterOptions(),
                },
                this.assetsProviderSelectFilter,
                this.userSelectFilter,
                this.statusFilter,
            ],
        };
    }
}

export const activeAssetsFiltersModel = new AssetsFiltersModel();
