import { isNil } from 'lodash-es';
import {
    type AccessReviewStatus,
    sharedAccessReviewBulkActionStatusController,
    sharedAccessReviewPeriodApplicationUserController,
} from '@controllers/access-reviews';
import { ActionStack } from '@cosmos/components/action-stack';
import { Avatar } from '@cosmos/components/avatar';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import type { ColorScheme } from '@cosmos/components/metadata';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';
import { AccessReviewPersonnelDetailsBanner } from './access-review-personnel-details-banner';
import { getStatusLabel } from './helpers/access-review-get-status-label.helper';
import { openAccessReviewUpdatePersonnelStatusModal } from './helpers/access-review-open-update-personnel-status-modal';

export class AccessReviewPersonnelDetailsModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'access-review-application-details-active-header';

    dataId = 'access-review-application-details-active-header';

    get breadcrumbs(): Breadcrumb[] {
        const {
            accessReviewPeriodApplicationUserDetails,
            applicationId,
            periodId,
        } = sharedAccessReviewPeriodApplicationUserController;

        const breadcrumbs: Breadcrumb[] = [
            {
                label: t`Access Review`,
                pathname: 'governance/access-review/active',
            },
        ];

        if (!isNil(applicationId)) {
            breadcrumbs.push({
                label:
                    accessReviewPeriodApplicationUserDetails?.application
                        .name || t`Unknown`,
                pathname: `governance/access-review/active/${applicationId}/period/${periodId}/personnel`,
            });
        }

        return breadcrumbs;
    }

    get isLoading(): boolean {
        return sharedAccessReviewPeriodApplicationUserController.isLoading;
    }

    get title(): string {
        const { accessReviewPeriodApplicationUserDetails, isLoading } =
            sharedAccessReviewPeriodApplicationUserController;

        if (isLoading || isNil(accessReviewPeriodApplicationUserDetails)) {
            return t`Loading...`;
        }

        const { user } = accessReviewPeriodApplicationUserDetails;

        if (isNil(user)) {
            return t`Loading...`;
        }

        return getFullName(user.firstName, user.lastName) || t`Unknown`;
    }

    get slot(): React.JSX.Element | undefined {
        const { userUrl } = sharedAccessReviewPeriodApplicationUserController;

        return (
            <Avatar
                fallbackText={getInitials(this.title)}
                imgAlt={this.title}
                data-id="oY88FePq"
                data-testid={`${this.dataId}-page-header-avatar`}
                imgSrc={userUrl}
            />
        );
    }
    get keyValuePairs(): KeyValuePairProps[] {
        const { accessReviewPeriodApplicationUserDetails } =
            sharedAccessReviewPeriodApplicationUserController;

        if (isNil(accessReviewPeriodApplicationUserDetails)) {
            return [];
        }
        const { status } = accessReviewPeriodApplicationUserDetails;

        let statusLabel: string;
        let statusColorScheme: ColorScheme;

        switch (status) {
            case 'APPROVED': {
                statusLabel = t`Approved`;
                statusColorScheme = 'success';

                break;
            }
            case 'REJECTED': {
                statusLabel = t`Rejected`;
                statusColorScheme = 'critical';

                break;
            }
            case 'OUT_OF_SCOPE': {
                statusLabel = t`Out of scope`;
                statusColorScheme = 'neutral';

                break;
            }
            case 'NOT_REVIEWED':
            default: {
                statusLabel = t`Not reviewed`;
                statusColorScheme = 'neutral';

                break;
            }
        }

        return [
            {
                id: 'access-review-personnel-status',
                'data-id': 'access-review-personnel-status',
                label: t`Review status`,

                type: 'TAG',
                value: {
                    label: statusLabel,
                    colorScheme: statusColorScheme,
                    type: 'status',
                },
            },
        ];
    }

    get actionStack(): React.JSX.Element | undefined {
        const { accessReviewPeriodApplicationUserDetails } =
            sharedAccessReviewPeriodApplicationUserController;

        const { updateStatus } = sharedAccessReviewBulkActionStatusController;

        if (isNil(accessReviewPeriodApplicationUserDetails)) {
            return undefined;
        }
        const { status, id, username } =
            accessReviewPeriodApplicationUserDetails;

        return (
            <ActionStack
                isFullWidth
                actions={[
                    {
                        actionType: 'dropdown',
                        id: 'access-review-personnel-action-stack',
                        typeProps: {
                            label: getStatusLabel(status),
                            level: 'secondary',
                            items: [
                                {
                                    type: 'item',
                                    label: t`Out of scope`,
                                    id: 'OUT_OF_SCOPE',
                                },
                                {
                                    type: 'item',
                                    label: t`Rejected`,
                                    id: 'REJECTED',
                                },
                                {
                                    type: 'item',
                                    label: t`Approved`,
                                    id: 'APPROVED',
                                },
                            ],
                            onSelectGlobalOverride: ({
                                id: statusToUpdate,
                            }) => {
                                if (statusToUpdate === 'REJECTED') {
                                    openAccessReviewUpdatePersonnelStatusModal(
                                        id,
                                        username,
                                    );

                                    return;
                                }

                                updateStatus(
                                    [id],
                                    statusToUpdate as AccessReviewStatus,
                                );
                            },
                        },
                    },
                ]}
            />
        );
    }

    get banner(): React.JSX.Element | undefined {
        return <AccessReviewPersonnelDetailsBanner />;
    }
}
