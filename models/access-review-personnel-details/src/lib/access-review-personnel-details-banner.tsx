import { useState } from 'react';
import { sharedAccessReviewPeriodApplicationUserController } from '@controllers/access-reviews';
import { Banner } from '@cosmos/components/banner';
// eslint-disable-next-line no-restricted-imports -- We don't currently have a LinkButton which supports onClick
import { Link } from '@cosmos/components/link';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const AccessReviewPersonnelDetailsBanner = observer(
    (): React.JSX.Element | undefined => {
        const [isShowing, setIsShowing] = useState(true);

        const { accessReviewPeriodApplicationUserDetails } =
            sharedAccessReviewPeriodApplicationUserController;

        if (!accessReviewPeriodApplicationUserDetails) {
            return undefined;
        }

        const {
            hasChangedAccessLevel,
            isFormerPersonnelWithAccess,
            application,
        } = accessReviewPeriodApplicationUserDetails;

        const applicationName = application.name || '';

        const showWarningBanner =
            hasChangedAccessLevel && !isFormerPersonnelWithAccess && isShowing;

        return (
            <>
                {isFormerPersonnelWithAccess && (
                    <Banner
                        data-id="access-review-personnel-banner"
                        displayMode="section"
                        severity="critical"
                        title={t`A former employee has access to your data`}
                        body={
                            <Text colorScheme="critical">
                                <Trans>
                                    Looks like this account from a former
                                    employee still has access to{' '}
                                    {applicationName}. Remove all access as soon
                                    as possible.
                                </Trans>
                            </Text>
                        }
                    />
                )}
                {showWarningBanner && (
                    <Banner
                        data-id="access-review-personnel-banner"
                        displayMode="section"
                        severity="warning"
                        title={t`We detected a user access level change`}
                        body={
                            <Text colorScheme="warning">
                                <Trans>
                                    This user&apos;s access level changed since
                                    the last user access review.&nbsp;
                                </Trans>
                                <Link
                                    colorScheme="warning"
                                    label={t`Discard warning`}
                                    onClick={() => {
                                        setIsShowing(false);
                                    }}
                                />
                            </Text>
                        }
                    />
                )}
            </>
        );
    },
);
