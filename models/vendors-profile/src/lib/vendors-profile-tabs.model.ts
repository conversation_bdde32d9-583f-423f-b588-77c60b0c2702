import { isNil } from 'lodash-es';
import { sharedVendorsDetailsController } from '@controllers/vendors';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { VendorCategory } from './types/vendor-types';

interface Tab {
    topicPath: string;
    label: string;
    iconName?: string;
}

export class VendorsProfileTabsModel {
    #vendorId: number | null = null;
    #vendorType: VendorCategory = 'current';

    constructor() {
        makeAutoObservable(this);
    }

    get tabs(): Tab[] {
        const {
            isReleaseVrmPublicTrustCenterViewCollection,
            isVendorRiskManagementProEnabled,
        } = sharedFeatureAccessModel;

        if (isNil(this.#vendorId)) {
            return [];
        }

        const vendorTypePrefix = this.#vendorType;
        const isCurrent = vendorTypePrefix === 'current';

        const { vendorDetails } = sharedVendorsDetailsController;
        const isProspectiveVendor = vendorDetails?.status === 'PROSPECTIVE';

        const showRisksTab =
            !isProspectiveVendor && isVendorRiskManagementProEnabled;

        return [
            {
                topicPath: `vendors/${vendorTypePrefix}/${this.#vendorId}/overview`,
                label: t`Overview`,
            },
            ...(showRisksTab
                ? [
                      {
                          topicPath: `vendors/current/${this.#vendorId}/risks`,
                          label: t`Risks`,
                      },
                  ]
                : []),
            {
                topicPath: `vendors/${vendorTypePrefix}/${this.#vendorId}/security-reviews`,
                label: t`Security reviews`,
            },
            {
                topicPath: `vendors/${vendorTypePrefix}/${this.#vendorId}/reports-and-documents`,
                label: t`Reports and documents`,
            },
            ...(isCurrent
                ? [
                      {
                          topicPath: `vendors/${vendorTypePrefix}/${this.#vendorId}/subprocessors`,
                          label: t`Subprocessors`,
                      },
                  ]
                : []),
            ...(isReleaseVrmPublicTrustCenterViewCollection
                ? [
                      {
                          topicPath: `vendors/${vendorTypePrefix}/${this.#vendorId}/trust-center`,
                          label: t`Trust Center`,
                          iconName: 'SafeBase',
                      },
                  ]
                : []),
        ];
    }

    setVendorId(id: number): void {
        this.#vendorId = id;
    }

    setVendorType(type: VendorCategory): void {
        this.#vendorType = type;
    }
}
