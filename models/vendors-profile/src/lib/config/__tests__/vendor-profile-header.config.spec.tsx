import { describe, expect, test, vi } from 'vitest';
import { VENDORS_CURRENT_PROFILE_HEADER_KEY } from '@components/vendors-current';
import type { VendorProfileHeader } from '@controllers/vendors';
import { buildProfileHeader } from '../vendor-profile-header.config';

vi.mock('@components/vendors-current', () => ({
    VENDORS_CURRENT_PROFILE_HEADER_KEY: 'vendors-current-profile-header',
}));

describe('vendor-profile-header.config', () => {
    describe('buildProfileHeader', () => {
        test('should build header with all values present', () => {
            const mockData: VendorProfileHeader = {
                name: 'Test Vendor',
                logoUrl: 'https://example.com/logo.png',
                showSecurityReviewsInfoBanner: false,
                businessUnit: 'FINANCE',
                impactLevel: 'HIGH',
                risk: 'HIGH',
                associatedRisks: 5,
                securityReviewWindow: '30 days',
            };

            const result = buildProfileHeader(mockData);

            expect(result).toHaveLength(5);

            // Business Unit
            expect(result[0]).toStrictEqual({
                id: `${VENDORS_CURRENT_PROFILE_HEADER_KEY}-business-unit`,
                'data-id': `${VENDORS_CURRENT_PROFILE_HEADER_KEY}-business-unit`,
                label: 'Business unit',
                value: {
                    label: 'Finance',
                },
                type: 'BADGE',
            });

            // Impact Level
            expect(result[1]).toStrictEqual({
                id: `${VENDORS_CURRENT_PROFILE_HEADER_KEY}-impact-level`,
                'data-id': `${VENDORS_CURRENT_PROFILE_HEADER_KEY}-impact-level`,
                label: 'Impact level',
                value: {
                    label: 'High',
                },
                type: 'BADGE',
            });

            // Risk
            expect(result[2]).toStrictEqual({
                id: `${VENDORS_CURRENT_PROFILE_HEADER_KEY}-risk`,
                'data-id': `${VENDORS_CURRENT_PROFILE_HEADER_KEY}-risk`,
                label: 'Risk',
                value: {
                    label: 'High',
                    type: 'status',
                    colorScheme: 'critical',
                },
                type: 'BADGE',
            });

            // Associated Risks
            expect(result[3]).toStrictEqual({
                id: `${VENDORS_CURRENT_PROFILE_HEADER_KEY}-associated-risks`,
                'data-id': `${VENDORS_CURRENT_PROFILE_HEADER_KEY}-associated-risks`,
                label: 'Associated risks',
                value: {
                    label: '5',
                    type: 'number',
                },
                type: 'TAG',
            });

            // Security Review Window
            expect(result[4]).toStrictEqual({
                id: `${VENDORS_CURRENT_PROFILE_HEADER_KEY}-security-review-window`,
                'data-id': `${VENDORS_CURRENT_PROFILE_HEADER_KEY}-security-review-window`,
                label: 'Security review window',
                value: '30 days',
                type: 'TEXT',
            });
        });

        test('should handle missing values with defaults', () => {
            const mockData: VendorProfileHeader = {
                name: 'Test Vendor',
                logoUrl: 'https://example.com/logo.png',
                showSecurityReviewsInfoBanner: false,
                businessUnit: undefined,
                impactLevel: undefined,
                risk: undefined,
                associatedRisks: undefined,
                securityReviewWindow: undefined,
            };

            const result = buildProfileHeader(mockData);

            expect(result).toHaveLength(5);

            // Check all default values
            expect(result[0].value).toBe('-');
            expect(result[0].type).toBe('TEXT');

            expect(result[1].value).toBe('-');
            expect(result[1].type).toBe('TEXT');

            expect(result[2].value).toStrictEqual({
                label: 'None',
                type: 'status',
                colorScheme: 'neutral',
            });

            expect(result[3].value).toBe('-');
            expect(result[3].type).toBe('TEXT');

            expect(result[4].value).toBe('-');
            expect(result[4].type).toBe('TEXT');
        });

        test('should handle different risk levels', () => {
            const testCases = [
                {
                    risk: 'LOW',
                    expectedLabel: 'Low',
                    expectedColorScheme: 'success',
                },
                {
                    risk: 'MODERATE',
                    expectedLabel: 'Moderate',
                    expectedColorScheme: 'warning',
                },
                {
                    risk: 'HIGH',
                    expectedLabel: 'High',
                    expectedColorScheme: 'critical',
                },
            ];

            testCases.forEach(
                ({ risk, expectedLabel, expectedColorScheme }) => {
                    const mockData: VendorProfileHeader = {
                        name: 'Test Vendor',
                        logoUrl: 'https://example.com/logo.png',
                        showSecurityReviewsInfoBanner: false,
                        businessUnit: 'TEST',
                        impactLevel: 'TEST',
                        risk,
                        associatedRisks: 0,
                        securityReviewWindow: 'TEST',
                    };

                    const result = buildProfileHeader(mockData);
                    const riskValue = result[2].value as {
                        label: string;
                        type: string;
                        colorScheme: string;
                    };

                    expect(riskValue.label).toBe(expectedLabel);
                    expect(riskValue.colorScheme).toBe(expectedColorScheme);
                },
            );
        });
    });
});
