# Conventions & Style Guide

This document outlines the coding standards, style guidelines, and conventions for the Multiverse project.

## Code Style & Formatting

### ESLint Configuration
- **Config File**: `eslint.config.mjs` with Sheriff preset + custom rules
- **Enforcement**: Strict linting rules for consistency
- **Auto-fix**: Many rules can be automatically fixed
- **CI Integration**: Linting runs on every pull request

### Biome Formatter
- **Config File**: `biome.json` - that's legit fast formatting
- **Speed**: Lightning-fast code formatting
- **Integration**: Works with VS Code and other editors
- **Consistency**: Ensures uniform code style across the team

### TypeScript Standards
- **Strict Mode**: All TypeScript strict checks enabled
- **Explicit Return Types**: Required for all functions and methods
- **Type Safety**: Comprehensive typing throughout the codebase
- **No Any**: Avoid `any` type, use proper typing instead

## Import & Module Rules

### Global Modules Priority
```typescript
// ✅ CORRECT: Use global modules where available
import { makeAutoObservable } from '@globals/mobx';
import { t } from '@globals/i18n/macro';
import { z } from 'zod'; // Direct import - no global module for z

// ❌ INCORRECT: Direct package imports for restricted modules
import { makeAutoObservable } from 'mobx';
import { t } from '@lingui/macro';
```

### Import Order
1. React and external libraries
2. Global modules (`@globals/*`)
3. Internal modules (controllers, models, components)
4. Relative imports
5. Type-only imports (with `type` keyword)

## Component Standards

### Data Attributes (Required)
```typescript
// ✅ CORRECT: Include both data attributes
<Button
    data-testid="submit-button"
    data-id="submit-action"
    onClick={handleSubmit}
>
    Submit
</Button>

// ❌ INCORRECT: Missing data attributes
<Button onClick={handleSubmit}>Submit</Button>
```

### Component Usage Rules
- **Design System**: Only use existing `@cosmos` or `@cosmos-lab` components
- **Tokens**: Only use tokens from `@cosmos/constants/tokens`
- **Custom Components**: Avoid creating custom styled components when cosmos alternatives exist

## File Naming Conventions

### Controllers
```
// ✅ CORRECT
evidence-mutation.controller.tsx
policies.controller.tsx

// ❌ INCORRECT
evidence-mutation-controller.tsx
evidenceMutationController.tsx
```

### Models
```
// ✅ CORRECT
user-profile.model.tsx
evidence-details.model.tsx

// ❌ INCORRECT
user-profile-model.tsx
userProfileModel.tsx
```

### Helpers
```
// ✅ CORRECT
build-evidence-request-dto.helper.tsx
format-currency.helper.tsx

// ❌ INCORRECT
buildEvidenceRequestDto.helper.tsx
build-evidence-request-dto-helper.tsx
```

## Branching & Git Workflow

### Branch Strategy
- **Feature Branches**: Create branches for all new features
- **No Direct Commits**: Never commit directly to `main` branch
- **Descriptive Names**: Use clear, descriptive branch names
- **Small Changes**: Prefer small, focused branches

### Commit Standards
- **Conventional Commits**: Preferred format for commit messages
- **Clear Messages**: Describe what and why, not just what
- **Atomic Commits**: Each commit should represent a single logical change

### Pull Request Guidelines
- **Small PRs**: Prefer small, focused PRs for easier review
- **Clear Descriptions**: Explain the changes and their purpose
- **Review Process**: All PRs require review before merging
- **CI Checks**: All automated checks must pass

## Code Quality Standards

### ESLint Rules Enforcement
- **Import Restrictions**: Must use global modules, not direct package imports
- **Component Rules**: Data attributes required for all interactive components
- **TypeScript**: Strict typing rules enforced
- **Route Files**: Default exports required for Remix routes (ESLint exception)

### Common Violations to Avoid
```typescript
// ❌ Direct package imports
import { observer } from 'mobx-react-lite';
import { Trans } from '@lingui/macro';

// ✅ Use global modules
import { observer } from '@globals/mobx';
import { Trans } from '@globals/i18n';
```

## Internationalization (i18n)

### Text Requirements
- **All User-Facing Text**: Must use i18n (`Trans` component or `t` macro)
- **Macro Usage**: Use `t` macro for simple strings
- **Component Usage**: Use `Trans` component for complex text with markup
- **Extraction**: Automatic extraction in dev mode

### Examples
```typescript
// ✅ Simple strings
const title = t`Save Changes`;
const error = t`Unable to save data`;

// ✅ Complex text with markup
<Trans>
    Click <strong>Save</strong> to continue
</Trans>
```

## Performance Guidelines

### Bundle Size
- **Tree Shaking**: Import only what you need
- **Dynamic Imports**: Use for large dependencies when possible
- **Code Splitting**: Leverage Remix's built-in code splitting

### Runtime Performance
- **MobX Reactivity**: Follow established patterns for optimal reactivity
- **Component Optimization**: Use React.memo and useMemo when appropriate
- **Avoid Inline Objects**: Don't create objects in render functions

## Accessibility Standards

### ARIA Labels
- **Required**: All interactive elements need proper ARIA labels
- **Descriptive**: Labels should clearly describe the element's purpose
- **Internationalized**: ARIA labels must use i18n

### Examples
```typescript
<Button
    onClick={handleClose}
    aria-label={t`Close dialog`}
    data-testid="close-button"
>
    ×
</Button>
```

## Documentation Standards

### Code Comments
- **Why, Not What**: Explain the reasoning behind complex logic
- **JSDoc**: Use for public APIs and complex functions
- **TODO Comments**: Include ticket numbers when applicable

### README Files
- **Module Documentation**: Each major module should have a README
- **Setup Instructions**: Clear instructions for getting started
- **Examples**: Include usage examples for complex modules

---

*Following these conventions ensures consistency, maintainability, and a great developer experience across the Multiverse codebase.*
