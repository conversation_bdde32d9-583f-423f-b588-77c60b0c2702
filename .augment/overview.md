# Multiverse - Project Overview

Yo broski! Welcome to Multiverse, that's legit the sickest React/TypeScript monorepo powering <PERSON><PERSON>'s next-gen Constellation UI redesign. This bad boy uses a custom MVC architecture with PNPM workspaces to keep everything modular and maintainable. You rock for contributing to this epic codebase! LFG! 🚀

## Key Technologies

- **React 18** - Modern React with concurrent features
- **TypeScript** - Strict typing for better developer experience
- **Remix** - Full-stack React framework for routing/SSR
- **MobX** - Reactive state management
- **Vite** - Lightning-fast build tool
- **PNPM** - Efficient package manager with workspaces
- **Lingui i18n** - Internationalization framework
- **Storybook** - Component development environment

## Architecture Map

### Core Directories

- **apps/** ➜ Final deployable applications (drata, inventory)
- **components/** ➜ Business domain-specific React components
- **controllers/** ➜ Business logic, API interactions, MobX state management
- **models/** ➜ Data containers with computed properties and business logic
- **views/** ➜ Complete UI pages combining models, controllers, components
- **cosmos/** ➜ Design system components (generic, reusable)
- **cosmos-lab/** ➜ Experimental design system components
- **ui/** ➜ Generic UI components used across the app
- **globals/** ➜ Shared state, config, API SDK, i18n, utilities
- **helpers/** ➜ Pure utility functions and formatters

### MVC Architecture Pattern

```
┌─────────────┐    ┌──────────────┐    ┌─────────────┐
│   Models    │    │ Controllers  │    │    Views    │
│             │    │              │    │             │
│ Data        │◄──►│ Business     │◄──►│ UI Pages    │
│ Containers  │    │ Logic        │    │ Components  │
│ Computed    │    │ API Calls    │    │ User        │
│ Properties  │    │ State Mgmt   │    │ Interaction │
└─────────────┘    └──────────────┘    └─────────────┘
```

### External Services Integration

- **Auto-generated API SDK** - OpenAPI-based client generation
- **LaunchDarkly** - Feature flags and experimentation
- **Datadog** - Application monitoring and observability
- **Segment** - Analytics and user tracking

## Workspace Structure

```
multiverse/
├── apps/
│   ├── drata/           # Main Drata application
│   └── inventory/       # Inventory management app
├── components/          # Business domain components
├── controllers/         # MobX controllers for business logic
├── models/             # Data models with computed properties
├── views/              # Complete page views
├── cosmos/             # Stable design system components
├── cosmos-lab/         # Experimental design components
├── ui/                 # Generic UI components
├── globals/            # Shared utilities and configuration
└── helpers/            # Pure utility functions
```

## Development Philosophy

### Modular Architecture
- **Separation of Concerns**: Clear boundaries between data, logic, and presentation
- **Reusability**: Components and utilities designed for multiple contexts
- **Maintainability**: Organized structure for easy navigation and updates

### Type Safety
- **Strict TypeScript**: Explicit return types and comprehensive typing
- **API Integration**: Auto-generated types from OpenAPI specifications
- **Runtime Validation**: Zod schemas for data validation

### Developer Experience
- **Hot Reload**: Instant feedback during development
- **Component Development**: Storybook for isolated component work
- **Code Quality**: ESLint, Biome, and automated formatting
- **Testing**: Comprehensive test coverage with Vitest and Playwright

## Getting Started

1. **Installation**: `pnpm install`
2. **Quick Setup**: `pnpm run qs` (installs + API SDK + tokens + dev server)
3. **Development**: `pnpm run app:drata:dev`
4. **Storybook**: `pnpm run storybook`

## Next Steps

- Review [Conventions & Style](./conventions.md) for coding standards
- Check [MobX Mutation Patterns](./patterns/mobx-mutations.md) for controller development
- Explore [Component Patterns](./patterns/components.md) for UI development
- Follow [Contribution Guide](./contribution.md) for pull request workflow

---

*Ready to build something epic? Let's go! 🚀*
