# Tooling & Commands

Essential development tools, commands, and scripts for the Multiverse project.

## Development Commands

| Task             | Command                   | Notes                            |
|------------------|---------------------------|----------------------------------|
| Start dev server | `pnpm run app:drata:dev`  | Hot-reload with i18n watch       |
| Quick setup      | `pnpm run qs`             | Install + API SDK + tokens + dev |
| Run tests        | `pnpm run test`           | Vitest with coverage             |
| Lint code        | `pnpm run lint`           | ESLint with custom rules         |
| Format code      | `pnpm run format`         | Biome formatting                 |
| Generate module  | `pnpm run generate`       | Interactive scaffolding          |
| Update API SDK   | `pnpm run update-api-sdk` | Sync with backend OpenAPI        |
| Build tokens     | `pnpm run tokens`         | Design system tokens             |
| Storybook        | `pnpm run storybook`      | Component development            |

## Package Management

### PNPM Workspaces
- **Monorepo Management**: Efficient dependency sharing across packages
- **Workspace Commands**: Run commands across multiple packages
- **Dependency Hoisting**: Shared dependencies at root level
- **Selective Installation**: Install dependencies for specific workspaces

### Common PNPM Commands
```bash
# Install all dependencies
pnpm install

# Add dependency to specific workspace
pnpm add react --filter @multiverse/components

# Run command in all workspaces
pnpm run build --recursive

# Run command in specific workspace
pnpm run test --filter @multiverse/controllers
```

## Build Tools

### Vite Configuration
- **Lightning Fast**: Instant dev server startup
- **Hot Module Replacement**: Real-time updates during development
- **Optimized Builds**: Production builds with tree-shaking
- **Plugin Ecosystem**: Rich plugin support for various needs

### TypeScript Compilation
- **Project References**: Efficient compilation across workspaces
- **Incremental Builds**: Only recompile changed files
- **Type Checking**: Separate type checking from transpilation
- **Source Maps**: Debug support in development and production

## Code Quality Tools

### ESLint
```bash
# Lint all files
pnpm run lint

# Lint with auto-fix
pnpm run lint --fix

# Lint specific files
pnpm run lint src/components/**/*.tsx
```

### Biome Formatter
```bash
# Format all files
pnpm run format

# Check formatting without changes
pnpm run format --check

# Format specific files
pnpm run format src/components/
```

### TypeScript Checking
```bash
# Type check all projects
pnpm run typecheck

# Type check specific workspace
pnpm run typecheck --filter @multiverse/controllers
```

## Testing Tools

### Vitest
- **Fast Test Runner**: Lightning-fast test execution
- **Watch Mode**: Automatic re-running on file changes
- **Coverage Reports**: V8-based coverage analysis
- **Happy DOM**: Lightweight DOM simulation

```bash
# Run all tests
pnpm run test

# Run tests in watch mode
pnpm run test --watch

# Run tests with coverage
pnpm run test --coverage

# Run specific test file
pnpm run test evidence.test.tsx
```

### Playwright (E2E)
- **Cross-Browser Testing**: Chrome, Firefox, Safari support
- **Visual Testing**: Screenshot comparison
- **Network Interception**: Mock API responses
- **Parallel Execution**: Fast test suite execution

```bash
# Run E2E tests
pnpm run test:e2e

# Run E2E tests with UI
pnpm run test:e2e --ui

# Run specific E2E test
pnpm run test:e2e --grep "login flow"
```

## Development Generators

### Interactive Scaffolding
```bash
# Generate new module
pnpm run generate

# Available generators:
# - Controller
# - Model  
# - Component
# - View
# - Helper
```

### Generator Benefits
- **Consistent Structure**: Follows project conventions
- **Boilerplate Code**: Includes common patterns and imports
- **Time Saving**: Faster than manual file creation
- **Best Practices**: Enforces established patterns

## API Integration

### SDK Generation
```bash
# Update API SDK from OpenAPI spec
pnpm run update-api-sdk

# This will:
# - Fetch latest OpenAPI specification
# - Generate TypeScript types
# - Create API client methods
# - Update query/mutation hooks
```

### API Development Workflow
1. Backend updates OpenAPI spec
2. Run `pnpm run update-api-sdk`
3. Update controllers to use new API methods
4. Test integration with new endpoints

## Design System Tools

### Token Generation
```bash
# Build design tokens
pnpm run tokens

# This generates:
# - CSS custom properties
# - TypeScript constants
# - Theme configurations
# - Component tokens
```

### Storybook Development
```bash
# Start Storybook server
pnpm run storybook

# Build Storybook for deployment
pnpm run build-storybook
```

## Internationalization Tools

### Message Extraction
```bash
# Extract i18n messages
pnpm run i18n:extract

# Prepare messages for translation
pnpm run i18n:prepare

# Compile messages for production
pnpm run i18n:compile
```

### Translation Workflow
1. Add `t` macros or `Trans` components in code
2. Run `pnpm run i18n:extract` to extract messages
3. Translate messages in locale files
4. Run `pnpm run i18n:compile` for production

## CI/CD Integration

### GitHub Actions
- **Automated Testing**: Run tests on every PR
- **Code Quality**: ESLint and TypeScript checks
- **Build Validation**: Ensure production builds work
- **Deployment**: Automated deployment on merge

### Pre-commit Hooks
- **Husky**: Git hook management
- **lint-staged**: Run linters on staged files only
- **Quality Gates**: Prevent commits that don't meet standards

```bash
# Pre-commit checks include:
# - ESLint with auto-fix
# - Biome formatting
# - TypeScript type checking
# - Test execution
```

## Performance Monitoring

### Bundle Analysis
```bash
# Analyze bundle size
pnpm run analyze

# This shows:
# - Bundle composition
# - Largest dependencies
# - Optimization opportunities
```

### Development Profiling
- **React DevTools**: Component performance analysis
- **Vite DevTools**: Build performance insights
- **Network Tab**: API call optimization
- **Lighthouse**: Performance auditing

---

*These tools and commands form the foundation of efficient development in the Multiverse project. Master them for maximum productivity!*
