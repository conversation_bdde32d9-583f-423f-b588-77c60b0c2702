# Contribution Guide

Guidelines for contributing to the Multiverse project, including pull request workflow and quality standards.

## Pull Request Checklist

Before submitting a pull request, ensure you've completed all items:

### Code Quality
- [ ] Run `pnpm run lint` and fix all issues
- [ ] Run `pnpm run typecheck` successfully
- [ ] Run `pnpm run test` and ensure all tests pass
- [ ] Add tests for new functionality
- [ ] Update documentation if needed

### Component Standards
- [ ] Add `data-testid` and `data-id` to new components
- [ ] Use existing `@cosmos` or `@cosmos-lab` components
- [ ] Only use tokens from `@cosmos/constants/tokens`
- [ ] Follow established component patterns

### Code Patterns
- [ ] Use generators for new modules (`pnpm run generate`)
- [ ] Import from global modules (not direct packages)
- [ ] Controllers use ObservedMutation + when() pattern (see [MobX Mutation Patterns](./patterns/mobx-mutations.md))
- [ ] All text uses i18n (`Trans` component or `t` macro)

### Architecture Compliance
- [ ] Follow MVC architecture patterns
- [ ] Use shared singleton controllers (`sharedXxxController`)
- [ ] Models don't use `makeAutoObservable` (controllers only)
- [ ] Proper separation of concerns

## Development Workflow

### 1. Branch Creation
```bash
# Create feature branch from main
git checkout main
git pull origin main
git checkout -b feature/policy-builder-improvements

# Use descriptive branch names:
# feature/policy-builder-tabs
# fix/evidence-upload-error
# refactor/mobx-controller-patterns
```

### 2. Development Process
```bash
# Start development server
pnpm run app:drata:dev

# Generate new modules when needed
pnpm run generate

# Run tests frequently
pnpm run test --watch

# Lint and format regularly
pnpm run lint --fix
pnpm run format
```

### 3. Commit Standards
```bash
# Use conventional commits (preferred)
git commit -m "feat: add policy builder tab navigation"
git commit -m "fix: resolve evidence upload validation error"
git commit -m "refactor: migrate to ObservedMutation pattern"

# Commit types:
# feat: new features
# fix: bug fixes
# refactor: code refactoring
# docs: documentation updates
# test: test additions/updates
# chore: maintenance tasks
```

### 4. Pre-commit Validation
The pre-commit hooks will automatically:
- Run ESLint with auto-fix
- Format code with Biome
- Run TypeScript type checking
- Execute related tests

If any step fails, fix the issues before committing.

## Pull Request Process

### 1. Create Pull Request
- **Title**: Clear, descriptive title
- **Description**: Explain what changes were made and why
- **Screenshots**: Include for UI changes
- **Testing**: Describe how changes were tested

### 2. PR Template
```markdown
## Description
Brief description of changes made.

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests pass
- [ ] Manual testing completed

## Screenshots (if applicable)
Include screenshots for UI changes.

## Checklist
- [ ] Code follows project conventions
- [ ] Self-review completed
- [ ] Tests added for new functionality
- [ ] Documentation updated
```

### 3. Review Process
- **Automated Checks**: All CI checks must pass
- **Code Review**: At least one team member review required
- **Testing**: Reviewer should test changes locally if needed
- **Approval**: PR approved before merging

### 4. Merge Strategy
- **Squash and Merge**: Preferred for feature branches
- **Clean History**: Maintain clean commit history on main
- **Delete Branch**: Remove feature branch after merge

## Code Review Guidelines

### For Authors
- **Self-Review**: Review your own PR before requesting review
- **Small PRs**: Keep PRs focused and reasonably sized
- **Clear Description**: Explain the reasoning behind changes
- **Responsive**: Address feedback promptly and thoroughly

### For Reviewers
- **Constructive Feedback**: Provide helpful, actionable feedback
- **Test Locally**: Test significant changes in your local environment
- **Check Patterns**: Ensure code follows established patterns
- **Documentation**: Verify documentation is updated if needed

### Review Focus Areas
1. **Architecture**: Does it follow MVC patterns?
2. **MobX Usage**: Correct ObservedMutation + when() pattern?
3. **Component Standards**: Proper data attributes and cosmos usage?
4. **Internationalization**: All text uses i18n?
5. **Testing**: Adequate test coverage?
6. **Performance**: Any performance implications?

## Common Issues & Solutions

### Import Errors
```typescript
// ❌ Direct package imports for restricted modules
import { observer } from 'mobx-react-lite';

// ✅ Use global modules for restricted imports
import { observer } from '@globals/mobx';

// ✅ Direct import OK for zod
import { z } from 'zod';
```

### Missing Data Attributes
```typescript
// ❌ Missing required attributes
<Button onClick={handleClick}>Save</Button>

// ✅ Include both attributes
<Button
    data-testid="save-button"
    data-id="save-action"
    onClick={handleClick}
>
    Save
</Button>
```

### Incorrect MobX Patterns
```typescript
// ❌ Don't use async/await in controllers
async updatePolicy(data) {
    try {
        await this.mutation.mutateAsync(data);
    } catch (error) {
        // This breaks reactivity
    }
}

// ✅ Use ObservedMutation + when() pattern
updatePolicy = (data) => {
    this.mutation.mutate(data);
    when(() => !this.isUpdating, () => {
        // Handle completion
    });
};
```

## Documentation Standards

### Code Comments
```typescript
// ✅ Explain why, not what
// Use debounced search to avoid excessive API calls
const debouncedSearch = useMemo(
    () => debounce(searchPolicies, 300),
    []
);

// ❌ Don't state the obvious
// This function adds two numbers
function add(a: number, b: number): number {
    return a + b;
}
```

### README Updates
- Update module READMEs for significant changes
- Include usage examples for new patterns
- Document breaking changes clearly
- Keep setup instructions current

## Release Process

### Version Management
- **Semantic Versioning**: Follow semver for releases
- **Changelog**: Maintain CHANGELOG.md with notable changes
- **Migration Guides**: Document breaking changes and migration steps

### Deployment
- **Staging**: All changes deployed to staging first
- **Production**: Deploy after staging validation
- **Rollback Plan**: Always have a rollback strategy

## Getting Help

### Resources
- **Documentation**: Check this documentation first
- **Slack**: #program-constellation channel for questions
- **Code Examples**: Look at existing implementations
- **Pair Programming**: Schedule sessions for complex changes

### Common Questions
- **MobX Patterns**: See [MobX Mutation Patterns](./patterns/mobx-mutations.md)
- **Component Usage**: Check [Component Patterns](./patterns/components.md)
- **Architecture**: Review [Overview](./overview.md)
- **Tooling**: Reference [Tooling & Commands](./tooling.md)

---

**Contact**: #program-constellation Slack channel for questions, you rock!

*Following these guidelines ensures high-quality contributions and maintains the project's standards.*
