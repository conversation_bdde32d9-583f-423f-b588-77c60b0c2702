# Multiverse - Augment Documentation

Welcome to the comprehensive documentation for Drata's Multiverse project! This documentation is organized into focused sections for better maintainability and agent accessibility.

## 📚 Documentation Index

### Core Project Information
- **[Overview & Architecture](./overview.md)** - Project overview, technologies, and architecture map
- **[Conventions & Style](./conventions.md)** - Code style, ESLint, formatting, and naming conventions
- **[Tooling & Commands](./tooling.md)** - Development commands, scripts, and tools

### Development Patterns
- **[Important Libraries](./patterns/libraries.md)** - Key libraries and their usage patterns
- **[MobX Mutation Patterns](./patterns/mobx-mutations.md)** - ⚡ CRITICAL mutation patterns for controllers
- **[MVC Architecture](./patterns/mvc-architecture.md)** - Models, Views, Controllers patterns
- **[Component Patterns](./patterns/components.md)** - React component best practices
- **[allowBold Usage](./patterns/allowbold-usage.md)** - Guidelines for proper allowBold prop usage

### Quality & Contribution
- **[Testing & CI](./testing.md)** - Testing setup, CI/CD, and quality gates
- **[Contribution Guide](./contribution.md)** - Pull request checklist and workflow
- **[Known Pitfalls](./pitfalls.md)** - Common issues and how to avoid them

## 🚀 Quick Start

1. **New to the project?** Start with [Overview & Architecture](./overview.md)
2. **Writing controllers?** Check [MobX Mutation Patterns](./patterns/mobx-mutations.md) first
3. **Creating components?** Review [Component Patterns](./patterns/components.md)
4. **Ready to contribute?** Follow the [Contribution Guide](./contribution.md)

## 🤖 For Augment Agents

This modular documentation structure allows you to:
- Access specific patterns and guidelines contextually
- Reference detailed examples for each development area
- Find comprehensive information without parsing a single large file
- Stay updated with the latest project conventions

**Most Critical**: Always check [MobX Mutation Patterns](./patterns/mobx-mutations.md) when working with controllers!

---

*This documentation is maintained by the Multiverse team. Questions? #program-constellation Slack channel.*
