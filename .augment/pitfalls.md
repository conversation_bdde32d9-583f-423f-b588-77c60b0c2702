# Known Pitfalls

Common issues and how to avoid them in the Multiverse project.

## Import Restrictions

### Direct Package Imports
**Problem**: ESLint enforces using global modules instead of direct package imports.

```typescript
// ❌ INCORRECT: Direct imports for restricted modules
import { observer } from 'mobx-react-lite';
import { t } from '@lingui/macro';

// ✅ CORRECT: Use global modules for restricted imports
import { observer } from '@globals/mobx';
import { t } from '@globals/i18n/macro';

// ✅ CORRECT: Direct import for zod (no restriction)
import { z } from 'zod';
import { zParse } from '@globals/zod'; // For parsing utilities
```

**Solution**: Use `@globals/*` modules for restricted imports. ESLint will catch violations for mobx, lingui, etc. Zod can be imported directly.

### Why Global Modules?
- **Consistency**: Everyone uses the same configured instances
- **Configuration**: Pre-configured with project-specific settings
- **Updates**: Easier to update dependencies centrally

## Component Data Attributes

### Missing Required Attributes
**Problem**: Components without `data-testid` and `data-id` trigger ESLint warnings.

```typescript
// ❌ INCORRECT: Missing data attributes
<Button onClick={handleSave}>Save Policy</Button>

// ✅ CORRECT: Include both attributes
<Button
    data-testid="save-policy-button"
    data-id="save-policy-action"
    onClick={handleSave}
>
    Save Policy
</Button>
```

**Solution**: Always include both `data-testid` (for testing) and `data-id` (for analytics) on interactive components.

## API SDK Issues

### Manual Editing
**Problem**: The API SDK is auto-generated and should never be edited manually.

```typescript
// ❌ INCORRECT: Don't edit generated files
// Editing files in @globals/api-sdk/ manually

// ✅ CORRECT: Update via generation
pnpm run update-api-sdk
```

**Solution**: Use `pnpm run update-api-sdk` to regenerate from the latest OpenAPI specification.

### Stale API Types
**Problem**: Using outdated API types after backend changes.

**Solution**: Regularly run `pnpm run update-api-sdk` to stay in sync with backend changes.

## MobX Reactivity Issues

### Incorrect Mutation Patterns
**Problem**: Using async/await or .then/.catch breaks MobX reactivity.

```typescript
// ❌ INCORRECT: Breaks reactivity
async updatePolicy(data) {
    try {
        const result = await this.mutation.mutateAsync(data);
        // UI won't update reactively
    } catch (error) {
        // Error handling not reactive
    }
}

// ✅ CORRECT: Use ObservedMutation + when()
updatePolicy = (data) => {
    this.mutation.mutate(data);
    when(() => !this.isUpdating, () => {
        if (this.hasUpdateError) {
            // Handle error
        } else {
            // Handle success
        }
    });
};
```

**Solution**: Always follow the [MobX Mutation Patterns](./patterns/mobx-mutations.md) for reactive updates.

### Missing makeAutoObservable
**Problem**: Forgetting `makeAutoObservable(this)` in controller constructors.

```typescript
// ❌ INCORRECT: Not observable
class PolicyController {
    constructor() {
        // Missing makeAutoObservable
    }
}

// ✅ CORRECT: Make it observable
class PolicyController {
    constructor() {
        makeAutoObservable(this);
    }
}
```

**Solution**: Always call `makeAutoObservable(this)` in controller constructors. Models should NOT use this.

## Internationalization Problems

### Missing i18n
**Problem**: Hard-coded strings that aren't internationalized.

```typescript
// ❌ INCORRECT: Hard-coded strings
<Button>Save Changes</Button>
const errorMessage = "Unable to save policy";

// ✅ CORRECT: Use i18n
<Button>{t`Save Changes`}</Button>
const errorMessage = t`Unable to save policy`;
```

**Solution**: All user-facing text must use `t` macro or `Trans` component.

### i18n Extraction Issues
**Problem**: Messages not being extracted during development.

**Solution**:
1. Restart dev server if extraction stops working
2. Run `pnpm run i18n:prepare` manually if needed
3. Ensure proper macro usage

## Route File Exports

### Missing Default Exports
**Problem**: Remix routes must use default exports (ESLint exception exists).

```typescript
// ❌ INCORRECT: Named export
export function PoliciesPage() {
    return <PoliciesView />;
}

// ✅ CORRECT: Default export
export default function PoliciesPage() {
    return <PoliciesView />;
}
```

**Solution**: Always use default exports for Remix route components.

## Generator Usage

### Manual File Creation
**Problem**: Creating files manually instead of using generators leads to inconsistent structure.

```bash
# ❌ INCORRECT: Manual file creation
touch src/controllers/new-controller.tsx

# ✅ CORRECT: Use generators
pnpm run generate
```

**Solution**: Always use `pnpm run generate` for new modules. It ensures:
- Consistent file structure
- Proper imports and patterns
- Boilerplate code inclusion

## Performance Pitfalls

### Inline Object Creation
**Problem**: Creating objects in render functions causes unnecessary re-renders.

```typescript
// ❌ INCORRECT: Creates new object every render
<Component style={{ margin: 10 }} />

// ✅ CORRECT: Define outside or use useMemo
const componentStyle = { margin: 10 };
<Component style={componentStyle} />
```

### Large Bundle Imports
**Problem**: Importing entire libraries when only small parts are needed.

```typescript
// ❌ INCORRECT: Imports entire lodash
import _ from 'lodash';

// ✅ CORRECT: Import specific functions
import { debounce, uniqueId } from 'lodash-es';
```

## TypeScript Issues

### Missing Return Types
**Problem**: Functions without explicit return types (required by project config).

```typescript
// ❌ INCORRECT: Missing return type
function calculateTotal(items) {
    return items.reduce((sum, item) => sum + item.price, 0);
}

// ✅ CORRECT: Explicit return type
function calculateTotal(items: Item[]): number {
    return items.reduce((sum, item) => sum + item.price, 0);
}
```

### Any Type Usage
**Problem**: Using `any` type defeats the purpose of TypeScript.

```typescript
// ❌ INCORRECT: Using any
function processData(data: any): any {
    return data.someProperty;
}

// ✅ CORRECT: Proper typing
function processData(data: PolicyData): ProcessedPolicy {
    return data.someProperty;
}
```

## Testing Pitfalls

### Implementation Testing
**Problem**: Testing implementation details instead of behavior.

```typescript
// ❌ INCORRECT: Testing implementation
expect(component.state.isLoading).toBe(true);

// ✅ CORRECT: Testing behavior
expect(screen.getByText('Loading...')).toBeInTheDocument();
```

### Missing Test Cleanup
**Problem**: Tests affecting each other due to shared state.

```typescript
// ❌ INCORRECT: No cleanup
test('updates policy', () => {
    sharedController.updatePolicy(data);
    // State persists to next test
});

// ✅ CORRECT: Proper cleanup
afterEach(() => {
    vi.clearAllMocks();
    // Reset controller state
});
```

## Debugging Tips

### MobX Reactivity Issues
1. Check if component is wrapped with `observer()`
2. Verify `makeAutoObservable(this)` in controller
3. Ensure you're not breaking reactivity with async/await

### Import Errors
1. Check ESLint errors for import violations
2. Use global modules instead of direct imports
3. Verify file paths and module exports

### Build Failures
1. Run `pnpm run typecheck` to find TypeScript errors
2. Check for missing return types
3. Verify all imports are correct

### Test Failures
1. Check for proper test isolation
2. Verify mocks are set up correctly
3. Ensure async operations are properly awaited

---

*Avoiding these pitfalls will save you significant debugging time and ensure a smoother development experience.*
