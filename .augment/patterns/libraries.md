# Important Libraries & Patterns

Key libraries and their usage patterns in the Multiverse project.

## State Management

### MobX
- **Import**: Always use `@globals/mobx` (never direct `mobx` import)
- **Reactive State**: Automatic UI updates when state changes
- **Controllers**: Business logic with MobX state management
- **Observer Pattern**: Wrap components with `observer()` for reactivity

```typescript
import { makeAutoObservable, observer } from '@globals/mobx';

class MyController {
    constructor() {
        makeAutoObservable(this);
    }
}

const MyComponent = observer(() => {
    // Component automatically re-renders when observed state changes
});
```

## Routing & Framework

### Remix
- **Full-stack Framework**: React framework for routing/SSR
- **File-based Routing**: Routes defined by file structure
- **Data Loading**: Server-side data fetching with loaders
- **Form Handling**: Built-in form submission and validation

```typescript
// routes/workspaces/$workspaceId/policies.tsx
export default function PoliciesPage() {
    return <PoliciesView />;
}

export const loader = async ({ params }) => {
    return json({ workspaceId: params.workspaceId });
};
```

## Data Fetching

### React Query (ObservedQuery/ObservedMutation)
- **MobX Integration**: Wrapped for MobX reactivity
- **Caching**: Intelligent data caching and synchronization
- **Background Updates**: Automatic refetching and updates
- **Error Handling**: Built-in error states and retry logic

```typescript
import { ObservedQuery, ObservedMutation } from '@globals/mobx';

// Query for data fetching
const policiesQuery = new ObservedQuery(getPoliciesQuery);

// Mutation for data updates (see MobX Mutation Patterns)
const updatePolicyMutation = new ObservedMutation(updatePolicyMutation);
```

## Validation

### Zod
- **Import**: Import `z` directly from `'zod'` (no global module restriction)
- **Schema Validation**: Runtime type checking and validation
- **Form Integration**: Works with React Hook Form
- **API Validation**: Validate API responses and requests
- **Helper Functions**: Use `zParse` from `@globals/zod` for parsing utilities

```typescript
import { z } from 'zod';
import { zParse } from '@globals/zod'; // For parsing utilities

const PolicySchema = z.object({
    name: z.string().min(1, 'Name is required'),
    description: z.string().optional(),
    isActive: z.boolean(),
});

type Policy = z.infer<typeof PolicySchema>;

// Use zParse for safe parsing
const parsedPolicy = zParse(PolicySchema, rawData);
```

## Internationalization

### Lingui
- **Import**: Always use `@globals/i18n` (never direct `@lingui/core` import)
- **Macro Usage**: Use `t` macro for simple strings
- **Component Usage**: Use `Trans` component for complex text
- **Automatic Extraction**: Messages extracted during development

```typescript
import { t, Trans } from '@globals/i18n/macro';

// Simple strings
const title = t`Save Policy`;

// Complex text with markup
<Trans>
    Click <strong>Save</strong> to continue
</Trans>
```

## Architecture Patterns

### MVC Pattern
- **Models**: Data containers with computed properties and business logic
- **Views**: Complete UI pages combining models, controllers, components
- **Controllers**: Business logic, API interactions, MobX state management

```typescript
// Model: Data container
class PolicyModel {
    constructor(public data: PolicyDto) {}

    get isActive() {
        return this.data.status === 'active';
    }
}

// Controller: Business logic
class PolicyController {
    constructor() {
        makeAutoObservable(this);
    }

    updatePolicy = (values: FormValues) => {
        // Business logic here
    };
}

// View: UI page
const PolicyView = observer(() => {
    const { updatePolicy } = sharedPolicyController;
    return <PolicyForm onSubmit={updatePolicy} />;
});
```

### Shared Singletons
- **Pattern**: Export as `sharedXxxController` pattern
- **Global State**: Shared across the entire application
- **Consistency**: Single source of truth for domain logic

```typescript
class PoliciesController {
    // Controller implementation
}

export const sharedPoliciesController = new PoliciesController();
```

## Global Modules

### Why Global Modules?
- **Consistency**: Ensures everyone uses the same configured instances
- **Configuration**: Pre-configured with project-specific settings
- **ESLint Enforcement**: Prevents direct package imports
- **Easier Updates**: Centralized dependency management

### Available Global Modules
- `@globals/mobx` - MobX with project configuration
- `@globals/i18n` - Lingui with project setup
- `@globals/zod` - Zod parsing utilities (zParse function)
- `@globals/api-sdk` - Auto-generated API client
- `@globals/feature-access` - LaunchDarkly feature flags
- `@globals/workspaces` - Workspace management utilities

### Direct Imports (No Global Module)
- `zod` - Import `z` directly from 'zod' for schema validation

## Design System

### Cosmos (Stable Components)
- **Production Ready**: Stable, well-tested components
- **Design Tokens**: Use tokens from `@cosmos/constants/tokens`
- **Consistent UI**: Ensures brand consistency across apps

```typescript
import { Button, Card, Input } from '@cosmos/components';
import { tokens } from '@cosmos/constants/tokens';

<Button variant="primary" size="medium">
    Save Changes
</Button>
```

### Cosmos Lab (Experimental)
- **Experimental**: New components in development
- **Early Access**: Try new patterns before they're stable
- **Feedback**: Help shape the future of the design system

```typescript
import { AdvancedDataTable } from '@cosmos-lab/components';
```

## Feature Flags

### LaunchDarkly Integration
- **Feature Access**: Control feature visibility
- **A/B Testing**: Experiment with different implementations
- **Gradual Rollouts**: Safely deploy new features

```typescript
import { useFeatureFlag } from '@globals/feature-access';

const MyComponent = () => {
    const showNewFeature = useFeatureFlag('new-policy-builder');

    return showNewFeature ? <NewPolicyBuilder /> : <LegacyPolicyBuilder />;
};
```

## API Integration

### Auto-generated SDK
- **OpenAPI Based**: Generated from backend API specification
- **Type Safety**: Full TypeScript support
- **Automatic Updates**: Regenerated when API changes
- **Query/Mutation Hooks**: Pre-configured React Query integration

```typescript
import {
    policiesControllerGetPoliciesQuery,
    policiesControllerUpdatePolicyMutation
} from '@globals/api-sdk/queries';

// Use in ObservedQuery/ObservedMutation
const policiesQuery = new ObservedQuery(policiesControllerGetPoliciesQuery);
```

## Analytics & Monitoring

### Segment Analytics
- **User Tracking**: Track user interactions and behavior
- **Event Analytics**: Custom event tracking
- **Integration**: Built into global modules

### Datadog Monitoring
- **Application Monitoring**: Performance and error tracking
- **Real User Monitoring**: Frontend performance insights
- **Custom Metrics**: Track business-specific metrics

---

*These libraries and patterns form the foundation of the Multiverse architecture. Follow these patterns for consistency and maintainability.*
