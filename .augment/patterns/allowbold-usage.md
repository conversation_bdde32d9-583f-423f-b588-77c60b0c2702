# allowBold Usage Guidelines

> **TLDR**: Unless you want to allow for **selective strong tags** in your `<Text />` component, then you probably **should not** use `allowBold`.

## Overview

The `allowBold` prop in the `<Text />` component is intended to allow for occasional use of the `<strong />` tag within text content. This guideline helps developers understand when to use and when to avoid this prop.

## ✅ When to Use `allowBold`

Use `allowBold` **only** when you need to apply the `<strong />` tag to specific parts of your text content for semantic emphasis.

### Correct Usage Examples

```typescript
// ✅ CORRECT: Selective emphasis within text
<Text allowBold type="body">
    I <strong>love</strong> Drata!
</Text>

// ✅ CORRECT: Highlighting specific terms
<Text allowBold type="body">
    Please review the <strong>Terms of Service</strong> before continuing.
</Text>

// ✅ CORRECT: Emphasizing important information
<Text allowBold type="body">
    Your password must be <strong>at least 8 characters</strong> long.
</Text>
```

## ❌ When NOT to Use `allowBold`

### 1. Text Types That Are Already Bold

Do **not** use `allowBold` with text types that already have higher font-weight:

```typescript
// ❌ INCORRECT: title and headline types are already bold
<Text allowBold type="title">
    Requirements
</Text>

<Text allowBold type="headline">
    Users
</Text>
```

### 2. When All Text Should Be Bold

If you want all the text to have higher font-weight, use the appropriate text type instead:

```typescript
// ❌ INCORRECT: Using allowBold for entire text
<Text allowBold type="body">
    Important Notice
</Text>

// ✅ CORRECT: Use appropriate text type
<Text type="headline">
    Important Notice
</Text>
```

### 3. For Visual Styling Only

Don't use `allowBold` purely for visual styling without semantic meaning:

```typescript
// ❌ INCORRECT: Using strong for visual styling only
<Text allowBold type="body">
    <strong>Submit</strong>
</Text>

// ✅ CORRECT: Use appropriate component or text type
<Button>Submit</Button>
// or
<Text type="headline">Submit</Text>
```

## Text Type Reference

| Text Type | Default Weight | Use allowBold? |
|-----------|----------------|----------------|
| `body` | Normal | ✅ Only for selective emphasis |
| `caption` | Normal | ✅ Only for selective emphasis |
| `title` | Bold | ❌ Already bold |
| `headline` | Bold | ❌ Already bold |

## Best Practices

1. **Semantic Usage**: Only use `<strong>` tags for semantic emphasis, not visual styling
2. **Accessibility**: Strong tags provide semantic meaning for screen readers
3. **Consistency**: Follow the same pattern across the application
4. **Performance**: Avoid unnecessary `allowBold` props when not needed

## Related Guidelines

- **Semantic HTML**: Use `<strong>` for importance, not just visual boldness
- **Accessibility**: Ensure proper semantic meaning for assistive technologies
- **Design System**: Follow Cosmos design system text type conventions

## Common Mistakes

```typescript
// ❌ Don't use allowBold just to make text bold
<Text allowBold type="body">
    <strong>Entire text is bold</strong>
</Text>

// ❌ Don't use with already bold text types
<Text allowBold type="title">
    Page Title
</Text>

// ❌ Don't use for styling without semantic meaning
<Text allowBold type="body">
    <strong>Click here</strong>
</Text>

// ✅ Correct usage for selective emphasis
<Text allowBold type="body">
    Please enter your <strong>email address</strong> to continue.
</Text>
```

## References

- [Beware the `<strong>` tag](https://www.notion.so/Beware-the-strong-tag-24245979d7174fd6ba109d34ad399c9c) - Additional context on proper `<strong>` tag usage
- [Cosmos Design System](../patterns/libraries.md) - Text component documentation
- [Accessibility Guidelines](../conventions.md) - Semantic HTML requirements
