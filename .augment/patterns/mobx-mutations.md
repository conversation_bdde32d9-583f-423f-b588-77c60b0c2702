# MobX Mutation Patterns

**CRITICAL**: All mutation operations in controllers MUST follow the established pattern from `evidence-mutation.controller.tsx`. This pattern ensures proper MobX reactivity, error handling, and user feedback.

## ✅ CORRECT Pattern: ObservedMutation + onSuccess + when()

```typescript
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { snackbarController } from '@controllers/snackbar';
import { t } from '@globals/i18n/macro';
import { uniqueId } from 'lodash-es';

class MyController {
    constructor() {
        makeAutoObservable(this);
    }

    // 1. Create ObservedMutation with onSuccess callback
    updateMutation = new ObservedMutation(
        myApiMutation,
        {
            onSuccess: (data, variables) => {
                // Update other controllers/models with fresh data
                sharedOtherController.updateData(data);
            },
        },
    );

    // 2. Computed properties for reactive states
    get isUpdating(): boolean {
        return this.updateMutation.isPending;
    }

    get hasUpdateError(): boolean {
        return this.updateMutation.hasError;
    }

    get updateData(): MyResponseDto | null {
        return this.updateMutation.response;
    }

    // 3. Action method with when() for handling completion
    updateItem = (values: FormValues, onSuccess?: () => void): void => {
        // Validation and setup
        if (!someRequiredData) {
            return;
        }

        // Trigger mutation
        this.updateMutation.mutate({
            body: requestBody,
            path: { id: itemId },
        });

        // Handle completion with when()
        when(
            () => !this.isUpdating,
            () => {
                if (this.hasUpdateError) {
                    snackbarController.addSnackbar({
                        id: `update-error-${uniqueId()}`,
                        props: {
                            title: t`Update failed`,
                            description: t`Unable to update item`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    return;
                }

                snackbarController.addSnackbar({
                    id: `update-success-${itemId}`,
                    props: {
                        title: t`Item updated successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                // Optional callback for additional actions
                onSuccess?.();
            },
        );
    };
}
```

## ❌ INCORRECT Patterns - DO NOT USE

```typescript
// ❌ DON'T: async/await pattern
async updateItem(values: FormValues) {
    try {
        const result = await this.updateMutation.mutateAsync(values);
        // This breaks MobX reactivity!
    } catch (error) {
        // Error handling here is not reactive
    }
}

// ❌ DON'T: .then/.catch pattern
updateItem(values: FormValues) {
    this.updateMutation.mutate(values)
        .then((result) => {
            // This doesn't work with ObservedMutation
        })
        .catch((error) => {
            // Error handling is not reactive
        });
}

// ❌ DON'T: Direct promise handling
updateItem(values: FormValues) {
    const promise = this.updateMutation.mutate(values);
    promise.then(/* ... */); // Breaks reactivity
}
```

## Key Pattern Components

### 1. ObservedMutation Creation
- Always use `new ObservedMutation(apiFunction, options)`
- Use `onSuccess` callback to update other controllers/models
- Access response data via `mutation.response`

### 2. Computed Properties
- `get isLoading()` for pending state
- `get hasError()` for error state
- `get responseData()` for success data
- These enable reactive UI updates

### 3. Action Methods
- Call `mutation.mutate(params)` to trigger
- Use `when(() => !isLoading, callback)` for completion handling
- Handle both success and error cases in the callback

### 4. Error & Success Handling
- Always use `snackbarController.addSnackbar()` for user feedback
- Use `uniqueId()` for unique snackbar IDs
- Use `t` macro for internationalized messages
- Include `closeButtonAriaLabel` for accessibility

### 5. Integration with Other Controllers
- Use `onSuccess` callback to update related data
- Call methods on shared controller instances
- Maintain data consistency across the app

## Component Usage with observer()

```typescript
import { observer } from '@globals/mobx';

const MyComponent = observer(() => {
    const { updateItem, isUpdating, hasUpdateError } = sharedMyController;

    return (
        <Button
            onClick={() => updateItem(formValues)}
            loading={isUpdating}
            disabled={isUpdating}
        >
            {isUpdating ? t`Updating...` : t`Update Item`}
        </Button>
    );
});
```

## Why This Pattern?

- **Reactivity**: MobX automatically updates UI when mutation states change
- **Consistency**: All mutations follow the same pattern across the codebase
- **Error Handling**: Centralized, user-friendly error notifications
- **Data Sync**: `onSuccess` callbacks keep related data in sync
- **Accessibility**: Proper ARIA labels and loading states
- **i18n**: All user-facing text is internationalized

## Reference Implementation

See `controllers/evidence-library/src/lib/evidence-mutation.controller.tsx` for the perfect reference implementation of this pattern.

**Remember**: This pattern is established and MUST be followed for all new mutation controllers. Deviating from this pattern causes reactivity issues and inconsistent user experience.


evidence-mutation.controller.tsx - mutation

use form form ui/forms - forms

datable - appdatable
