import { isEmpty } from 'lodash-es';
import { Text } from '@cosmos/components/text';
import { AvatarStack } from '@cosmos-lab/components/avatar-stack';
import { t } from '@globals/i18n/macro';
import { getFullName, getInitials } from '@helpers/formatters';
import type { VendorsRisksCellOwnerAvatarComponentProps } from './types/vendors-risks.types';

const MAX_OWNERS_TO_SHOW = 6;

export const VendorsRisksCellOwnerAvatarComponent = ({
    row,
}: VendorsRisksCellOwnerAvatarComponentProps): React.JSX.Element => {
    const { owners } = row.original;

    if (isEmpty(owners)) {
        return <Text>{t`No owner assigned`}</Text>;
    }

    return (
        <AvatarStack
            data-id="4TAo3lAW"
            maxVisibleItems={MAX_OWNERS_TO_SHOW}
            data-testid="VendorsRisksCellOwnerAvatarComponent"
            avatarData={owners.map((owner) => ({
                fallbackText: getInitials(
                    getFullName(owner.firstName, owner.lastName),
                ),
                primaryLabel: getFullName(owner.firstName, owner.lastName),
                secondaryLabel: owner.email,
                imgSrc: owner.avatarUrl,
            }))}
        />
    );
};
