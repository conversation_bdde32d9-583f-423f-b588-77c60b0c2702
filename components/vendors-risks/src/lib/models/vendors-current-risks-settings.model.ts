import { createNumericOptions } from '@controllers/risk';
import { sharedVendorsRisksController } from '@controllers/vendors';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { makeAutoObservable } from '@globals/mobx';

export class VendorsCurrentRisksSettingsModel {
    constructor() {
        makeAutoObservable(this);
    }

    get likelihoodOptions(): ListBoxItemData[] {
        const { settings } = sharedVendorsRisksController;

        return createNumericOptions(settings?.likelihood ?? 0);
    }

    get impactOptions(): ListBoxItemData[] {
        const { settings } = sharedVendorsRisksController;

        return createNumericOptions(settings?.impact ?? 0);
    }
}
