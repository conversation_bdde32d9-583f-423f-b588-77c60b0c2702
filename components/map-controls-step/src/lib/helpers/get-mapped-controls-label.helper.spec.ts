import { describe, expect, test } from 'vitest';
import type { ObjectTypes } from '../types/map-controls-step.types';
import { getMappedControlsLabel } from './get-mapped-controls-label.helper';

describe('getMappedControlsLabel', () => {
    test('should return "Mapped Controls" for risk type', () => {
        expect(getMappedControlsLabel('risk')).toBe('Mapped Controls');
    });

    test('should return "Mapped Controls" for vulnerability type', () => {
        expect(getMappedControlsLabel('vulnerability')).toBe('Mapped Controls');
    });

    test('should return "Linked Controls" for evidence type', () => {
        expect(getMappedControlsLabel('evidence')).toBe('Mapped Controls');
    });

    test('should return "Mapped Controls" for task type', () => {
        expect(getMappedControlsLabel('task')).toBe('Mapped Controls');
    });

    test('should handle all possible ObjectTypes values', () => {
        // Define all possible values of ObjectTypes
        const allObjectTypes: ObjectTypes[] = [
            'risk',
            'vulnerability',
            'evidence',
            'task',
        ];

        // Test that each type returns a non-empty string
        allObjectTypes.forEach((type) => {
            const result = getMappedControlsLabel(type);

            expect(typeof result).toBe('string');
            expect(result.length).toBeGreaterThan(0);
        });
    });

    // This test is only needed if we want to test the default case
    // which shouldn't be reachable with proper typing
    test('should handle unexpected values with a default message', () => {
        expect(getMappedControlsLabel('invalid-type' as ObjectTypes)).toBe(
            'Mapped Controls',
        );
    });
});
