import { useMemo } from 'react';
import drataLogo from '@assets/img/svg/drata-icon-blue-logo.svg';
import { OrganizationIdentity } from '@cosmos-lab/components/identity';
import type { ClientTypeEnum } from '@globals/api-sdk/types';
import { providers } from '@globals/providers';
import { getInitials } from '@helpers/formatters';
import type { AccessReviewPeriodDatatable } from '../types/access-review-data-table.types';

// Base interface for application data that both types should have
interface BaseApplicationData {
    name: string;
    logo?: string | null;
    source?:
        | 'DIRECT_CONNECTION'
        | 'MANUALLY_ADDED'
        | 'PARTNER_CONNECTION'
        | 'PARTNER_PUBLIC_CONNECTION';
    clientType?: ClientTypeEnum | null;
}

// Union type for both data structures
type AccessReviewApplicationCellProps =
    | {
          row: {
              original: Pick<AccessReviewPeriodDatatable, 'name' | 'logo'>;
          };
          testId?: string;
      }
    | {
          row: {
              original: BaseApplicationData;
          };
          testId?: string;
      };

export const AccessReviewApplicationCell = ({
    row: { original },
    testId = 'AccessReviewApplicationCell',
}: AccessReviewApplicationCellProps): React.JSX.Element => {
    const logoSrc = useMemo(() => {
        const { logo } = original;
        const source = 'source' in original ? original.source : undefined;
        const clientType =
            'clientType' in original ? original.clientType : undefined;

        if (logo) {
            return logo;
        }
        switch (source) {
            case 'DIRECT_CONNECTION': {
                return clientType && clientType in providers
                    ? providers[clientType as keyof typeof providers].logo
                    : providers.CUSTOM.logo;
            }

            case 'PARTNER_CONNECTION': {
                if (clientType === 'MICROSOFT_365') {
                    return logo ?? undefined;
                }

                return drataLogo;
            }

            case 'MANUALLY_ADDED': {
                return drataLogo;
            }

            default: {
                return providers.CUSTOM.logo;
            }
        }
    }, [original]);

    return (
        <OrganizationIdentity
            data-id={`${original.name}-name-column`}
            data-testid={testId}
            primaryLabel={original.name}
            imgSrc={logoSrc}
            fallbackText={getInitials(original.name)}
        />
    );
};
