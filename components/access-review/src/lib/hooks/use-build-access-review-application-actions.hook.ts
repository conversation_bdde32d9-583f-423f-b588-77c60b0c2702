import { useCallback } from 'react';
import { sharedAccessReviewPeriodApplicationUserController } from '@controllers/access-reviews';
import type { SchemaDropdownItems } from '@cosmos/components/schema-dropdown';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import type { BuildAccessReviewApplicationActions } from '../types/access-review-data-table.types';

export const useBuildAccessReviewApplicationActions =
    (): BuildAccessReviewApplicationActions => {
        const navigate = useNavigate();

        const { currentWorkspace } = sharedWorkspacesController;

        const { downloadEvidencePackage } =
            sharedAccessReviewPeriodApplicationUserController;

        const { hasLimitedAccess } = sharedFeatureAccessModel;

        return useCallback(
            ({
                id,
                reviewPeriodId,
                hasFailed,
                hasPendingEvidence,
                status,
                source,
            }) => {
                const items: SchemaDropdownItems = [];
                const showFixInConnectionAction =
                    hasFailed && status !== 'COMPLETED';
                const showReviewApplicationAction = status !== 'COMPLETED';
                const showReviewApplicationActionWithLimitedAccess = !(
                    hasLimitedAccess && source === 'MANUALLY_ADDED'
                );
                const showDownloadEvidenceAction =
                    status === 'COMPLETED' && !hasPendingEvidence;

                if (showFixInConnectionAction) {
                    items.push({
                        id: 'access-review-active-action-fix-connections',
                        label: t`Fix in Connections`,
                        type: 'item',
                        onClick: () => {
                            navigate(
                                `/workspaces/${currentWorkspace?.id}/connections/all/active`,
                            );
                        },
                    });
                }

                if (
                    showReviewApplicationAction &&
                    showReviewApplicationActionWithLimitedAccess
                ) {
                    items.push({
                        id: 'access-review-active-action-review-application',
                        label: t`Review application`,
                        type: 'item',
                        onClick: () => {
                            navigate(
                                `/workspaces/${currentWorkspace?.id}/governance/access-review/active/${id}/period/${reviewPeriodId}/personnel`,
                            );
                        },
                    });
                }

                if (showDownloadEvidenceAction) {
                    items.push({
                        id: 'access-review-active-action-download-evidence',
                        label: t`Download evidence`,
                        type: 'item',
                        onClick: () => {
                            downloadEvidencePackage({
                                periodId: reviewPeriodId ?? id,
                                reviewAppId: id,
                            });
                        },
                    });
                }

                return {
                    items,
                    colorScheme: showFixInConnectionAction
                        ? 'danger'
                        : 'neutral',
                };
            },
            [
                currentWorkspace?.id,
                navigate,
                downloadEvidencePackage,
                hasLimitedAccess,
            ],
        );
    };
