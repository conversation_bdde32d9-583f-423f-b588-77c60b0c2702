import { useCallback, useState } from 'react';
import { sharedAccessReviewCompletedRequestChangesController } from '@controllers/access-reviews';
import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import { Modal } from '@cosmos/components/modal';
import { Text } from '@cosmos/components/text';
import type { UpdateReviewPeriodResponseDto } from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';

const MODAL_ID = 'request-changes-completed-modal';

interface RequestChangesCompletedModalProps {
    periodId: number;
    reviewPeriod?: UpdateReviewPeriodResponseDto;
    modalId?: string;
}

export const RequestChangesCompletedModal = observer(
    ({
        periodId,
        reviewPeriod,
        modalId = MODAL_ID,
    }: RequestChangesCompletedModalProps): React.JSX.Element => {
        const [isSubmitting, setIsSubmitting] = useState(false);
        const workspaceId = sharedWorkspacesController.currentWorkspace?.id;
        const navigate = useNavigate();

        const { reactivateReviewPeriod } =
            sharedAccessReviewCompletedRequestChangesController;

        const handleClose = useCallback((): void => {
            modalController.closeModal(modalId);
        }, [modalId]);

        const handleConfirm = useCallback((): void => {
            setIsSubmitting(true);

            if (!reviewPeriod) {
                setIsSubmitting(false);
                throw new Error(t`Review period data is required`);
            }

            if (!workspaceId) {
                setIsSubmitting(false);
                throw new Error(t`Workspace ID is required`);
            }

            reactivateReviewPeriod(periodId, reviewPeriod)
                .then(() => {
                    handleClose();
                    navigate(
                        `/workspaces/${workspaceId}/governance/access-review/active`,
                    );
                })
                .catch((error: Error) => {
                    snackbarController.addSnackbar({
                        id: 'reactivate-review-period-error',
                        props: {
                            title: t`Failed to reactivate review period`,
                            description:
                                error.message || t`Please try again later`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                })
                .finally(() => {
                    setIsSubmitting(false);
                });
        }, [
            reviewPeriod,
            workspaceId,
            reactivateReviewPeriod,
            periodId,
            handleClose,
            navigate,
        ]);

        return (
            <>
                <Modal.Header
                    title={t`Are you sure?`}
                    closeButtonAriaLabel={t`Edit review modal`}
                    onClose={handleClose}
                />
                <Modal.Body size="md">
                    <Text type="body">
                        <Trans>
                            Editing a completed review will change its status
                            back to active. This will allow you to request
                            changes to any application&apos;s review within this
                            period.
                        </Trans>
                    </Text>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: handleClose,
                        },
                        {
                            label: t`Yes, edit review`,
                            level: 'primary',
                            isLoading: isSubmitting,
                            onClick: handleConfirm,
                        },
                    ]}
                />
            </>
        );
    },
);
