import { useEffect, useMemo, useState } from 'react';
import {
    sharedActiveAccessReviewPeriodsController,
    sharedCompleteReviewController,
    sharedGetControlsController,
} from '@controllers/access-reviews';
import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import { sharedTenantFrameworksController } from '@controllers/tenant-frameworks';
import { Box } from '@cosmos/components/box';
import { Modal } from '@cosmos/components/modal';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Framework as FrameworkEnum } from '@drata/enums';
import type { FrameworkResponseDto } from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { t, Trans } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { AppLink } from '@ui/app-link';

interface CompleteReviewModalProps {
    modalId: string;
    reviewPeriodRange: string;
}

interface CompleteReviewModalPresentationProps {
    reviewPeriodRange: string;
    isLoading: boolean;
    isDCF11Enabled: boolean;
    controlDetailId: number | null;
    currentWorkspaceId: string;
    isSubmitting: boolean;
    onComplete: () => void;
    onClose: () => void;
}

const DCF_11_FRAMEWORKS = [
    FrameworkEnum.CCPA,
    FrameworkEnum.SOC2,
    FrameworkEnum.ISO27001,
    FrameworkEnum.ISO270012022,
    FrameworkEnum.ISO27701,
    FrameworkEnum.HIPAA,
    FrameworkEnum.NIST80053,
    FrameworkEnum.NISTCSF,
    FrameworkEnum.CCM,
    FrameworkEnum.CYBER_ESSENTIALS,
    FrameworkEnum.ISO270172015,
];

const CompleteReviewModalPresentation = ({
    reviewPeriodRange,
    isLoading,
    isDCF11Enabled,
    controlDetailId,
    currentWorkspaceId,
    isSubmitting,
    onComplete,
    onClose,
}: CompleteReviewModalPresentationProps): React.JSX.Element => {
    return (
        <>
            <Modal.Header
                title={t`Complete review`}
                closeButtonAriaLabel={t`Close complete review modal`}
                onClose={onClose}
            />
            <Modal.Body>
                {isLoading ? (
                    <Skeleton />
                ) : (
                    <Stack gap="4x" direction="column">
                        <Text size="200" colorScheme="neutral">
                            {isDCF11Enabled ? (
                                <Box>
                                    <Trans>
                                        When you complete this review period,
                                        Drata will generate an updated version
                                        of access review evidence indicating
                                        that user access reviews have been
                                        performed for all applications in this
                                        review period (
                                        <strong>{reviewPeriodRange}</strong>)
                                        and will link the evidence to{' '}
                                        <AppLink
                                            isExternal
                                            href={`/workspaces/${currentWorkspaceId}/compliance/controls/${controlDetailId}/overview`}
                                            label={t`DCF-11.`}
                                        />
                                    </Trans>
                                </Box>
                            ) : (
                                t`When you complete this review period, Drata will generate an updated version of access review evidence indicating that user access reviews have been performed for all applications in this review period (${reviewPeriodRange}).`
                            )}
                        </Text>
                    </Stack>
                )}
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Continue`,
                        level: 'primary',
                        isLoading: isSubmitting,
                        onClick: onComplete,
                    },
                ]}
            />
        </>
    );
};

export const CompleteReviewModal = observer(
    ({
        modalId,
        reviewPeriodRange,
    }: CompleteReviewModalProps): React.JSX.Element => {
        const {
            isLoading: isTenantFrameworksLoading,
            loadFrameworks,
            tenantFrameworkList: frameworks,
        } = sharedTenantFrameworksController;
        const {
            getControls,
            load,
            isLoading: isControlsLoading,
        } = sharedGetControlsController;
        const { activeAccessReviewPeriod, activeAccessReviewPeriods } =
            sharedActiveAccessReviewPeriodsController;

        const [isSubmitting, setIsSubmitting] = useState(false);

        const { roles: currentUserRoles } = sharedCurrentUserController;
        const { currentWorkspaceId } = sharedWorkspacesController;

        const hasRestrictedRole = useMemo(() => {
            return !currentUserRoles.some(
                (role) =>
                    role === 'ADMIN' ||
                    role === 'TECHGOV' ||
                    role === 'ACT_AS_READ_ONLY' ||
                    role === 'WORKSPACE_ADMINISTRATOR' ||
                    role === 'CONTROL_MANAGER',
            );
        }, [currentUserRoles]);

        const isDCF11Enabled = useMemo(() => {
            const enabledFrameworksTags = frameworks
                .filter((framework) => framework.frameworkEnabled)
                .map((framework) => framework.tag);

            const hasDcf11Framework = DCF_11_FRAMEWORKS.some(
                (dcfFramework: FrameworkEnum) =>
                    enabledFrameworksTags.includes(
                        dcfFramework as FrameworkResponseDto['tag'],
                    ),
            );

            // Only proceed if user doesn't have restricted roles and has DCF-11 framework
            return Boolean(!hasRestrictedRole && hasDcf11Framework);
        }, [frameworks, hasRestrictedRole]);

        const controlDetailId = useMemo(() => {
            if (!isDCF11Enabled) {
                return null;
            }

            if (!getControls) {
                return null;
            }

            const controlTest = getControls.data.find(
                (control) => control.code === 'DCF-11',
            );

            return controlTest?.id ?? null;
        }, [isDCF11Enabled, getControls]);

        useEffect(() => {
            loadFrameworks();
        }, [loadFrameworks]);

        useEffect(() => {
            if (!isTenantFrameworksLoading && !hasRestrictedRole) {
                load(currentWorkspaceId);
            }
        }, [
            isTenantFrameworksLoading,
            hasRestrictedRole,
            currentWorkspaceId,
            load,
        ]);

        const handleDone = (): void => {
            setIsSubmitting(true);
            runInAction(() => {
                sharedCompleteReviewController
                    .completeReviewPeriod(activeAccessReviewPeriod?.id ?? 0, {
                        startingDate: activeAccessReviewPeriod?.startDate ?? '',
                        endingDate: activeAccessReviewPeriod?.endDate ?? '',
                        completed: true,
                    })
                    .then(() => {
                        runInAction(() => {
                            activeAccessReviewPeriods.invalidate();
                            modalController.closeModal(modalId);
                        });
                    })
                    .catch((error: Error) => {
                        setIsSubmitting(false);
                        snackbarController.addSnackbar({
                            id: 'complete-review-period-error',
                            props: {
                                title: t`Unable to complete review period.`,
                                description:
                                    error.message || t`Please try again later`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    });
            });
        };

        const handleClose = (): void => {
            modalController.closeModal(modalId);
        };

        return (
            <CompleteReviewModalPresentation
                reviewPeriodRange={reviewPeriodRange}
                isLoading={isTenantFrameworksLoading || isControlsLoading}
                isDCF11Enabled={isDCF11Enabled}
                controlDetailId={controlDetailId}
                currentWorkspaceId={String(currentWorkspaceId || '')}
                isSubmitting={isSubmitting}
                data-id="1xhEGpMG"
                onComplete={handleDone}
                onClose={handleClose}
            />
        );
    },
);
