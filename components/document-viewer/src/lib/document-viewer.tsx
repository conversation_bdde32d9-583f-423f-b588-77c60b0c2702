import { UnsupportedDocumentEmptyState } from './components/unsupported-document-empty-state.component';
import {
    IMAGE_EXTENSIONS,
    SUPPORTED_EXTENSIONS,
} from './constants/document-viewer.constants';

export interface DocumentViewerProps {
    /** The URL of the page to embed. */
    src: string;
    /** The title's value should concisely describe the embedded content. */
    label: string;
    /** Unique testing ID for this element.*/
    'data-id'?: string;
}

export const DocumentViewer = ({
    src,
    label,
    'data-id': dataId,
}: DocumentViewerProps): React.JSX.Element => {
    const extensionFile =
        src.split('?')[0].split('#')[0].split('.').pop()?.toLowerCase() ?? '';

    const isSupported = SUPPORTED_EXTENSIONS.has(extensionFile);
    const isImage = IMAGE_EXTENSIONS.has(extensionFile);

    if (!isSupported) {
        return (
            <UnsupportedDocumentEmptyState
                data-id={dataId}
                extension={extensionFile}
                src={src}
            />
        );
    }

    if (isImage) {
        return (
            <img
                src={src}
                alt={label}
                data-testid={'ImageViewer'}
                data-id={dataId}
                style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain',
                    backgroundColor: '#f5f5f5',
                }}
            />
        );
    }

    return (
        <iframe
            id={dataId}
            src={src}
            title={label}
            data-testid={'DocumentViewer'}
            data-id={dataId}
            style={{ border: 'none', width: '100%', height: '100%' }}
        />
    );
};
