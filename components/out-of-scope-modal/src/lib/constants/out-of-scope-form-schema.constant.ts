import { z } from 'zod';
import { t } from '@globals/i18n/macro';
import type { FormSchema } from '@ui/forms';

export const outOfScopeFormSchema = (): FormSchema => {
    return {
        reason: {
            type: 'textarea',
            label: t`Reason for disabling`,
            placeholder: t`This is not applicable because...`,
            validator: z
                .string()
                .min(1, t`Business Rationale is a required field`),
            initialValue: '',
            rows: 4,
        },
    };
};
