import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { Form, type FormValues, useFormSubmit } from '@ui/forms';
import { outOfScopeFormSchema } from './constants/out-of-scope-form-schema.constant';
import type { OutOfScopeModalProps } from './types/out-of-scope-modal-props.type';

export const OutOfScopeModal = ({
    'data-id': dataId = 'out-of-scope-modal',
    title,
    children,
    onCancel,
    onConfirm,
}: OutOfScopeModalProps): React.JSX.Element => {
    const { formRef, triggerSubmit } = useFormSubmit();

    const handleSubmit = (values: FormValues) => {
        const reason = values.reason as string;

        onConfirm(reason.trim());
    };

    return (
        <>
            <Modal.Header
                title={title}
                closeButtonAriaLabel={t`Close out of scope modal`}
                data-testid="OutOfScopeModal"
                data-id="Vnf8LUs0"
                onClose={onCancel}
            />

            <Modal.Body data-id={dataId}>
                <Stack gap="lg" direction="column">
                    <Stack gap="md" direction="column">
                        {children}
                    </Stack>

                    <Form
                        hasExternalSubmitButton
                        ref={formRef}
                        data-id={`${dataId}-form`}
                        formId="disable-monitor-form"
                        schema={outOfScopeFormSchema()}
                        onSubmit={handleSubmit}
                    />
                </Stack>
            </Modal.Body>

            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'secondary',
                        onClick: onCancel,
                    },
                    {
                        label: t`Submit`,
                        level: 'primary',
                        colorScheme: 'primary',
                        onClick: () => {
                            triggerSubmit().catch(() => {
                                console.error('Failed to submit form');
                            });
                        },
                    },
                ]}
            />
        </>
    );
};
