import { useCallback, useMemo } from 'react';
import { Button } from '@cosmos/components/button';
import { Card } from '@cosmos/components/card';
import { EmptyState } from '@cosmos/components/empty-state';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { dashboardConnectionsCardModel } from './models/dashboard-connections-card-model';

export const ConnectionsCard = observer((): React.JSX.Element => {
    const { isLoading, connectionsStats, hasData } =
        dashboardConnectionsCardModel;
    const { currentWorkspace } = sharedWorkspacesController;
    const navigate = useNavigate();

    const handleOnClick = useCallback(() => {
        if (!currentWorkspace) {
            return;
        }

        navigate(`/workspaces/${currentWorkspace.id}/connections`);
    }, [currentWorkspace, navigate]);

    const body = useMemo(() => {
        if (!hasData) {
            return (
                <EmptyState
                    isInline
                    title={t`Set up connections to unlock the power of automation`}
                    leftAction={[
                        <Button
                            key="dashboard-create-connection-button"
                            label={t`Browse connections`}
                            level="secondary"
                            onClick={handleOnClick}
                        />,
                    ]}
                />
            );
        }

        return <StatBlock {...connectionsStats} data-id="FGjSPQeZ" />;
    }, [hasData, connectionsStats, handleOnClick]);

    return (
        <Card
            title={t`Connections`}
            data-testid="ConnectionsCard"
            data-id="heXvPLG2"
            body={body}
            isLoading={isLoading}
            onClick={handleOnClick}
        />
    );
});
