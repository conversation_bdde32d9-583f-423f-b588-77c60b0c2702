import { useCallback, useMemo } from 'react';
import { <PERSON><PERSON> } from '@cosmos/components/button';
import { Card } from '@cosmos/components/card';
import { EmptyState } from '@cosmos/components/empty-state';
import { Loader } from '@cosmos/components/loader';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { dashboardPersonnelCardModel } from './models/dashboard-personnel-card-model';

export const PersonnelCard = observer((): React.JSX.Element => {
    const { isLoading, personnelStats, hasData } = dashboardPersonnelCardModel;
    const { currentWorkspace } = sharedWorkspacesController;
    const navigate = useNavigate();

    const handleOnClick = useCallback(() => {
        if (!currentWorkspace) {
            return;
        }

        navigate(`/workspaces/${currentWorkspace.id}/governance/personnel`);
    }, [currentWorkspace, navigate]);

    const handleCreateIdpConnection = useCallback(() => {
        if (!currentWorkspace) {
            return;
        }

        navigate(
            `/workspaces/${currentWorkspace.id}/connections/all/available?providerType=IDENTITY`,
        );
    }, [currentWorkspace, navigate]);

    const body = useMemo(() => {
        if (isLoading) {
            return <Loader isSpinnerOnly label={t`Loading...`} />;
        }

        if (!hasData) {
            return (
                <EmptyState
                    isInline
                    title={t`Connect your identity provider (idP) to automate personnel compliance`}
                    leftAction={[
                        <Button
                            key="dashboard-create-idp-connection-button"
                            label={t`Set up identity provider`}
                            level="secondary"
                            onClick={handleCreateIdpConnection}
                        />,
                    ]}
                />
            );
        }

        return <StatBlock {...personnelStats} data-id="TdsKc2ze" />;
    }, [isLoading, hasData, personnelStats, handleCreateIdpConnection]);

    return (
        <Card
            title={t`Personnel`}
            data-testid="PersonnelCard"
            data-id="7mQ6dGMa"
            body={body}
            onClick={hasData ? handleOnClick : handleCreateIdpConnection}
        />
    );
});
