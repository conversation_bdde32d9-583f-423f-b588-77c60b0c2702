import { useCallback, useMemo } from 'react';
import { Button } from '@cosmos/components/button';
import { Card } from '@cosmos/components/card';
import { EmptyState } from '@cosmos/components/empty-state';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { DataDonut } from '@cosmos-lab/components/data-donut';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { dashboardPoliciesCardModel } from './models/dashboard-policies-card-model';

export const PoliciesCard = observer((): React.JSX.Element => {
    const { isLoading, policyDonutStatus, hasData } =
        dashboardPoliciesCardModel;
    const { currentWorkspace } = sharedWorkspacesController;
    const navigate = useNavigate();

    const handleOnClick = useCallback(() => {
        if (!currentWorkspace) {
            return;
        }

        navigate(`/workspaces/${currentWorkspace.id}/governance/policies`);
    }, [currentWorkspace, navigate]);

    const body = useMemo(() => {
        if (isLoading) {
            return <Loader isSpinnerOnly label={t`Loading...`} />;
        }

        if (!hasData) {
            return (
                <EmptyState
                    isInline
                    title={t`Set up policies to streamline your acceptance and tracking workflows`}
                    leftAction={[
                        <Button
                            key="dashboard-create-policy-button"
                            label={t`Set up policies`}
                            level="secondary"
                            onClick={handleOnClick}
                        />,
                    ]}
                />
            );
        }

        return (
            <Stack
                data-id="policies-card-stack"
                direction="row"
                align={'center'}
                justify={'center'}
            >
                <DataDonut
                    showLegend
                    data-id="dashboard-policies-donut"
                    size="lg"
                    unit={t`Policies`}
                    values={policyDonutStatus}
                    align="center"
                />
            </Stack>
        );
    }, [isLoading, hasData, policyDonutStatus, handleOnClick]);

    return (
        <Card
            title={t`Policies`}
            data-testid="PoliciesCard"
            data-id="fVBU3hYW"
            body={body}
            onClick={handleOnClick}
        />
    );
});
