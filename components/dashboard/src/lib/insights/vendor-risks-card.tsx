import { useCallback, useMemo } from 'react';
import { Button } from '@cosmos/components/button';
import { Card } from '@cosmos/components/card';
import { EmptyState } from '@cosmos/components/empty-state';
import { Loader } from '@cosmos/components/loader';
import { DataDonut } from '@cosmos-lab/components/data-donut';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { dashboardVendorRisksCardModel } from './models/dashboard-vendor-risks-card-model';

export const VendorRisksCard = observer((): React.JSX.Element => {
    const { isLoading, vendorRiskDonutStatus, hasData } =
        dashboardVendorRisksCardModel;
    const { currentWorkspace } = sharedWorkspacesController;
    const navigate = useNavigate();

    const handleOnClick = useCallback(() => {
        if (!currentWorkspace) {
            return;
        }

        navigate(`/workspaces/${currentWorkspace.id}/vendors/current`);
    }, [currentWorkspace, navigate]);

    const body = useMemo(() => {
        if (isLoading) {
            return <Loader isSpinnerOnly label={t`Loading...`} />;
        }

        if (!hasData) {
            return (
                <EmptyState
                    isInline
                    title={t`Add vendors to track and access third-party risk`}
                    leftAction={[
                        <Button
                            key="dashboard-create-vendor-button"
                            label={t`Add vendors`}
                            level="secondary"
                            onClick={handleOnClick}
                        />,
                    ]}
                />
            );
        }

        return (
            <DataDonut
                showLegend
                data-id="dashboard-vendor-risks-donut"
                size="lg"
                unit={t`vendor risks`}
                align="center"
                values={vendorRiskDonutStatus}
            />
        );
    }, [isLoading, hasData, vendorRiskDonutStatus, handleOnClick]);

    return (
        <Card
            title={t`Vendor risks`}
            data-testid="VendorRisksCard"
            data-id="fVBU3hYW"
            body={body}
            onClick={handleOnClick}
        />
    );
});
