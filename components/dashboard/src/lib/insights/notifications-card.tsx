import { useCallback } from 'react';
import type { DashboardNotification } from '@components/dashboard';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { dimension104x } from '@cosmos/constants/tokens';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { dashboardNotificationsCardModel } from './models/dashboard-notifications-card-model';

export const NotificationsCard = observer((): React.JSX.Element | null => {
    const { canViewNotifications, allNotifications } =
        dashboardNotificationsCardModel;
    const navigate = useNavigate();

    const { currentWorkspace } = sharedWorkspacesController;

    const handleNotificationClick = useCallback(
        (notification: DashboardNotification) => {
            if (!currentWorkspace) {
                return;
            }

            if (notification.type === 'AUDIT') {
                navigate(
                    `/workspaces/${currentWorkspace.id}/compliance/audits/all/${notification.id}/details`,
                );

                return;
            }

            navigate(`/trust-center`);
        },
        [currentWorkspace, navigate],
    );

    return canViewNotifications ? (
        <Card
            title={t`Notifications`}
            data-testid="NotificationsCard"
            data-id="SZzy13pM"
            body={
                <Box
                    data-id="notifications-scroll-container"
                    maxHeight={dimension104x}
                    overflowY="auto"
                >
                    <StackedList data-id="dashboard-stacked-list-notifications">
                        {allNotifications.map((notification) => {
                            const notificationName = notification.name;

                            return (
                                <StackedListItem
                                    key={notification.id}
                                    data-id={`notification-item-${notification.id}`}
                                    rowButtonLabel={t`View ${notificationName} notifications`}
                                    primaryColumn={
                                        <Stack
                                            gap="4x"
                                            data-testid="NotificationsCell"
                                            data-id="2cw4LIZ6"
                                        >
                                            <AppLink
                                                label={notification.name}
                                                data-testid="AppLinkCell"
                                                data-id="Y4HTf2SB"
                                            />
                                            <Metadata
                                                label={notification.numUnreadMessages.toString()}
                                                colorScheme="warning"
                                                iconName="Messages"
                                                type="number"
                                            />
                                        </Stack>
                                    }
                                    onRowClick={() => {
                                        handleNotificationClick(notification);
                                    }}
                                />
                            );
                        })}
                    </StackedList>
                </Box>
            }
        />
    ) : null;
});
