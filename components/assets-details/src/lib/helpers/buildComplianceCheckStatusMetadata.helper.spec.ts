import { describe, expect, test } from 'vitest';
import { buildComplianceCheckStatusMetadata } from './buildComplianceCheckStatusMetadata.helper';

describe('buildComplianceCheckStatusMetadata', () => {
    test('returns correct metadata for EXCLUDED status', () => {
        const result = buildComplianceCheckStatusMetadata('EXCLUDED');

        expect(result).toStrictEqual({
            label: 'Excluded',
            iconName: 'OutOfScope',
            colorScheme: 'neutral',
        });
    });

    test('returns correct metadata for PASS status', () => {
        const result = buildComplianceCheckStatusMetadata('PASS');

        expect(result).toStrictEqual({
            label: 'Compliant',
            iconName: 'CheckCircle',
            colorScheme: 'success',
        });
    });

    test('returns correct metadata for FAIL status', () => {
        const result = buildComplianceCheckStatusMetadata('FAIL');

        expect(result).toStrictEqual({
            label: 'Not compliant',
            iconName: 'NotReady',
            colorScheme: 'critical',
        });
    });

    test('returns correct metadata for MISCONFIGURED status', () => {
        const result = buildComplianceCheckStatusMetadata('MISCONFIGURED');

        expect(result).toStrictEqual({
            label: 'Not compliant',
            iconName: 'NotReady',
            colorScheme: 'critical',
        });
    });

    test('returns default metadata for undefined status', () => {
        const result = buildComplianceCheckStatusMetadata(undefined);

        expect(result).toStrictEqual({
            label: 'Not compliant',
            iconName: 'NotReady',
            colorScheme: 'critical',
        });
    });
});
