import { sharedUsersInfiniteController } from '@controllers/users';
import { t } from '@globals/i18n/macro';
import { compareDayIsPast } from '@helpers/date-time';
import type { FormSchema } from '@ui/forms';

export const getFormSchema = (): FormSchema => {
    const { options, hasNextPage, isFetching, isLoading, onFetchUsers } =
        sharedUsersInfiniteController;

    return {
        approvers: {
            type: 'combobox',
            label: t`Approvers`,
            loaderLabel: t`Loading results`,
            removeAllSelectedItemsLabel: t`Clean all`,
            getSearchEmptyState: () => t`No users found`,
            isMultiSelect: true,
            options,
            hasMore: hasNextPage,
            isLoading: isFetching && isLoading,
            onFetchOptions: onFetchUsers,
            helpText: t`Add people who can approve this control. If added, only one needs to approve.`,
        },
        approvalDeadline: {
            type: 'date',
            label: t`Approval deadline`,
            getIsDateUnavailable: compareDayIsPast,
            dateUnavailableText: t`The approval deadline must be today or in the future`,
        },
    };
};
