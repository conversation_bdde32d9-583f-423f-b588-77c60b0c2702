import { isEmpty } from 'lodash-es';
import { useCallback } from 'react';
import { openMapRequirementsModal } from '@components/requirements';
import { Accordion } from '@cosmos/components/accordion';
import { Button } from '@cosmos/components/button';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { getFrameworkBadge } from '@cosmos-lab/components/framework-badge';
import { SimpleTable } from '@cosmos-lab/components/simple-table';
import type {
    FrameworkResponseDto,
    RequirementListResponseDto,
} from '@globals/api-sdk/types';
import { ActionsCell } from './table-cells/actions-cell';
import { NameCell } from './table-cells/name-cell';
import { RequirementCell } from './table-cells/requirement-cell';

type RetypedRequirementListResponseDto = Omit<
    RequirementListResponseDto,
    'id'
> & {
    id: string;
};

export interface RequirementsAccordionProps {
    id: string;
    framework: FrameworkResponseDto;
}

const REQUIREMENTS_TABLE_COLUMNS = [
    {
        id: 'requirement',
        accessorKey: 'requirement',
        header: 'Requirement',
        cell: RequirementCell,
        size: '26%',
    },
    {
        id: 'name',
        header: 'Name',
        cell: NameCell,
        size: '60%',
    },
    {
        id: 'actions',
        accessorKey: 'actions',
        header: 'Actions',
        cell: ActionsCell,
        isHidden: true,
        size: '14%',
    },
];

export const RequirementsAccordion = ({
    framework,
}: RequirementsAccordionProps): React.JSX.Element => {
    /**
     * TODO: We don't currently have a way to persist the selected requirements over the entire form.
     * The below empty array is just a placeholder that should be replaced with the actual requirements selected by the user
     * from the MapRequirementsModal.
     * We should wait until the forms component is ready for this specific use case.
     * This work will be done in the following ticket: https://drata.atlassian.net/browse/ENG-67249.
     */
    const requirements: RetypedRequirementListResponseDto[] = [];
    const requirementsCountLabel = requirements.length.toString();
    const showTable = !isEmpty(requirements);
    const showRemoveAll = showTable;

    const handleMapRequirementsOnClick = useCallback(() => {
        openMapRequirementsModal(framework);
    }, [framework]);

    return (
        <Accordion
            title={framework.name}
            data-testid="RequirementsAccordion"
            data-id="rAMy2A8p"
            iconSlot={{
                slotType: 'frameworkBadge',
                typeProps: {
                    badgeName: getFrameworkBadge(framework.tag),
                    size: 'sm',
                },
            }}
            body={
                <Stack direction="column" gap="4x">
                    <Stack gap="3x" justify="end">
                        {showRemoveAll && (
                            <Button
                                label="Unmap all"
                                colorScheme="danger"
                                level="tertiary"
                                size="sm"
                            />
                        )}
                        <Button
                            label="Map requirements"
                            level="secondary"
                            size="sm"
                            onClick={handleMapRequirementsOnClick}
                        />
                    </Stack>
                    {showTable && (
                        <SimpleTable
                            caption="Requirements table"
                            data={requirements}
                            columns={REQUIREMENTS_TABLE_COLUMNS}
                        />
                    )}
                </Stack>
            }
            supportingContent={
                <Stack gap="2x" align="center">
                    <Text type="title" size="100">
                        Mapped requirements
                    </Text>
                    <Metadata label={requirementsCountLabel} type="number" />
                </Stack>
            }
        />
    );
};
