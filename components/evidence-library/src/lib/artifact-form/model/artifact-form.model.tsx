import { isNil } from 'lodash-es';
import { z } from 'zod';
import { sharedUsersInfiniteController } from '@controllers/users';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedEvidenceLibraryUpdateDatesModel } from '@models/evidence';
import type { FieldSchemaWithGroup, FormSchema } from '@ui/forms';
import { getArtifactDatesInputSchema } from '../../artifact-dates-input/helpers/get-artifact-dates-input-schema.helper';
import { getBoxArtifactDatesInputSchema } from '../../artifact-dates-input/helpers/get-box-artifact-dates-input-schema.helper';
import { getArtifactExternalFormSchema } from '../../artifact-external-form/helpers/get-artifact-external-form-schema.helper';
import type { ArtifactFormProps } from '../artifact-form.component';
import {
    JIRA_URL_REGEX,
    URL_REGEX,
} from '../constants/evidence-form-regex.constant';
import {
    ACCEPTED_UPLOAD_FORMATS,
    FILE_UPLOAD_ERROR_CODE_MESSAGES,
} from '../constants/file-artifact.constants';
import { RENEWAL_FREQUENCY_OPTIONS } from '../constants/renewal-frequency.constant';

export class EvidenceArtifactSchemaModel {
    #meta: ArtifactFormProps['meta'] | undefined;

    constructor() {
        makeAutoObservable(this);
    }

    get ownerSchema(): FieldSchemaWithGroup {
        const { ownerInitialValue } = this.#meta ?? {};
        const { options, hasNextPage, isFetching, isLoading, onFetchUsers } =
            sharedUsersInfiniteController;

        const currentOwnerValue = ownerInitialValue
            ? {
                  id: ownerInitialValue.id,
                  label: ownerInitialValue.fullName,
                  avatar: {
                      imgSrc: ownerInitialValue.avatarUrl,
                      fallbackText: ownerInitialValue.initials,
                  },
                  description: ownerInitialValue.email,
                  value: ownerInitialValue.id,
                  userData: ownerInitialValue.userData,
              }
            : undefined;

        return {
            type: 'combobox',
            label: t`Owner`,
            loaderLabel: t`Loading users...`,
            placeholder: t`Search by name`,
            isLoading: isFetching && isLoading,
            getSearchEmptyState: () => t`No users found`,
            options,
            hasMore: hasNextPage,
            onFetchOptions: onFetchUsers,
            isOptional: false,
            initialValue: currentOwnerValue,
            validator: z.object({
                id: z.string(),
                label: z.string(),
                value: z.string(),
            }),
        };
    }

    get evidenceDatesSchema(): FieldSchemaWithGroup {
        return getArtifactDatesInputSchema(this.#meta);
    }

    get updateEvidenceDatesSchema(): FieldSchemaWithGroup {
        const { currentLibraryVersion, evidenceDetails } =
            sharedEvidenceLibraryUpdateDatesModel;

        return {
            ...getArtifactDatesInputSchema(),
            initialValue: {
                creationDate: currentLibraryVersion?.filedAt,
                renewalDate: currentLibraryVersion?.renewalDate,
                renewalFrequency:
                    RENEWAL_FREQUENCY_OPTIONS[
                        evidenceDetails?.renewalSchema.renewalScheduleType ??
                            'ONE_YEAR'
                    ],
            },
        } as FieldSchemaWithGroup;
    }

    get fileSchema(): FieldSchemaWithGroup {
        return {
            type: 'file',
            label: t`Upload artifact files`,
            helpText: t`You can only upload one file`,
            isMulti: false,
            oneFileOnly: true,
            acceptedFormats: ACCEPTED_UPLOAD_FORMATS,
            errorCodeMessages: FILE_UPLOAD_ERROR_CODE_MESSAGES,
            innerLabel: t`Or drop files here`,
            selectButtonText: t`Upload files`,
            removeButtonText: t`Remove file`,
            ...(this.#meta?.fileInitialValue && {
                initialValue: this.#meta.fileInitialValue,
            }),
        };
    }

    get urlSchema(): FieldSchemaWithGroup {
        return {
            type: 'text',
            label: t`URL`,
            validator: z
                .string()
                .url({
                    message: t`Please enter a valid URL`,
                })
                .refine((value) => URL_REGEX.test(value), {
                    message: t`Please enter a valid URL`,
                }),
            initialValue: this.#meta?.urlInitialValue,
        };
    }

    get ticketUrlSchema(): FieldSchemaWithGroup {
        return {
            type: 'text',
            label: t`Ticket URL`,
            helpText: t`This evidence only supports tickets that are closed.`,
            validator: z
                .string()
                .url({
                    message: t`Ticket URL is required`,
                })
                .refine((value) => JIRA_URL_REGEX.test(value), {
                    message: t`This URL must be a valid ticket URL`,
                }),
            initialValue: this.#meta?.ticketUrlInitialValue,
        };
    }

    get evidenceDatesInputSchema(): FieldSchemaWithGroup {
        if (!this.#meta) {
            throw new Error('Meta is not defined');
        }

        const { fileInitialValue, onDelete, externalFileInitialValue } =
            this.#meta;

        const fileAndDeleteAreNil = isNil(fileInitialValue) && isNil(onDelete);
        const externalFileIsNil = isNil(externalFileInitialValue);

        if (
            (externalFileIsNil && fileAndDeleteAreNil) ||
            (externalFileIsNil && isNil(onDelete)) ||
            (!externalFileIsNil && fileAndDeleteAreNil)
        ) {
            throw new Error('Invalid meta file');
        }

        return getBoxArtifactDatesInputSchema(this.#meta);
    }

    get artifactExternalFormSchema(): FieldSchemaWithGroup {
        if (
            !this.#meta ||
            isNil(this.#meta.onDisconnect) ||
            !this.#meta.provider
        ) {
            throw new Error('Meta is not defined');
        }

        return getArtifactExternalFormSchema(this.#meta);
    }

    setMeta = (meta: ArtifactFormProps['meta']): void => {
        this.#meta = meta;
    };

    schema = (mode: ArtifactFormProps['mode']): FormSchema => {
        switch (mode) {
            case 'URL': {
                return {
                    url: this.urlSchema,
                    artifactDatesInput: this.evidenceDatesSchema,
                    owner: this.ownerSchema,
                };
            }
            case 'TICKET_PROVIDER': {
                return {
                    ticketUrl: this.ticketUrlSchema,
                    artifactDatesInput: this.evidenceDatesSchema,
                    owner: this.ownerSchema,
                };
            }
            case 'EVIDENCE_DATES': {
                return {
                    artifactDatesInput: this.evidenceDatesSchema,
                };
            }
            case 'FILE': {
                return {
                    file: this.fileSchema,
                };
            }
            case 'EXTERNAL_FILE': {
                return {
                    externalFile: this.artifactExternalFormSchema,
                };
            }
            case 'EVIDENCE_DATES_OWNER': {
                return {
                    artifactDatesInput: this.evidenceDatesSchema,
                    owner: this.ownerSchema,
                };
            }
            case 'OWNER': {
                return {
                    owner: this.ownerSchema,
                };
            }
            case 'NEW_EVIDENCE':
            case 'BOX_FILE_EVIDENCE_DATES': {
                return {
                    artifactDatesInput: {
                        ...this.evidenceDatesInputSchema,
                        showDivider: true,
                    },
                    owner: this.ownerSchema,
                };
            }
            case 'UPDATE_EVIDENCE_DATES': {
                return {
                    artifactDatesInput: this.updateEvidenceDatesSchema,
                };
            }
            default: {
                return {};
            }
        }
    };
}
