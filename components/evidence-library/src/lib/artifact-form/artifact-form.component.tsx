import type { SubmitHandler } from 'react-hook-form';
import type { CosmosFileObject } from '@cosmos/components/file-upload';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { observer } from '@globals/mobx';
import { Form, type FormValues } from '@ui/forms';
import type { ArtifactExternalSourceProviderProps } from '../modals/add-artifact/types/artifact-external-source.type';
import type { EvidenceOwner } from '../types/evidence-owner.type';
import type { ExternalFile } from '../types/external-file.type';
import { EvidenceArtifactSchemaModel } from './model/artifact-form.model';

export interface ArtifactFormMeta {
    fileName?: string;
    onDelete?: () => void;
    ownerInitialValue?: EvidenceOwner;
    urlInitialValue?: string;
    ticketUrlInitialValue?: string;
    fileInitialValue?: CosmosFileObject[];
    creationDateInitialValue?: string;
    renewalDateInitialValue?: string;
    renewalFrequencyInitialValue?: ListBoxItemData;
    onDisconnect?: () => void;
    provider?: ArtifactExternalSourceProviderProps;
    externalFileInitialValue?: ExternalFile;
}

export interface ArtifactFormProps {
    formId: string;
    formRef: React.RefObject<HTMLFormElement>;
    onSubmit: SubmitHandler<FormValues>;
    mode:
        | 'URL'
        | 'TICKET_PROVIDER'
        | 'EVIDENCE_DATES'
        | 'FILE'
        | 'EXTERNAL_FILE'
        | 'EVIDENCE_DATES_OWNER'
        | 'OWNER'
        | 'BOX_FILE_EVIDENCE_DATES'
        | 'NEW_EVIDENCE'
        | 'UPDATE_EVIDENCE_DATES';
    meta?: ArtifactFormMeta;
}

export const ArtifactForm = observer(
    ({
        mode,
        formRef,
        onSubmit,
        formId,
        meta,
    }: ArtifactFormProps): React.JSX.Element => {
        const { schema, setMeta } = new EvidenceArtifactSchemaModel();

        if (meta) {
            setMeta(meta);
        }

        // there is a form for scan files probably we need to implement that in here,
        // but looking at the code is too complex need a lot of understanding of the code and the feature
        return (
            <Form
                hasExternalSubmitButton
                formId={formId}
                ref={formRef}
                schema={schema(mode)}
                data-id="zn67EW2H"
                onSubmit={onSubmit}
            />
        );
    },
);
