import type { EvidenceRequestDto } from '@globals/api-sdk/types';

export interface ExternalFile {
    recordId: string;
    recordType: string;
    name: string;
    size?: number;
    lastModifiedDate: Date;
    mimeType: string;
    downloadable?: boolean;
    sourceCloudProvider: string;
    provider: {
        connected: boolean;
        label: string;
        key: string;
        source: EvidenceRequestDto['source'];
    };
}
