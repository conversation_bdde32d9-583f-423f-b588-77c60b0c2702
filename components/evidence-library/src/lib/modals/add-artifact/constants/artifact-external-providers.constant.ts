import type {
    ArtifactExternalSourceProviderProps,
    CloudStorageExternalProviders,
} from '../types/artifact-external-source.type';

export const artifactExternalProviders = {
    'google-drive': {
        key: 'google-drive',
        label: 'Google Drive',
        external: true,
        source: 'GOOGLE_DRIVE',
    },
    onedrive: {
        key: 'onedrive',
        label: 'OneDrive',
        external: true,
        source: 'ONE_DRIVE',
    },
    sharepoint: {
        key: 'sharepoint',
        label: 'SharePoint',
        external: true,
        source: 'SHARE_POINT',
    },
    box: {
        key: 'box',
        label: 'Box',
        external: true,
        source: 'BOX',
    },
    dropbox: {
        key: 'dropbox',
        label: 'Dropbox',
        external: true,
        source: 'DROPBOX',
    },
} as const satisfies Record<
    CloudStorageExternalProviders,
    ArtifactExternalSourceProviderProps
>;
