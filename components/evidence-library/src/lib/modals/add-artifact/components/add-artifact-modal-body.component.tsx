import { head } from 'lodash-es';
import { Feedback } from '@cosmos/components/feedback';
import type { CosmosFileObject } from '@cosmos/components/file-upload';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Tabs } from '@cosmos/components/tabs';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { ArtifactForm } from '../../../artifact-form/artifact-form.component';
import type { ExternalFile } from '../../../types/external-file.type';
import type { AddArtifactModalProps } from '../add-artifact-wizard-modal';
import { EVIDENCE_ARTIFACT_FORM_ID } from '../constants/evidence-artifact-form-id.constant';
import { buildArtifactTabs } from '../helpers/build-artifacts-tabs.helper';
import { sharedArtifactModalModel } from '../models/artifact-modal.model';

export const ArtifactModalBody = observer(
    ({ formRef, onSubmit }: AddArtifactModalProps): React.JSX.Element => {
        if (!formRef || !onSubmit) {
            throw new Error('Data required');
        }
        const { step, firstStepValues, prevStep, owner, mode } =
            sharedArtifactModalModel;

        const file = [
            {
                file: head(toJS(firstStepValues?.file as File[])),
                errors: [],
            },
        ] as CosmosFileObject[];

        const externalFile = firstStepValues?.externalFile as ExternalFile;

        const feedbackComponent = (
            <Feedback
                severity="primary"
                title={t`This evidence will update everywhere in Drata when
                                        you upload a new version.`}
            />
        );

        return (
            <Modal.Body data-id="XN9kCnNB">
                <Stack gap="2xl" direction="column" data-id="38s8muYP">
                    {mode === 'TICKET_PROVIDER' && (
                        <>
                            <Feedback
                                title={t`you’re connected to Jira`}
                                severity="success"
                            />
                            <Text>
                                {t`Evidence Library only supports tickets that are
                                closed. Make sure your ticket status is set to
                                closed or done before copying and pasting the
                                URL.`}
                            </Text>
                        </>
                    )}

                    {(() => {
                        if (step === 'FIRST_STEP') {
                            if (mode === 'FILE') {
                                return (
                                    <Tabs
                                        overflowLeftLabel="Scroll tabs to the left"
                                        overflowRightLabel="Scroll tabs to the right"
                                        defaultTabId="evidence-local-file-tab"
                                        data-id="GKpVw75S"
                                        hasPadding={false}
                                        tabs={buildArtifactTabs({
                                            formRef,
                                            onSubmit,
                                        })}
                                    />
                                );
                            }

                            return (
                                <Stack gap="2xl" direction="column">
                                    {feedbackComponent}
                                    <ArtifactForm
                                        formId={EVIDENCE_ARTIFACT_FORM_ID}
                                        formRef={formRef}
                                        mode={mode}
                                        meta={{
                                            ownerInitialValue: owner,
                                        }}
                                        onSubmit={onSubmit}
                                    />
                                </Stack>
                            );
                        }

                        return (
                            <Stack
                                gap="2xl"
                                direction="column"
                                data-id="N1l26zkr"
                            >
                                {feedbackComponent}
                                <ArtifactForm
                                    formId={EVIDENCE_ARTIFACT_FORM_ID}
                                    formRef={formRef}
                                    mode={'BOX_FILE_EVIDENCE_DATES'}
                                    data-id="_r8oc6cE"
                                    meta={{
                                        onDelete: prevStep,
                                        ownerInitialValue: owner,
                                        fileInitialValue: file,
                                        externalFileInitialValue: externalFile,
                                    }}
                                    onSubmit={onSubmit}
                                />
                            </Stack>
                        );
                    })()}
                </Stack>
            </Modal.Body>
        );
    },
);
