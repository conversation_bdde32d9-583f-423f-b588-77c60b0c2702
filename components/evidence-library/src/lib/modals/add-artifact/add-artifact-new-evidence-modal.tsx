import type { <PERSON>mit<PERSON><PERSON><PERSON> } from 'react-hook-form';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Tabs } from '@cosmos/components/tabs';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { type FormValues, useFormSubmit } from '@ui/forms';
import { sharedEvidenceLibraryAddEvidenceFormModel } from '@views/evidence-add-evidence';
import type { ExternalFile } from '../../types/external-file.type';
import { buildArtifactTabs } from './helpers/build-artifacts-tabs.helper';
import { handleCloseModal } from './helpers/close-add-artifact-modal.helper';

export const AddArtifactNewEvidenceModal = (): React.JSX.Element => {
    const { formRef, triggerSubmit } = useFormSubmit();
    const { storeFileValue, storeFileExternalValue } =
        sharedEvidenceLibraryAddEvidenceFormModel;

    const handleSubmit: SubmitHandler<FormValues> = action((values) => {
        const parsedValues = values as {
            file?: File[];
            externalFile?: ExternalFile;
        };

        if (parsedValues.file) {
            storeFileValue(values);
            handleCloseModal();
        }

        if (parsedValues.externalFile) {
            storeFileExternalValue(values);
            handleCloseModal();
        }
    });

    return (
        <>
            <Modal.Header
                title={t`Add artifact`}
                closeButtonAriaLabel={t`Close add artifact`}
                onClose={handleCloseModal}
            />
            <Modal.Body data-id="XN9kCnNB">
                <Stack gap="2xl" direction="column" data-id="38s8muYP">
                    <Tabs
                        overflowLeftLabel={t`Scroll tabs to the left`}
                        overflowRightLabel={t`Scroll tabs to the right`}
                        defaultTabId="evidence-local-file-tab"
                        data-id="GKpVw75S"
                        hasPadding={false}
                        tabs={buildArtifactTabs({
                            formRef,
                            onSubmit: handleSubmit,
                        })}
                    />
                </Stack>
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        type: 'button',
                        label: t`Cancel`,
                        level: 'secondary',
                        onClick: () => {
                            handleCloseModal();
                        },
                    },
                    {
                        type: 'submit',
                        label: t`Add artifact`,
                        level: 'primary',
                        colorScheme: 'primary',
                        onClick: () => {
                            triggerSubmit().catch(() => {
                                // not handling errors here
                                console.error('Failed to submit form');
                            });
                        },
                    },
                ]}
            />
        </>
    );
};
