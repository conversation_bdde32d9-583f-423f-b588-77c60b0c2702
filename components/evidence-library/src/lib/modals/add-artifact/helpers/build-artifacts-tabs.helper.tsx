import type { Submit<PERSON>and<PERSON> } from 'react-hook-form';
import type { TabItem } from '@cosmos/components/tabs';
import type { FormValues } from '@ui/forms';
import { ArtifactExternalForm } from '../../../artifact-external-form/artifact-external-form.component';
import {
    ArtifactForm,
    type ArtifactFormMeta,
} from '../../../artifact-form/artifact-form.component';
import { artifactExternalProviders } from '../constants/artifact-external-providers.constant';
import { EVIDENCE_ARTIFACT_FORM_ID } from '../constants/evidence-artifact-form-id.constant';
import type { ArtifactExternalSourceProviderProps } from '../types/artifact-external-source.type';

export const buildArtifactTabs = ({
    formRef,
    onSubmit,
    meta,
}: {
    formRef: React.RefObject<HTMLFormElement>;
    onSubmit: SubmitHandler<FormValues>;
    meta?: ArtifactFormMeta;
}): TabItem[] => {
    const providersArray = Object.values(artifactExternalProviders);

    const renderForm = (
        provider?: ArtifactExternalSourceProviderProps,
    ): React.JSX.Element => {
        if (provider?.external) {
            return (
                <ArtifactExternalForm
                    formRef={formRef}
                    provider={provider}
                    onSubmit={onSubmit}
                />
            );
        }

        return (
            <ArtifactForm
                formId={EVIDENCE_ARTIFACT_FORM_ID}
                formRef={formRef}
                mode={'FILE'}
                meta={meta}
                data-testid="renderForm"
                data-id="wOXoTYZs"
                onSubmit={onSubmit}
            />
        );
    };

    const tabs: TabItem[] = [
        {
            tabId: 'evidence-local-file-tab',
            label: 'Local file',
            content: renderForm(),
        },
        ...providersArray.map((provider) => ({
            tabId: provider.key,
            label: provider.label,
            content: renderForm(provider),
        })),
    ];

    return tabs;
};
