import { isNil, size } from 'lodash-es';
import type { ObjectTypes } from '@components/map-controls-step';
import {
    type ControlListBoxItemData,
    sharedEvidenceMutationController,
    sharedLinkControlsController,
} from '@controllers/evidence-library';
import { modalController } from '@controllers/modal';
import { ComboboxField } from '@cosmos/components/combobox-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Modal } from '@cosmos/components/modal';
import { plural, t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { LINK_CONTROLS_MODAL_ID } from './constants/link-controls-modal.constant';
import { getModalTitleByObject } from './helpers/get-modal-title-by-object.helper';

const FORM_ID = 'map-control-to-evidence-modal-form-id';

export const LinkControlsModal = observer(
    ({
        objectType,
        onConfirm,
    }: {
        objectType: ObjectTypes;
        onConfirm?: (selectedControls: ControlListBoxItemData[]) => void;
    }): React.JSX.Element => {
        const {
            addControls,
            updateSelectedItems,
            clearSelectedItems,
            selectedListItems,
            controlOptions,
            loadNextPage,
            isLoading: isControlsLoading,
            hasNextPage,
        } = sharedLinkControlsController;
        const { isUpdating } = sharedEvidenceMutationController;

        const count = size(selectedListItems);

        const handleChange = (value: ListBoxItemData[]) => {
            updateSelectedItems(value as ControlListBoxItemData[]);
        };

        return (
            <>
                <Modal.Header
                    title={getModalTitleByObject(objectType)}
                    closeButtonAriaLabel={t`Close modal`}
                    description={plural(count, {
                        one: '# control selected',
                        other: '# controls selected',
                    })}
                    onClose={() => {
                        clearSelectedItems();
                        modalController.closeModal(LINK_CONTROLS_MODAL_ID);
                    }}
                />
                <Modal.Body>
                    <ComboboxField
                        isMultiSelect
                        label={t`Search control`}
                        formId={FORM_ID}
                        getSearchEmptyState={() => t`No controls found`}
                        loaderLabel={t`Loading...`}
                        name="controls"
                        removeAllSelectedItemsLabel={t`Clear all`}
                        placeholder={t`Search by name...`}
                        getRemoveIndividualSelectedItemClickLabel={undefined}
                        options={controlOptions}
                        isLoading={isControlsLoading}
                        hasMore={hasNextPage}
                        itemToString={(item) => {
                            if (isNil(item) || 'groupHeader' in item) {
                                return '';
                            }

                            return `${(item.controlData as ControlListBoxItemData['controlData']).code} ${item.label}`;
                        }}
                        onFetchOptions={loadNextPage}
                        onChange={handleChange}
                    />
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Close`,
                            level: 'secondary',
                            onClick: () => {
                                clearSelectedItems();
                                modalController.closeModal(
                                    LINK_CONTROLS_MODAL_ID,
                                );
                            },
                        },
                        {
                            label: t`Confirm`,
                            level: 'primary',
                            colorScheme: 'primary',
                            isLoading: isUpdating,
                            onClick: action(() => {
                                if (onConfirm) {
                                    // Use custom callback with selected controls

                                    onConfirm(selectedListItems);
                                } else {
                                    // Use default behavior
                                    addControls();
                                }
                                if (objectType !== 'evidence-linked-controls') {
                                    modalController.closeModal(
                                        LINK_CONTROLS_MODAL_ID,
                                    );
                                }
                            }),
                        },
                    ]}
                />
            </>
        );
    },
);
