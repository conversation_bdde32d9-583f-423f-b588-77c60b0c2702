import { noop } from 'lodash-es';
import { z } from 'zod';
import { t } from '@globals/i18n/macro';
import type { CustomFieldRenderProps, FieldSchemaWithGroup } from '@ui/forms';
import type { ArtifactFormProps } from '../../artifact-form/artifact-form.component';
import { ArtifactExternalBrowserFilesForm } from '../artifact-external-browser-files-form.component';

export const getArtifactExternalFormSchema = (
    meta: NonNullable<ArtifactFormProps['meta']>,
): FieldSchemaWithGroup => {
    const { onDisconnect, provider } = meta;

    if (!provider) {
        throw new Error('Provider is not defined');
    }

    return {
        type: 'custom',
        label: t`Cloud Storage Providers`,
        render: (props: CustomFieldRenderProps) => (
            <ArtifactExternalBrowserFilesForm
                data-id="_3--AD4Y"
                provider={provider}
                feedback={props.feedback}
                onDisconnect={onDisconnect ?? noop}
                onChange={props.onChange}
            />
        ),
        validator: z.object(
            {
                recordId: z.string(),
                recordType: z.literal('file'),
                name: z.string(),
                size: z.number().optional(),
                lastModifiedDate: z.string(),
                mimeType: z.string(),
                downloadable: z.boolean().optional(),
                sourceCloudProvider: z.literal(provider.key),
                provider: z.object({
                    connected: z.boolean(),
                    label: z.literal(provider.label),
                    key: z.literal(provider.key),
                    source: z.string(),
                }),
            },
            { message: t`Please select a file` },
        ),
    };
};
