import { useEffect } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import type { Connection } from '@apideck/vault-js';
import {
    sharedCloudStorageController,
    sharedCloudStorageFilesController,
    sharedCloudStorageMutationController,
} from '@controllers/cloud-storage';
import { Button } from '@cosmos/components/button';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { cloudStorageProvider } from '@helpers/external-integration';
import { Form, type FormValues } from '@ui/forms';
import { EvidenceArtifactSchemaModel } from '../artifact-form/model/artifact-form.model';
import { EVIDENCE_ARTIFACT_FORM_ID } from '../modals/add-artifact/constants/evidence-artifact-form-id.constant';
import type { ArtifactExternalSourceProviderProps } from '../modals/add-artifact/types/artifact-external-source.type';
import { sharedArtifactExternalBrowseFilesModel } from './models/artifact-external-browser-files.model';

export const ArtifactExternalForm = observer(
    ({
        provider,
        onSubmit,
        formRef,
    }: {
        provider: ArtifactExternalSourceProviderProps;
        onSubmit: SubmitHandler<FormValues>;
        formRef: React.RefObject<HTMLFormElement>;
    }): React.JSX.Element => {
        const { validationUserConnection, isLoading } =
            sharedCloudStorageController;
        const { isDeleteRunning } = sharedCloudStorageMutationController;
        const { hasConnection } = sharedArtifactExternalBrowseFilesModel;

        const onConnectionChange = (connection: Connection): void => {
            sharedCloudStorageController.loadValidateUser(provider.key);
            sharedCloudStorageMutationController.postReportConnectionEvent(
                connection,
            );
        };

        const openExternalConnectionModal = action((sessionToken: string) => {
            cloudStorageProvider.openCloudStorageProviderModal({
                sessionToken,
                providerId: provider.key,
                onClose: () => {
                    sharedCloudStorageController.loadValidateUser(provider.key);
                },
                onConnectionChange,
            });
        });

        const disconnectProvider = action((): void => {
            sharedCloudStorageMutationController.deleteConnection(provider.key);
            sharedCloudStorageController.loadValidateUser(provider.key);
        });

        useEffect(() => {
            sharedCloudStorageController.loadValidateUser(provider.key);

            if (hasConnection) {
                sharedCloudStorageFilesController.load({
                    providerId: provider.key,
                });
            }
        }, [provider.key, hasConnection]);

        const { schema, setMeta } = new EvidenceArtifactSchemaModel();

        setMeta({
            provider,
            onDisconnect: disconnectProvider,
        });

        if (isLoading || isDeleteRunning) {
            return (
                <Stack align="center" gap="4x" direction="column" width="100%">
                    <Loader label={t`Loading...`} />
                </Stack>
            );
        }

        if (!hasConnection) {
            return (
                <Stack
                    width="100%"
                    data-id="file-source-external"
                    align="center"
                    gap="4x"
                    direction="column"
                    data-testid="FileSourceExternal"
                >
                    <Text
                        type="title"
                        as="p"
                        align="center"
                    >{`Connect your ${provider.label} account to access your files`}</Text>

                    <Button
                        label="Connect"
                        colorScheme="primary"
                        level="secondary"
                        onClick={() => {
                            openExternalConnectionModal(
                                validationUserConnection?.sessionCreated
                                    ?.sessionToken ?? '',
                            );
                        }}
                    />
                </Stack>
            );
        }

        return (
            <Form
                hasExternalSubmitButton
                ref={formRef}
                formId={EVIDENCE_ARTIFACT_FORM_ID}
                schema={schema('EXTERNAL_FILE')}
                data-id="zn67EW2H"
                onSubmit={onSubmit}
            />
        );
    },
);
