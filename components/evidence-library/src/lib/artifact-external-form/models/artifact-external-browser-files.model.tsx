import { isEmpty, isNil } from 'lodash-es';
import {
    ROOT_FOLDER_NAME,
    sharedCloudStorageController,
    sharedCloudStorageDrivesController,
    sharedCloudStorageFilesController,
} from '@controllers/cloud-storage';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import type { CloudFileFiltersDto } from '@globals/api-sdk/types';
import { makeAutoObservable } from '@globals/mobx';
import { INITIAL_PAGE } from '../../constants/browser-files.constant';
import {
    DOC_OR_IMAGE,
    FILE_TYPES,
} from '../../constants/file-specifications.constant';
import {
    getAcceptedMimetypes,
    hasAllowedExtension,
    hasNotAllowedFile,
} from '../../helpers/browser-files.helper';
import {
    isBackFolder,
    isFolderType,
    isSharedDrives,
    isSharedDrivesOrFolder,
} from '../../helpers/file-types.helper';
import type { CloudStorageExternalProviders } from '../../modals/add-artifact/types/artifact-external-source.type';
import type {
    CloudStorageResponse,
    FileItem,
} from '../../types/browser-files.type';

class ArtifactExternalBrowserFilesModel {
    constructor() {
        makeAutoObservable(this);
    }

    navigationFilesStack: FileItem[] = [];
    lastPageLoaded = 1;
    lastFolderId?: string;
    lastSearchParam?: string;
    readonly EMPTY_SIZE = 0;
    readonly EMPTY_CLOUD_STORAGE_RESPONSE: CloudStorageResponse = {
        items: [],
        metadata: {
            itemsOnPage: this.EMPTY_SIZE,
            cursors: {
                previous: undefined,
                current: undefined,
                next: undefined,
            },
        },
    };

    buildBreadcrumbs(providerId: string, providerName: string): Breadcrumb[] {
        const breadcrumbRoot: Breadcrumb = {
            label: providerName,
            pathname: providerId,
        };

        const navigationBreads = this.navigationFilesStack.map(
            (item: FileItem) => ({
                label: item.name,
                pathname: item.id,
            }),
        );

        return [breadcrumbRoot, ...navigationBreads];
    }

    get hasConnection(): boolean {
        const { isLoading, validationUserConnection } =
            sharedCloudStorageController;

        return (
            !isNil(validationUserConnection) &&
            !isLoading &&
            validationUserConnection.hasConnection
        );
    }

    moveToRoot = (providerId: CloudStorageExternalProviders) => {
        this.lastSearchParam = '';
        this.changeBreadCrumbLevel(providerId, providerId);
    };

    changeBreadCrumbLevel = (
        providerId: CloudStorageExternalProviders,
        id: string,
    ) => {
        if (providerId === id) {
            sharedCloudStorageFilesController.load({
                providerId,
                query: {
                    searchParam: this.lastSearchParam,
                },
            });

            this.navigationFilesStack = [];
            this.lastFolderId = '';

            return;
        }

        const index = this.navigationFilesStack.findIndex(
            (item) => item.id === id,
        );

        if (index === -1) {
            return;
        }

        const breadcrumbFound = this.navigationFilesStack[index];

        this.navigationFilesStack = this.navigationFilesStack.slice(0, index);

        this.navigateToFolder(providerId, breadcrumbFound);
    };

    navigateToFolder = (
        providerId: CloudStorageExternalProviders,
        fileReference: FileItem,
    ): FileItem | null => {
        const { id, type, mimeType } = fileReference;
        const extensions = getAcceptedMimetypes(DOC_OR_IMAGE);

        if (
            !hasNotAllowedFile(
                type ?? FILE_TYPES.FOLDER,
                mimeType ?? '',
                extensions.acceptedMimetypes,
            )
        ) {
            return null;
        }

        if (isBackFolder(id)) {
            this.navigationFilesStack.pop();
            this.lastSearchParam = '';
        } else if (isFolderType(type ?? '')) {
            this.navigationFilesStack.push(fileReference);
        } else if (
            hasAllowedExtension(mimeType ?? '', extensions.acceptedMimetypes)
        ) {
            return fileReference;
        }

        const lastFolder = this.navigationFilesStack.at(-1);

        this.lastFolderId = lastFolder?.id;
        this.lastPageLoaded = INITIAL_PAGE;

        this.requestCloudStorageEndpoints({
            providerId,
            page: INITIAL_PAGE,
            folderId: this.lastFolderId,
        });

        return null;
    };

    buildItemsWithBackButton<T extends FileItem>(items: T[]): FileItem[] {
        const result: FileItem[] = [];

        if (!isEmpty(this.navigationFilesStack)) {
            result.push({
                name: '...',
                type: FILE_TYPES.FOLDER,
                id: FILE_TYPES.BACK,
            });
        }

        return [...result, ...items];
    }

    getDrivesFoundResponse(): CloudStorageResponse {
        const { drivesFound } = sharedCloudStorageDrivesController;

        if (isNil(drivesFound)) {
            return this.EMPTY_CLOUD_STORAGE_RESPONSE;
        }

        const items = drivesFound.driveInformation.map((f) => ({
            name: f.driveName,
            type: FILE_TYPES.FOLDER,
            id: f.driveId,
            size: this.EMPTY_SIZE,
        }));

        return {
            items: this.buildItemsWithBackButton(items),
            metadata: drivesFound.metadata,
        };
    }

    getFilesFoundResponse(): CloudStorageResponse {
        const { filesFound } = sharedCloudStorageFilesController;

        if (isNil(filesFound)) {
            return this.EMPTY_CLOUD_STORAGE_RESPONSE;
        }

        const items = filesFound.fileInformation.map((f) => ({
            name: f.name,
            type: f.recordType,
            id: f.recordId,
            lastModifiedDate: f.lastModifiedDate,
            mimeType: f.mimeType,
            size: f.size,
        }));

        return {
            items: this.buildItemsWithBackButton(items),
            metadata: filesFound.metadata,
        };
    }

    get cloudStorageResponse(): CloudStorageResponse {
        if (
            sharedCloudStorageFilesController.isLoading ||
            sharedCloudStorageDrivesController.isLoading
        ) {
            return this.EMPTY_CLOUD_STORAGE_RESPONSE;
        }

        return this.lastFolderId === FILE_TYPES.SHARED_DRIVES
            ? this.getDrivesFoundResponse()
            : this.getFilesFoundResponse();
    }

    fetchBySearchTerm = ({
        providerId,
        searchParam,
    }: {
        providerId: CloudStorageExternalProviders;
        searchParam: string;
    }) => {
        this.moveToRoot(providerId);

        sharedCloudStorageFilesController.load({
            providerId,
            query: {
                searchParam,
            },
        });

        this.lastSearchParam = searchParam;
        this.lastPageLoaded = INITIAL_PAGE;
    };

    requestCloudStorageEndpoints = ({
        providerId,
        page,
        nextCursor,
        folderId,
    }: {
        providerId: CloudStorageExternalProviders;
        page: number;
        nextCursor?: string;
        folderId?: string;
    }) => {
        this.lastPageLoaded = page;
        const filter: CloudFileFiltersDto = {};

        if (isSharedDrivesOrFolder(this.lastFolderId)) {
            filter.shared = true;
        } else if (isEmpty(this.lastSearchParam)) {
            filter.shared = isSharedDrives(this.lastFolderId);
            filter.folderId = folderId ?? this.lastFolderId;
        } else {
            filter.folderId = ROOT_FOLDER_NAME;
        }

        if (isSharedDrives(this.lastFolderId)) {
            sharedCloudStorageDrivesController.load(providerId);
        } else {
            sharedCloudStorageFilesController.load({
                providerId,
                query: {
                    cursor: nextCursor,
                    searchParam: this.lastSearchParam,
                },
                filter,
            });
        }
    };
}

export const sharedArtifactExternalBrowseFilesModel =
    new ArtifactExternalBrowserFilesModel();
