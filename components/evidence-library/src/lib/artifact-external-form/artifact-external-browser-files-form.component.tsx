import { debounce, isEmpty } from 'lodash-es';
import { useMemo, useState } from 'react';
import type { ControllerRenderProps } from 'react-hook-form';
import { AppDatatable } from '@components/app-datatable';
import {
    sharedCloudStorageDrivesController,
    sharedCloudStorageFilesController,
} from '@controllers/cloud-storage';
import { Box } from '@cosmos/components/box';
import type {
    FetchDataResponseParams,
    Row,
} from '@cosmos/components/datatable';
import { Feedback } from '@cosmos/components/feedback';
import type { FieldFeedbackProps } from '@cosmos/components/field-feedback';
import { Stack } from '@cosmos/components/stack';
import { Breadcrumbs } from '@cosmos-lab/components/breadcrumbs';
import { PaginationControls } from '@cosmos-lab/components/pagination-controls';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Grid } from '@radix-ui/themes/components/grid';
import { INITIAL_PAGE } from '../constants/browser-files.constant';
import { BROWSER_FILE_COLUMNS } from '../constants/browser-files-columns.constant';
import { getEstimatedTotal } from '../helpers/browser-files.helper';
import type {
    ArtifactExternalSourceProviderProps,
    CloudStorageExternalProviders,
} from '../modals/add-artifact/types/artifact-external-source.type';
import type { FileItem } from '../types/browser-files.type';
import { sharedArtifactExternalBrowseFilesModel } from './models/artifact-external-browser-files.model';

export const ArtifactExternalBrowserFilesForm = observer(
    ({
        provider,
        onDisconnect,
        onChange,
        feedback,
    }: {
        provider: ArtifactExternalSourceProviderProps;
        onDisconnect: () => void;
        onChange: ControllerRenderProps['onChange'];
        feedback?: {
            type: FieldFeedbackProps['type'];
            message: string;
        };
    }) => {
        const [currentPage, setCurrentPage] = useState(INITIAL_PAGE);
        const [resetToken, setResetToken] = useState(0);
        const [selectedRow, setSelectedRow] = useState<Row<FileItem> | null>(
            null,
        );
        const {
            cloudStorageResponse: response,
            lastFolderId,
            lastSearchParam,
            moveToRoot,
            navigateToFolder,
            changeBreadCrumbLevel,
            fetchBySearchTerm,
            requestCloudStorageEndpoints,
        } = sharedArtifactExternalBrowseFilesModel;

        const resetPagination = () => {
            setResetToken((prev) => prev + 1);
            setCurrentPage(INITIAL_PAGE);
        };

        const debouncedFetch = useMemo(
            () =>
                debounce(
                    (
                        providerId: CloudStorageExternalProviders,
                        searchParam: string,
                    ) => {
                        fetchBySearchTerm({ providerId, searchParam });
                    },
                    300,
                ),
            [fetchBySearchTerm],
        );

        const onClickRow = ({
            row,
            _internal,
        }: {
            row: FileItem;
            _internal: Row<FileItem>;
        }) => {
            const selectedFile = navigateToFolder(provider.key, row);

            if (!selectedFile) {
                selectedRow?.toggleSelected(false);

                return;
            }

            _internal.toggleSelected(true);
            setSelectedRow(_internal);
            onChange({
                recordId: selectedFile.id,
                recordType: selectedFile.type,
                name: selectedFile.name,
                size: selectedFile.size,
                lastModifiedDate: selectedFile.lastModifiedDate,
                mimeType: selectedFile.mimeType,
                downloadable: selectedFile.downloadable,
                sourceCloudProvider: provider.key,
                provider: {
                    connected: true,
                    label: provider.label,
                    key: provider.key,
                    source: provider.source,
                },
            });
        };

        const onClickBreadcrumb = (id: string) => {
            resetPagination();
            changeBreadCrumbLevel(provider.key, id);
        };

        const fetchBySearch = (param: FetchDataResponseParams) => {
            const newSearch = param.globalFilter.search;
            const wasSearchFieldCleaned =
                !isEmpty(lastSearchParam) && isEmpty(newSearch);

            if (wasSearchFieldCleaned) {
                resetPagination();
                moveToRoot(provider.key);

                return;
            }

            if (lastSearchParam !== newSearch && !isEmpty(newSearch)) {
                debouncedFetch(provider.key, newSearch ?? '');
            }
        };

        return (
            <Box data-id="artifact-external-browser-files-box" mt="2x">
                <Stack gap="3x" direction="column" data-id="JitSqLhK">
                    <Breadcrumbs
                        breadcrumbs={sharedArtifactExternalBrowseFilesModel.buildBreadcrumbs(
                            provider.key,
                            provider.label,
                        )}
                        onClick={(id) => {
                            onClickBreadcrumb(id);
                        }}
                    />
                    <Box>
                        <Box
                            height="285px"
                            data-id="artifact-external-browser-files-box"
                        >
                            <AppDatatable
                                hidePagination
                                isMultiRowSelectionEnabled={false}
                                tableId="browser-files-table"
                                total={response.items.length}
                                columns={BROWSER_FILE_COLUMNS}
                                data={response.items}
                                isLoading={
                                    sharedCloudStorageFilesController.isLoading ||
                                    sharedCloudStorageDrivesController.isLoading
                                }
                                tableActions={[
                                    {
                                        actionType: 'dropdown',
                                        id: 'map-controls-dropdown',
                                        typeProps: {
                                            isIconOnly: true,
                                            label: t`settings`,
                                            level: 'tertiary',
                                            startIconName: 'Settings',
                                            items: [
                                                {
                                                    id: 'opt-1',
                                                    label: t`Disconnect`,
                                                    onSelect: () => {
                                                        onDisconnect();
                                                    },
                                                },
                                            ],
                                        },
                                    },
                                ]}
                                emptyStateProps={{
                                    title: t`No files found`,
                                    illustrationName: 'NoResults',
                                }}
                                onFetchData={fetchBySearch}
                                onRowClick={(row) => {
                                    onClickRow(row);
                                }}
                            />
                        </Box>
                        <Grid columns="1fr auto auto" align="center">
                            {feedback && (
                                <Feedback
                                    data-id="artifact-external-browser-files-feedback"
                                    title={feedback.message}
                                    severity={'critical'}
                                />
                            )}
                            <PaginationControls
                                hidePageSizeOptions
                                hidePageCount
                                key={`${lastFolderId}${resetToken}`}
                                total={getEstimatedTotal(
                                    Boolean(response.metadata?.cursors.next),
                                    currentPage,
                                )}
                                onPageChange={(page) => {
                                    const nextCursor =
                                        page > currentPage
                                            ? response.metadata?.cursors.next
                                            : response.metadata?.cursors
                                                  .previous;

                                    setCurrentPage(page);

                                    requestCloudStorageEndpoints({
                                        providerId: provider.key,
                                        page,
                                        nextCursor,
                                    });
                                }}
                            />
                        </Grid>
                    </Box>
                </Stack>
            </Box>
        );
    },
);
