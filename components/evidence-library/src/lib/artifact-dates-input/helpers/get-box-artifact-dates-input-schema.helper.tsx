import { isNil, noop } from 'lodash-es';
import { z } from 'zod';
import { t } from '@globals/i18n/macro';
import type { CustomFieldRenderProps, FieldSchemaWithGroup } from '@ui/forms';
import type { ArtifactFormProps } from '../../artifact-form/artifact-form.component';
import { ArtifactDatesBoxInput } from '../artifact-dates-box-input.component';

export const getBoxArtifactDatesInputSchema = (
    meta: NonNullable<ArtifactFormProps['meta']>,
): FieldSchemaWithGroup => {
    const {
        fileInitialValue = [],
        onDelete = noop,
        creationDateInitialValue,
        renewalDateInitialValue,
        renewalFrequencyInitialValue,
        externalFileInitialValue,
    } = meta;

    const fileName = isNil(externalFileInitialValue)
        ? (fileInitialValue[0]?.file.name ?? '')
        : externalFileInitialValue.name;

    const hasInitialValues =
        !isNil(creationDateInitialValue) &&
        !isNil(renewalDateInitialValue) &&
        !isNil(renewalFrequencyInitialValue);

    return {
        type: 'custom',
        label: t`Dates`,
        render: (props: CustomFieldRenderProps) => (
            <ArtifactDatesBoxInput
                fieldProps={props}
                fileName={fileName}
                data-id={props['data-id']}
                onDelete={onDelete}
            />
        ),
        validator: z.object({
            creationDate: z
                .string({
                    message: t`Choose a creation date`,
                })
                .date(),
            renewalDate: z
                .string({
                    message: t`Choose a renewal date`,
                })
                .date(),
            renewalFrequency: z.object({
                value: z.string(),
            }),
        }),
        ...(hasInitialValues && {
            initialValue: {
                creationDate: creationDateInitialValue,
                renewalDate: renewalDateInitialValue,
                renewalFrequency: renewalFrequencyInitialValue,
            },
        }),
    };
};
