import { isEmpty } from 'lodash-es';
import { type Action, ActionStack } from '@cosmos/components/action-stack';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { getFullName, getInitials } from '@helpers/formatters';
import { AppLink } from '@ui/app-link';

interface ControlDetailsAccordionProps {
    id: string;
    description: string;
    frameworks?: { label: string }[];
    owner?: { firstName: string; lastName: string; avatarUrl?: string };
    'data-id'?: string;
    onRemoveControl?: () => void;
}

export const ControlDetailsBodyComponent = observer(
    ({
        id,
        description,
        frameworks,
        owner,
        'data-id': dataId = 'control-details-body',
        onRemoveControl,
    }: ControlDetailsAccordionProps): React.JSX.Element => {
        const actions: Action[] = [];

        if (onRemoveControl) {
            actions.push({
                actionType: 'button',
                id: 'remove-control-action-stack',
                typeProps: {
                    label: t`Remove`,
                    level: 'tertiary',
                    colorScheme: 'danger',
                    size: 'sm',
                    onClick: onRemoveControl,
                },
            });
        }

        return (
            <Stack
                direction="column"
                gap="2xl"
                data-testid="ControlDetailsBodyComponent"
                data-id={dataId}
            >
                <Text>{description}</Text>

                {frameworks && (
                    <KeyValuePair
                        label={t`Frameworks`}
                        type="REACT_NODE"
                        value={
                            <Stack direction="row" gap="sm">
                                {frameworks.map((framework) => (
                                    <Metadata
                                        key={framework.label}
                                        label={framework.label}
                                        type="tag"
                                        data-id="nE0du6OR"
                                    />
                                ))}
                            </Stack>
                        }
                    />
                )}

                {owner && (
                    <KeyValuePair
                        label={t`Control owner`}
                        type="USER"
                        value={{
                            username: getFullName(
                                owner.firstName,
                                owner.lastName,
                            ),
                            avatarProps: {
                                fallbackText: getInitials(
                                    `${owner.firstName} ${owner.lastName}`,
                                ),
                                imgSrc: owner.avatarUrl ?? '',
                                imgAlt: getFullName(
                                    owner.firstName,
                                    owner.lastName,
                                ),
                            },
                        }}
                    />
                )}

                <AppLink
                    href={`/workspaces/${sharedWorkspacesController.currentWorkspace?.id}/compliance/controls/${id}/overview`}
                    label={t`Go to control`}
                />

                {!isEmpty(actions) && (
                    <ActionStack
                        gap="lg"
                        stacks={[
                            {
                                actions,
                                id: 'control-action-stack',
                            },
                        ]}
                    />
                )}
            </Stack>
        );
    },
);
