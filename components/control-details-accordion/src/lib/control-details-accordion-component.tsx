import { Accordion } from '@cosmos/components/accordion';
import { getControlStatusDisplay } from '@helpers/control-status';
import { ControlDetailsBodyComponent } from './components/control-details-body-component';

interface ControlDetailsAccordionComponentProps {
    id: string;
    name: string;
    code: string;
    description: string;
    isReady?: boolean;
    archivedAt?: string;
    frameworks?: { label: string }[];
    owner?: { firstName: string; lastName: string; avatarUrl?: string };
    'data-id'?: string;
    onRemoveControl?: () => void;
}

export const ControlDetailsAccordionComponent = ({
    id,
    name,
    code,
    description,
    isReady = false,
    archivedAt = '',
    frameworks,
    owner,
    'data-id': dataId,
    onRemoveControl,
}: ControlDetailsAccordionComponentProps): React.JSX.Element => {
    const { colorScheme, iconName } = getControlStatusDisplay({
        isReady,
        archivedAt,
    });

    return (
        <Accordion
            title={name}
            data-testid="ControlDetailsAccordionComponent"
            data-id={dataId}
            body={
                <ControlDetailsBodyComponent
                    id={id}
                    description={description}
                    frameworks={frameworks}
                    owner={owner}
                    data-id={`control-details-body-${id}`}
                    onRemoveControl={onRemoveControl}
                />
            }
            iconSlot={{
                slotType: 'metadata',
                typeProps: {
                    label: code,
                    colorScheme,
                    iconName,
                },
            }}
        />
    );
};
