import {
    sharedUsersInfinite<PERSON>ontroller,
    sharedVendorContactsInfiniteController,
} from '@controllers/users';
import { sharedVendorsIntegrationsInfiniteController } from '@controllers/vendors';
import type { VendorResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { formatCurrencyValue } from '@helpers/formatters';
import type { FormSchema } from '@ui/forms';
import { RiskSelectField } from '../components/risk-select-field.component';
import {
    BUSINESS_UNIT_VALUES,
    getBusinessUnitLabel,
} from '../constants/business-unit.constants';
import { getRiskLabel, RISK_VALUES } from '../constants/risk.constants';
import { SECURITY_OWNER_ROLES } from '../constants/security-owner-roles.constants';
import { VENDOR_CONTACT_ROLES } from '../constants/vendor-contact-roles.constants';
import { VENDOR_STATUS_OPTIONS } from '../constants/vendor-status.constants';
import {
    getDropdownActionLabelVendorType,
    VENDOR_TYPE_VALUES,
} from '../constants/vendor-type.constants';
import {
    findInOptions,
    toFieldOptionsWithLabelFunction,
} from '../helpers/field-option.helpers';
import { getRecommendedImpactLevelOptions } from '../helpers/recommended-impact-level.helper';
import {
    transformIntegrationsToListBoxItemData,
    transformUserResponseDtoToListBoxItemData,
} from '../helpers/vendor-add-vendor.helper';

export const buildVendorInternalDetailsFormSchema = (
    state?: VendorResponseDto | null,
): FormSchema => {
    const { options, hasNextPage, isFetching, isLoading, onFetchIntegrations } =
        sharedVendorsIntegrationsInfiniteController;

    const {
        options: userOptions,
        hasNextPage: hasMoreUsers,
        isFetching: isFetchingUsers,
        isLoading: isLoadingUsers,
        onFetchUsers,
    } = sharedUsersInfiniteController;

    const {
        options: contactOptions,
        hasNextPage: hasMoreContacts,
        isFetching: isFetchingContacts,
        isLoading: isLoadingContacts,
        onFetchUsers: onFetchContacts,
    } = sharedVendorContactsInfiniteController;

    const shouldShowImpactLevel =
        !sharedFeatureAccessModel.isVendorRiskManagementProEnabled;

    const baseSchema: FormSchema = {
        status: {
            type: 'select',
            options: VENDOR_STATUS_OPTIONS,
            initialValue:
                state?.status &&
                findInOptions(state.status, VENDOR_STATUS_OPTIONS),
            label: t`Status`,
            isOptional: true,
            loaderLabel: t`Loading vendor statuses`,
        },
        type: {
            type: 'select',
            options: toFieldOptionsWithLabelFunction(
                VENDOR_TYPE_VALUES,
                getDropdownActionLabelVendorType,
            ),
            initialValue:
                (state?.type &&
                    findInOptions(
                        state.type,
                        toFieldOptionsWithLabelFunction(
                            VENDOR_TYPE_VALUES,
                            getDropdownActionLabelVendorType,
                        ),
                    )) ??
                findInOptions(
                    'NONE',
                    toFieldOptionsWithLabelFunction(
                        VENDOR_TYPE_VALUES,
                        getDropdownActionLabelVendorType,
                    ),
                ),
            label: t`Type`,
            isOptional: true,
            loaderLabel: t`Loading vendor types`,
        },
        category: {
            type: 'select',
            options: toFieldOptionsWithLabelFunction(
                BUSINESS_UNIT_VALUES,
                getBusinessUnitLabel,
            ),
            initialValue:
                state?.category &&
                findInOptions(
                    state.category,
                    toFieldOptionsWithLabelFunction(
                        BUSINESS_UNIT_VALUES,
                        getBusinessUnitLabel,
                    ),
                ),
            label: t`Business unit`,
            isOptional: true,
            loaderLabel: t`Loading business units`,
        },
        risk: {
            type: 'custom',
            render: RiskSelectField,
            validateWithDefault: 'select',
            options: toFieldOptionsWithLabelFunction(RISK_VALUES, getRiskLabel),
            initialValue:
                state?.risk &&
                findInOptions(
                    state.risk,
                    toFieldOptionsWithLabelFunction(RISK_VALUES, getRiskLabel),
                ),
            label: t`Risk`,
            isOptional: true,
            loaderLabel: t`Loading risk levels`,
        },
        ...(shouldShowImpactLevel && {
            impactLevel: {
                type: 'select',
                options: getRecommendedImpactLevelOptions(),
                initialValue:
                    state?.impactLevel &&
                    getRecommendedImpactLevelOptions().find(
                        (option) => option.value === state.impactLevel,
                    ),
                label: t`Impact Level`,
                isOptional: true,
                loaderLabel: t`Loading impact levels`,
            },
        }),
        dataStored: {
            type: 'textarea',
            initialValue: state?.dataStored ?? '',
            label: t`Stored data`,
            isOptional: true,
        },
        hasPii: {
            type: 'checkbox',
            initialValue: state?.hasPii ?? false,
            label: t`This vendor stores personally identifiable information (PII)`,
            isOptional: true,
        },
        isSubProcessor: {
            type: 'checkbox',
            initialValue: state?.isSubProcessor ?? false,
            label: t`This vendor is our sub-processor`,
            isOptional: true,
        },
        location: {
            type: 'text',
            initialValue: state?.location ?? '',
            label: t`Data location`,
            isOptional: true,
            shownIf: {
                fieldName: 'isSubProcessor',
                operator: 'equals',
                value: true,
            },
        },
        integrations: {
            type: 'combobox',
            isMultiSelect: true,
            options,
            initialValue: transformIntegrationsToListBoxItemData(
                state?.integrations,
            ),
            label: t`Integrations`,
            placeholder: t`Search integrations`,
            isOptional: true,
            getSearchEmptyState: () => {
                return t`No integrations found`;
            },
            removeAllSelectedItemsLabel: t`Remove all integrations`,
            loaderLabel: t`Loading integrations`,
            getRemoveIndividualSelectedItemClickLabel: ({ itemLabel }) =>
                `Remove ${itemLabel}`,
            isLoading: isFetching && isLoading,
            hasMore: hasNextPage,
            onFetchOptions: onFetchIntegrations,
        },
        // TODO: https://drata.atlassian.net/browse/ENG-70842 - Deletion of selected item not working properly, will be addressed in upcoming ticket
        user: {
            type: 'combobox',
            options: userOptions,
            initialValue: transformUserResponseDtoToListBoxItemData(
                state?.user,
            ),
            label: t`Security owner`,
            isOptional: true,
            loaderLabel: t`Loading security owners`,
            placeholder: t`Search by name`,
            getSearchEmptyState: () => t`No security owners found`,
            clearSelectedItemButtonLabel: t`Clear security owner`,
            isLoading: isFetchingUsers && isLoadingUsers,
            hasMore: hasMoreUsers,
            onFetchOptions: ({ search, increasePage }) => {
                onFetchUsers({
                    search,
                    increasePage,
                    excludeReadOnlyUsers: true,
                    roles: SECURITY_OWNER_ROLES,
                });
            },
            helpText: t`A security owner is responsible for reviewing this vendor’s security posture.`,
        },
        // TODO: https://drata.atlassian.net/browse/ENG-70842 - Deletion of selected item not working properly, will be addressed in upcoming ticket
        contact: {
            type: 'combobox',
            options: contactOptions,
            initialValue: transformUserResponseDtoToListBoxItemData(
                state?.contact,
            ),
            label: t`Vendor relationship contact`,
            isOptional: true,
            loaderLabel: t`Loading vendor contacts`,
            placeholder: t`Search by name`,
            getSearchEmptyState: () => t`No vendor contacts found`,
            clearSelectedItemButtonLabel: t`Clear contact`,
            isLoading: isFetchingContacts && isLoadingContacts,
            hasMore: hasMoreContacts,
            onFetchOptions: ({ search, increasePage }) => {
                onFetchContacts({
                    search,
                    increasePage,
                    roles: VENDOR_CONTACT_ROLES,
                });
            },
            helpText: t`This is the contact to reach out to if you have questions about this vendor.`,
        },
        // TODO: https://drata.atlassian.net/browse/ENG-70842 - Optional field validation not working properly, will be addressed in upcoming ticket
        cost: {
            type: 'text',
            initialValue: state?.cost ? formatCurrencyValue(state.cost) : '',
            label: t`Annual contract value`,
            isOptional: true,
            // This should be uncommented when the ticket is fixed
            // validator: z
            //     .string()
            //     .optional()
            //     .nullable()
            //     .transform((v) => v?.trim() ?? '')
            //     .refine(
            //         (value) => {
            //             if (isNil(value) || value.trim() === '') {
            //                 return true;
            //             }

            //             return /^\d+(?:\.\d{1,2})?$/.test(value);
            //         },
            //         {
            //             message:
            //                 'Only numbers with up to 2 decimal places are allowed',
            //         },
            //     )
            //     .refine(
            //         (value) => {
            //             if (isNil(value) || value.trim() === '') {
            //                 return true;
            //             }

            //             return Number(value) >= 0;
            //         },
            //         {
            //             message: 'Value must be zero or positive',
            //         },
            //     ),
        },
        notes: {
            type: 'textarea',
            initialValue: state?.notes ?? '',
            label: t`Additional notes`,
            isOptional: true,
        },
    };

    return baseSchema;
};
