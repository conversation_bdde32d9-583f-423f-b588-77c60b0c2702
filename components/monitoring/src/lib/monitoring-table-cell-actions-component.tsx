import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { MonitoringTableCellActionsModel } from './models/monitoring-table-cell-actions.model';
import type { MonitoringTableCellProps } from './types/monitoring.types';

export const MonitoringTableCellActionsComponent = observer(
    ({ row: { original } }: MonitoringTableCellProps): React.ReactNode => {
        const { testId, checkStatus, testType } = original;

        const model = new MonitoringTableCellActionsModel(
            testId,
            checkStatus,
            testType,
        );

        // Don't render dropdown if no actions are available
        if (!model.hasActions) {
            return null;
        }

        return (
            <SchemaDropdown
                isIconOnly
                colorScheme="neutral"
                level="tertiary"
                startIconName="HorizontalMenu"
                label={t`More options`}
                data-testid="MonitoringTableCellActionsComponent"
                data-id="CXqNbR4t"
                items={model.actionItems}
            />
        );
    },
);
