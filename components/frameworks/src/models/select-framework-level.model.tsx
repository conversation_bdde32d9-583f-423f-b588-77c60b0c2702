import type { FrameworkDetailsController } from '@controllers/frameworks';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type {
    FrameworkResponseDto,
    LevelImpactResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import { type FormSchema, UniversalFormField } from '@ui/forms';
import { SelectFrameworkLevelModalContentBodyLevelImpact } from '../components/select-framework-level-modal-content/select-framework-level-modal-content-body-level-impact';
import { getLevelLabel } from '../helpers/get-level-label.helper';
import { isValueLabel } from '../helpers/is-value-label.helper';

export class SelectFrameworkLevelModel {
    frameworksDetailsController: FrameworkDetailsController;
    selectedLevel?: number;

    constructor(frameworksDetailsController: FrameworkDetailsController) {
        this.frameworksDetailsController = frameworksDetailsController;

        makeAutoObservable(this);
    }

    get isLoadingOptions(): boolean {
        return this.frameworksDetailsController.isLoadingLevel;
    }

    get levelOptions(): ListBoxItemData[] {
        return Object.entries(
            this.frameworksDetailsController.levels?.options ?? {},
        )
            .filter(isValueLabel)
            .map(([label, value]) => {
                return {
                    id: String(value),
                    label: getLevelLabel(label),
                    value: String(value),
                };
            });
    }

    get formSchema(): FormSchema {
        return {
            level: {
                type: 'custom',
                validateWithDefault: 'select',
                label:
                    this.frameworksDetailsController.frameworkDetails
                        ?.levelLabel ?? t`Level`,
                options: this.levelOptions,
                initialValue: this.levelOptions.find((option) => {
                    return (
                        option.label ===
                        getLevelLabel(
                            this.frameworksDetailsController.selectedLevel ??
                                '',
                        )
                    );
                }),
                render: action((fieldProps) => {
                    return (
                        <>
                            <UniversalFormField
                                __fromCustomRender
                                name={fieldProps.name}
                                formId={fieldProps.formId}
                                data-id={fieldProps['data-id']}
                            />

                            {fieldProps.value && fieldProps.isDirty && (
                                <SelectFrameworkLevelModalContentBodyLevelImpact
                                    data-id="3muGe0R5"
                                    selectFrameworkLevelModel={this}
                                />
                            )}
                        </>
                    );
                }),
            },
            privacy: {
                type: 'custom',
                validateWithDefault: 'checkbox',
                label: t`Include privacy requirements`,
                initialValue:
                    this.frameworksDetailsController.frameworkDetails
                        ?.privacy ?? false,
                render: action((fieldProps) => {
                    if (
                        this.frameworksDetailsController.frameworkDetails
                            ?.tag !== 'NIST80053'
                    ) {
                        return null;
                    }

                    return (
                        <UniversalFormField
                            __fromCustomRender
                            name={fieldProps.name}
                            formId={fieldProps.formId}
                            data-id={fieldProps['data-id']}
                        />
                    );
                }),
            },
        };
    }

    get modalTitle(): string {
        switch (this.frameworksDetailsController.frameworkDetails?.tag) {
            case 'CMMC': {
                return t`Set the level`;
            }
            case 'DORA': {
                return t`Set the Applicability`;
            }
            case 'FFIEC': {
                return t`Set the maturity level`;
            }
            case 'NIST80053': {
                return t`Set the control baseline`;
            }
            default: {
                return t`Unknown framework`;
            }
        }
    }

    get frameworkTag(): FrameworkResponseDto['tag'] {
        return this.frameworksDetailsController.frameworkDetails?.tag ?? 'NONE';
    }

    get levelImpact(): LevelImpactResponseDto | null {
        if (
            !this.frameworksDetailsController.frameworkDetails ||
            !this.selectedLevel
        ) {
            return null;
        }

        if (!this.frameworksDetailsController.isLoadingLevelImpact) {
            this.frameworksDetailsController.getLevelImpactQuery.load({
                path: {
                    frameworkId:
                        this.frameworksDetailsController.frameworkDetails.id,
                },
                query: {
                    level: this.selectedLevel,
                },
            });
        }

        if (this.frameworksDetailsController.levelImpact) {
            return this.frameworksDetailsController.levelImpact;
        }

        return null;
    }

    get isLoadingLevelImpact(): boolean {
        return this.frameworksDetailsController.isLoadingLevelImpact;
    }
}
