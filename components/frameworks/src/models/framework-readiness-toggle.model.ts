import { makeAutoObservable } from '@globals/mobx';

const FRAMEWORK_READINESS_TOGGLE_STORAGE_KEY = 'framework-readiness-toggle';

class FrameworkReadinessToggleModel {
    privateReadinessToggle: 'control' | 'requirement';

    get readinessToggle(): 'control' | 'requirement' {
        return this.privateReadinessToggle;
    }

    set readinessToggle(value: 'control' | 'requirement') {
        window.sessionStorage.setItem(
            FRAMEWORK_READINESS_TOGGLE_STORAGE_KEY,
            value,
        );

        this.privateReadinessToggle = value;
    }

    constructor() {
        makeAutoObservable(this);

        this.privateReadinessToggle =
            (window.sessionStorage.getItem(
                FRAMEWORK_READINESS_TOGGLE_STORAGE_KEY,
            ) as 'control' | 'requirement' | null) ?? 'control';
    }
}

export const sharedFrameworkReadinessToggleModel =
    new FrameworkReadinessToggleModel();
