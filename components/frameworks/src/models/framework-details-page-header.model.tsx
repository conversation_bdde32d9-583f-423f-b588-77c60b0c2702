import { sharedFrameworkReadinessToggleModel } from '@components/frameworks';
import { sharedFrameworkDetailsController } from '@controllers/frameworks';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { Loader } from '@cosmos/components/loader';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import {
    criticalBackgroundStrongInitial,
    successBackgroundModerate,
} from '@cosmos/constants/tokens';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import {
    DataDonut,
    type DataDonutSliceData,
} from '@cosmos-lab/components/data-donut';
import {
    FrameworkBadge,
    getFrameworkBadge,
} from '@cosmos-lab/components/framework-badge';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { CustomFrameworkDetailsPageHeaderActionStack } from '../components/custom-framework-details-page-header-action-stack';
import { FrameworkDetailsPageHeaderActionStack } from '../components/framework-details-page-header-action-stack';
import { calculatePercentage } from '../helpers/frameworks-metrics.helper';

export class FrameworkDetailsPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    get breadcrumbs(): Breadcrumb[] {
        return [
            {
                label: t`Frameworks`,
                /**
                 * Example:
                 * from - /workspaces/1/compliance/frameworks/all/current/3/requirements
                 * to   - /workspaces/1/compliance/frameworks/all/current.
                 */
                pathname: window.location.pathname
                    .split('/')
                    .slice(0, -2)
                    .join('/'),
            },
        ];
    }

    get title(): string {
        const { isLoading, frameworkDetails } =
            sharedFrameworkDetailsController;

        if (!frameworkDetails || isLoading) {
            return t`Loading...`;
        }

        return frameworkDetails.name;
    }

    get slot(): React.JSX.Element {
        const { isLoading, frameworkDetails } =
            sharedFrameworkDetailsController;

        if (!frameworkDetails || isLoading) {
            return <Loader isSpinnerOnly label={t`Loading...`} />;
        }

        return (
            <FrameworkBadge
                badgeName={getFrameworkBadge(frameworkDetails.tag)}
            />
        );
    }

    get actionStack(): React.JSX.Element {
        return this.isCustomFramework ? (
            <CustomFrameworkDetailsPageHeaderActionStack />
        ) : (
            <FrameworkDetailsPageHeaderActionStack />
        );
    }

    get isCustomFramework(): boolean {
        const { frameworkDetails } = sharedFrameworkDetailsController;

        return frameworkDetails?.tag === 'CUSTOM';
    }

    private createDonutData(
        ready: number,
        total: number,
    ): DataDonutSliceData[] & { length: 2 | 1 | 3 | 4 } {
        const notReady = total - ready;

        return [
            {
                label: t`Ready`,
                value: ready,
                color: successBackgroundModerate,
            },
            {
                label: t`Not ready`,
                value: notReady,
                color: criticalBackgroundStrongInitial,
            },
        ] as DataDonutSliceData[] & {
            length: 2;
        };
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const { isLoading, frameworkDetails } =
            sharedFrameworkDetailsController;

        const {
            totalInScopeControls,
            numReadyInScopeControls,
            numInScopeRequirements,
            numReadyInScopeRequirements,
        } = frameworkDetails ?? {
            totalInScopeControls: 0,
            numReadyInScopeControls: 0,
            numInScopeRequirements: 0,
            numReadyInScopeRequirements: 0,
        };

        const { readinessToggle } = sharedFrameworkReadinessToggleModel;

        // Show loading state while data is being fetched
        if (isLoading) {
            return [
                {
                    id: 'framework-readiness-status',
                    'data-id': 'framework-readiness-status',
                    label: t`Readiness`,
                    value: <Skeleton />,
                    type: 'REACT_NODE',
                },
                {
                    id: 'framework-requirements-ready',
                    'data-id': 'framework-requirements-ready',
                    label: t`Requirements ready`,
                    value: <Skeleton />,
                    type: 'REACT_NODE',
                },
                {
                    id: 'framework-controls-ready',
                    'data-id': 'framework-controls-ready',
                    label: t`Controls ready`,
                    value: <Skeleton />,
                    type: 'REACT_NODE',
                },
            ];
        }

        const readinessCalculationMethod =
            readinessToggle === 'control' ? t`by controls` : t`by requirements`;

        // Calculate readiness donut data based on toggle
        const readinessReady =
            readinessToggle === 'control'
                ? calculatePercentage(
                      numReadyInScopeControls,
                      totalInScopeControls,
                  )
                : calculatePercentage(
                      numReadyInScopeRequirements,
                      numInScopeRequirements,
                  );

        const readinessTotal =
            readinessToggle === 'control'
                ? totalInScopeControls
                : numInScopeRequirements;

        return [
            {
                id: 'framework-readiness-status',
                'data-id': 'framework-readiness-status',
                label: t`Readiness (${readinessCalculationMethod})`,
                value: (
                    <Stack align="center" gap="2x">
                        <DataDonut
                            data-id="framework-readiness-donut"
                            size="sm"
                            unit={t`%`}
                            values={this.createDonutData(
                                readinessReady,
                                readinessTotal,
                            )}
                        />

                        <Text size="200">{t`${readinessReady}%`}</Text>
                    </Stack>
                ),
                type: 'REACT_NODE',
            },
            {
                id: 'framework-requirements-ready',
                'data-id': 'framework-requirements-ready',
                label: t`Requirements ready`,
                value: (
                    <Stack align="center" gap="2x">
                        <DataDonut
                            data-id="framework-requirements-donut"
                            size="sm"
                            unit={t`requirement`}
                            values={this.createDonutData(
                                numReadyInScopeRequirements,
                                numInScopeRequirements,
                            )}
                        />

                        <Text size="200">
                            {numReadyInScopeRequirements}/
                            {numInScopeRequirements}
                        </Text>
                    </Stack>
                ),
                type: 'REACT_NODE',
            },
            {
                id: 'framework-controls-ready',
                'data-id': 'framework-controls-ready',
                label: t`Controls ready`,
                value: (
                    <Stack align="center" gap="2x">
                        <DataDonut
                            data-id="framework-controls-donut"
                            size="sm"
                            unit={t`control`}
                            values={this.createDonutData(
                                numReadyInScopeControls,
                                totalInScopeControls,
                            )}
                        />

                        <Text size="200">
                            {numReadyInScopeControls}/{totalInScopeControls}
                        </Text>
                    </Stack>
                ),
                type: 'REACT_NODE',
            },
        ];
    }
}
