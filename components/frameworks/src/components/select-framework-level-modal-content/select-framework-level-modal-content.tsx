import { isNil } from 'lodash-es';
import { useCallback, useMemo, useRef } from 'react';
import {
    type FrameworkDetailsController,
    sharedFrameworkLevelMutationController,
} from '@controllers/frameworks';
import { modalController } from '@controllers/modal';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import type { FormValues } from '@ui/forms';
import { SELECT_FRAMEWORK_LEVEL_MODAL_ID } from '../../constants';
import { SelectFrameworkLevelModel } from '../../models/select-framework-level.model';
import { SelectFrameworkLevelModalContentBody } from './select-framework-level-modal-content-body';

interface Props {
    frameworksDetailsController: FrameworkDetailsController;
}

export const SelectFrameworkLevelModalContent = observer(
    ({ frameworksDetailsController }: Props): React.JSX.Element => {
        const navigate = useNavigate();
        const formRef = useRef<HTMLFormElement & { submitForm: () => void }>(
            null,
        );

        const { currentWorkspaceId } = sharedWorkspacesController;

        const selectFrameworkLevelModel = useMemo(
            () => new SelectFrameworkLevelModel(frameworksDetailsController),
            [frameworksDetailsController],
        );

        const { modalTitle } = useMemo(
            () => selectFrameworkLevelModel,
            [selectFrameworkLevelModel],
        );

        const handleCancel = useCallback(() => {
            modalController.closeModal(SELECT_FRAMEWORK_LEVEL_MODAL_ID);
        }, []);

        const handleSave = useCallback(() => {
            formRef.current?.submitForm();
        }, []);

        const handleSubmit = useCallback(
            async (formValues: FormValues) => {
                const { level, privacy } =
                    // This is safe as long as the form validation is applied.
                    formValues as {
                        level: { value: string };
                        privacy: boolean;
                    };

                await when(
                    () => !isNil(frameworksDetailsController.frameworkDetails),
                );

                // This should be a given at this point, but TypeScript doesn't know that
                if (isNil(frameworksDetailsController.frameworkDetails)) {
                    return;
                }

                const frameworkId =
                    frameworksDetailsController.frameworkDetails.id;

                const frameworkTag =
                    frameworksDetailsController.frameworkDetails.tag;

                sharedFrameworkLevelMutationController.updateFrameworkLevel(
                    frameworkId,
                    Number(level.value),
                    frameworkTag === 'NIST80053' ? privacy : null,
                );

                when(
                    () =>
                        !sharedFrameworkLevelMutationController.isLoading &&
                        !sharedFrameworkLevelMutationController.hasError,
                    () => {
                        modalController.closeModal(
                            SELECT_FRAMEWORK_LEVEL_MODAL_ID,
                        );

                        navigate(
                            `/workspaces/${currentWorkspaceId}/compliance/frameworks/all/current/${frameworkId}/requirements`,
                        );
                    },
                );
            },
            [
                currentWorkspaceId,
                frameworksDetailsController.frameworkDetails,
                navigate,
            ],
        );

        return (
            <>
                <Modal.Header
                    title={modalTitle}
                    closeButtonAriaLabel={t`Close`}
                    onClose={() => {
                        modalController.closeModal(
                            SELECT_FRAMEWORK_LEVEL_MODAL_ID,
                        );
                    }}
                />

                <Modal.Body>
                    <Stack
                        gap="6x"
                        direction="column"
                        data-testid="SelectFrameworkLevelModalContent"
                        data-id="goWnAw83"
                    >
                        <SelectFrameworkLevelModalContentBody
                            formRef={formRef}
                            handleSubmit={handleSubmit}
                            selectFrameworkLevelModel={
                                selectFrameworkLevelModel
                            }
                        />
                    </Stack>
                </Modal.Body>

                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: handleCancel,
                        },
                        {
                            label: t`Save`,
                            level: 'primary',
                            colorScheme: 'primary',
                            onClick: handleSave,
                            isLoading:
                                sharedFrameworkLevelMutationController.isLoading,
                        },
                    ]}
                />
            </>
        );
    },
);
