import { t } from '@globals/i18n/macro';

export function getLevelLabel(requirementIndexTag: string): string {
    switch (requirementIndexTag) {
        case 'SECURITY_LOW': {
            return t`Security - Low`;
        }
        case 'SECURITY_MODERATE': {
            return t`Security - Moderate`;
        }
        case 'SECURITY_HIGH': {
            return t`Security - High`;
        }
        case 'LEVEL_1': {
            return t`Level 1`;
        }
        case 'LEVEL_2': {
            return t`Level 2`;
        }
        case 'BASELINE': {
            return t`Baseline`;
        }
        case 'EVOLVING': {
            return t`Evolving`;
        }
        case 'INTERMEDIATE': {
            return t`Intermediate`;
        }
        case 'ADVANCED': {
            return t`Advanced`;
        }
        case 'INNOVATIVE': {
            return t`Innovative`;
        }
        case 'SIMPLIFIED': {
            return t`Simplified`;
        }
        case 'STANDARD': {
            return t`Standard`;
        }
        case 'IG1': {
            return t`IG1`;
        }
        case 'IG2': {
            return t`IG2`;
        }
        case 'IG3': {
            return t`IG3`;
        }
        default: {
            return t`Unknown level`;
        }
    }
}
