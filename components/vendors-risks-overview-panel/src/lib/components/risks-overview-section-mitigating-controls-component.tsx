import { isEmpty } from 'lodash-es';
import { ControlDetailsAccordionComponent } from '@components/control-details-accordion';
import { EmptyState } from '@cosmos/components/empty-state';
import { Text } from '@cosmos/components/text';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

interface RisksOverviewSectionMitigatingControlsComponentProps {
    vendorRiskDetail: RiskWithCustomFieldsResponseDto;
}

export const RisksOverviewSectionMitigatingControlsComponent = ({
    vendorRiskDetail,
}: RisksOverviewSectionMitigatingControlsComponentProps): React.JSX.Element => {
    const { controls } = vendorRiskDetail;

    return (
        <>
            <Text type="headline" size="400">
                {t`Mitigating controls`}
            </Text>
            {isEmpty(controls) && (
                <EmptyState
                    isInline
                    title={t`This risk is not mapped to any controls`}
                    illustrationName="Controls"
                />
            )}
            {controls.map((control) => (
                <ControlDetailsAccordionComponent
                    key={control.id}
                    id={String(control.id)}
                    name={control.name}
                    code={control.code}
                    description={control.description}
                    isReady={control.isReady}
                    archivedAt={control.archivedAt ?? ''}
                    data-id="vt4hw_dD"
                />
            ))}
        </>
    );
};
