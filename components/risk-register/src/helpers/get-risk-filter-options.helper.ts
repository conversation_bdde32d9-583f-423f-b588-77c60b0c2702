import type { RadioFieldGroupProps } from '@cosmos/components/radio-field-group';
import { getRiskTypeLabel } from './get-risk-type-label.helper';

export const getRiskFilterOptions = (): RadioFieldGroupProps['options'] =>
    [
        {
            label: getRiskTypeLabel('NEEDS_ATTENTION'),
            value: 'NEEDS_ATTENTION',
        },
        {
            label: getRiskTypeLabel('CUSTOM_RISKS'),
            value: 'CUSTOM_RISKS',
        },
        {
            label: getRiskTypeLabel('INTERNAL_RISKS'),
            value: 'INTERNAL_RISKS',
        },
        {
            label: getRiskTypeLabel('EXTERNAL_RISKS'),
            value: 'EXTERNAL_RISKS',
        },
    ] as const satisfies RadioFieldGroupProps['options'];
