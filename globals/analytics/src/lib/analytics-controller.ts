import { isNil } from 'lodash-es';
import { sharedConfigController } from '@globals/config';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { sharedCurrentUserController } from '@globals/current-user';
import { action, makeAutoObservable, when } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';
import { AnalyticsBrowser } from '@segment/analytics-next';

interface Chamaleon {
    show: (tourId: unknown, options: unknown) => void;
}

declare global {
    interface Window {
        /**
         * Analytics: AnalyticsBrowser;.
         */
        chmln: Chamaleon;
    }
}

const RESTRICTED_PARAMS_KEYS = [
    'code',
    'state',
    'error',
    'redirect_uri',
    'error_description',
];

class AnalyticsController {
    analytics: AnalyticsBrowser | null = null;
    needsAuth = true;
    hasAccessToken = false;

    constructor() {
        makeAutoObservable(this);
    }

    get #isInvalidAnalytics(): boolean {
        return (
            isNil(this.analytics) ||
            (this.needsAuth && !this.hasAccessToken) ||
            !sharedConfigController.configs.segment?.enabled
        );
    }

    startup() {
        when(
            () =>
                !isNil(sharedConfigController.configs.segment?.enabled) &&
                !isNil(sharedCurrentUserController.user),
            () => {
                if (!sharedConfigController.configs.segment?.enabled) {
                    return;
                }

                this.#init(true, true);
                this.identify();
            },
        );
    }

    page(routeCategory: string, pageTitle: string, pathname: string) {
        if (this.#isInvalidAnalytics) {
            return;
        }

        const { isAuditorInReadOnlyMode, isActAsReadOnly } =
            sharedCurrentUserController;

        // omit events for auditors and users in read-only mode
        if (isAuditorInReadOnlyMode || isActAsReadOnly) {
            return;
        }

        const { url, params } = this.#filterUrlParamsByKeys(
            window.location,
            RESTRICTED_PARAMS_KEYS,
        );

        // Why do we have to do this? isnt Segment doing it for us ?
        this.analytics
            ?.page(routeCategory, pageTitle, {
                title: this.#pathNameToTitle(pathname),
                url,
                search: params,
            })
            .catch((error) => {
                // TODO: error handling
                console.error(error);
            });
    }

    group(groupId = '', properties = {}) {
        if (this.#isInvalidAnalytics) {
            return;
        }

        this.analytics?.group(groupId, properties).catch((error) => {
            // TODO: error handling
            console.error(error);
        });
    }

    track(
        // TODO: put this events in an enum, should be the same as Product Board
        event: string,
        properties: Record<string, string> = {},
    ) {
        if (this.#isInvalidAnalytics) {
            return;
        }

        const { entryId: userId } = sharedCurrentUserController;

        if (!userId) {
            return;
        }

        const { url, params } = this.#filterUrlParamsByKeys(
            window.location,
            RESTRICTED_PARAMS_KEYS,
        );

        this.analytics
            ?.track(event, {
                userId,
                url,
                search: params,
                ...properties,
            })
            .catch((error) => {
                // TODO: error handling
                console.error(error);
            });
    }

    /**
     * TODO: put this events in an enum, should be the same as Product Board.
     */
    trackMany(events: { event: string; properties: Record<string, string> }[]) {
        events.forEach((event, index) => {
            /*
                TODO.10762: Currently analytics helper only have implemented single endpoint, for this kind of actions we need the
                Batch analytics API, the single endpoint only supports 1 event every 200ms so we are adding a timeout atm. The refactor
                will be considered in the ticket above
            */
            setTimeout(
                action(() => {
                    this.track(event.event, event.properties);
                }),
                550 * index,
            );
        });
    }

    identify() {
        if (this.#isInvalidAnalytics) {
            return;
        }

        const { isAuditorInReadOnlyMode, isActAsReadOnly } =
            sharedCurrentUserController;

        // omit events for auditors and users in read-only mode
        if (isAuditorInReadOnlyMode || isActAsReadOnly) {
            return;
        }

        const traits = this.#identifyTraits;

        const { url, params } = this.#filterUrlParamsByKeys(
            window.location,
            RESTRICTED_PARAMS_KEYS,
        );

        const { entryId } = sharedCurrentUserController;

        this.analytics
            ?.identify(entryId, traits ?? {}, {
                page: {
                    url,
                    search: params,
                },
            })
            .catch((error) => {
                // TODO: error handling
                console.error(error);
            });
    }

    #toTitleCase(str: string): string {
        if (isNil(str)) {
            return str;
        }

        return str.replaceAll(/\w\S*/g, (txt: string) => {
            return txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase();
        });
    }

    #pathNameToTitle(str: string): string {
        const replaceDash = str.replaceAll('-', ' ');
        const replaceSlash = replaceDash.replaceAll('/', ' - ');

        return this.#toTitleCase(`Drata${replaceSlash}`);
    }

    /**
     * TODO: implement the method.
     */
    destroy() {
        console.debug('Destroying analytics');
    }

    #init(needsAuth: boolean, hasAccessToken: boolean) {
        // if it's already loaded - bail
        if (!isNil(this.analytics)) {
            return;
        }
        this.needsAuth = needsAuth;
        this.hasAccessToken = hasAccessToken;

        // must be logged in to load analytics
        if (this.#isInvalidAnalytics) {
            return;
        }

        const { isAuditorInReadOnlyMode, isActAsReadOnly } =
            sharedCurrentUserController;

        // omit events for auditors and users in read-only mode
        if (isAuditorInReadOnlyMode || isActAsReadOnly) {
            return;
        }

        this.analytics = new AnalyticsBrowser();
        this.analytics.load({
            writeKey: sharedConfigController.configs.segment
                ?.writeKey as string,
        });

        // Helper function to avoid CSP issues with Chameleon ENG-66393
        Object.defineProperty(window, 'chameleonPerformAction', {
            value: (optionKey: string, tourId: unknown) => {
                // eslint-disable-next-line no-multi-assign -- code coming directly from Chameleon, need it to pass eslint
                const windowChmln = (window.chmln =
                    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- code coming directly from Chameleon, need it to pass eslint
                    (window.window as unknown as Window).chmln || []);
                const options = { [optionKey]: true };

                windowChmln.show(tourId, options);
            },
            writable: false,
            configurable: false,
        });
    }

    /**
     * IMPORTANT:
     *
     * This is the DTO of the identify traits for Segment
     * If we need to change this, please update it in the API as well
     * in segment-analytics.ts at #identifyTraits.
     *
     */
    get #identifyTraits(): Record<string, unknown> | null {
        const {
            user,
            isAdmin,
            isEmployee,
            isTechgov,
            isRiskManager,
            isControlManager,
            isPeopleOps: isPersonnelComplianceManager,
            isPolicyManager,
            isReviewer,
            isAuditor,
            hasAcceptedTerms: acceptedTerms,
        } = sharedCurrentUserController;

        if (!user || !sharedCurrentUserController.isRegularUser(user)) {
            return null;
        }

        const traits: Record<
            string,
            string | boolean | { id: string; name: string }
        > = {
            name: getFullName(user.firstName, user.lastName),
            email: user.email,
            createdAt: user.createdAt,
            jobTitle: user.jobTitle || '',
            isAdmin,
            isEmployee,
            isTechgov,
            isRiskManager,
            isControlManager,
            isPersonnelComplianceManager,
            isPolicyManager,
            isReviewer,
            isAuditor,
            acceptedTerms,
        };

        if (!isAuditor) {
            traits.groupId = user.accountId;
            const { company } = sharedCurrentCompanyController;

            /**
             * Company id and name are needed properties for the Intercom destination
             * passing these traits allows Intercom to associate a user to their group,
             * removal would prevent Intercom from associating a user to their company data.
             */
            if (!isNil(company)) {
                traits.company = {
                    id: company.accountId,
                    name: company.name,
                };
            }
        }

        return traits;
    }

    /**
     * TODO: We might be able to get rid of this.
     */
    #filterUrlParamsByKeys = (
        windowLocation: {
            origin?: string;
            pathname?: string;
            search?: string;
            hash?: string;
        } = {},
        restrictedParamKeys: string[] = [],
    ) => {
        const {
            origin = '',
            pathname = '',
            search: params = '',
            hash = '',
        } = windowLocation;

        const paramsObj = Object.fromEntries(new URLSearchParams(params));

        // eslint-disable-next-line unicorn/no-array-reduce -- copied this from web, we kept it for now
        const filteredParamsObj = Object.entries(paramsObj).reduce(
            (acc, [paramKey, paramValue]) => {
                return restrictedParamKeys.includes(paramKey)
                    ? acc
                    : {
                          ...acc,
                          [paramKey]: paramValue,
                      };
            },
            {},
        );

        const filteredParamsString = new URLSearchParams(
            filteredParamsObj,
        ).toString();

        const sanitizedParams = filteredParamsString
            ? `?${filteredParamsString}`
            : '';

        const sanitizedUrl = `${origin}${pathname}${sanitizedParams}${hash}`;

        return {
            url: sanitizedUrl,
            params: sanitizedParams,
        };
    };

    // TODO: trackRequirementFilters does not belong here but it needs to be somewhere
    // trackRequirementFilters(
    //     frameworkSlug,
    //     { isReady, category, subCategory, topic },
    // ) {
    //     const options = {
    //         frameworkName: frameworkSlug,
    //     };

    //     if (!isNil(isReady)) {
    //         options.isReady = isReady;
    //     }
    //     // ISO 27001 has a special category that has the form of an Array, if we encounter that case we only want to track it as `Annex A`
    //     if (!isNil(category)) {
    //         if (
    //             frameworkSlug === FrameworkSlug.ISO27001 &&
    //             category instanceof Array
    //         ) {
    //             options.requirementCategory = 'Annex A';
    //         } else {
    //             options.requirementCategory = category;
    //         }
    //     }

    //     if (!isNil(subCategory)) {
    //         options.requirementSubCategory = subCategory;
    //     }

    //     if (!isNil(topic)) {
    //         options.requirementTopic = topic;
    //     }

    //     if (!category && !topic) {
    //         options.requirementCategory = 'All Requirements';
    //     }

    //     analytics.track(ANALYTICS_EVENT_ENUM.REQUIREMENTS_FILTERED, options);
    // }
}

export const sharedAnalyticsController = new AnalyticsController();
