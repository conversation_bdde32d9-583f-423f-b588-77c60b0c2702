import {
    initialize,
    type LDClient,
    type LDFlagValue,
    type LDOptions,
    type LDSingleKindContext,
} from 'launchdarkly-js-client-sdk';
import { sharedAuthController } from '@controllers/auth';
import type { CompanyResponseDto, MeResponseDto } from '@globals/api-sdk/types';
import { sharedConfigController } from '@globals/config';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { sharedCurrentUserController } from '@globals/current-user';
import { makeAutoObservable, reaction, runInAction } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';

type FeatureKey =
    | 'release-custom-workflows'
    | 'release-vrm-public-trust-center-view-collection'
    | 'show-syncron-custom-reports'
    | 'release-constellation-opt-in'
    | 'release-bulk-import';

const DUMMY_USER = {
    key: 'dummy-user',
    kind: 'user',
    anonymous: true,
};
const EXCLUDED_COMPANY_DOMAINS = [
    // TODO: determine if we need to support this
    // 'split.io'
] as readonly string[];

function getClientConfiguration(): { clientId: string; options: LDOptions } {
    const { localServer = { projectName: '' }, clientId = '' } =
        sharedConfigController.configs.launchDarkly ?? {};

    if (localServer.enabled) {
        // eslint-disable-next-line no-console -- Intentionally logging local server configuration for debugging
        console.log(
            'LaunchDarkly LocalServer enabled',
            localServer.projectName,
        );

        return {
            clientId: localServer.projectName as string,
            options: {
                ...localServer.urls,
                eventCapacity: 200,
                // https://launchdarkly.github.io/js-client-sdk/interfaces/LDOptions.html#sendEventsOnlyForVariation
                sendEventsOnlyForVariation: true,
            },
        };
    }

    return {
        clientId,
        options: {
            eventCapacity: 200,
            // https://launchdarkly.github.io/js-client-sdk/interfaces/LDOptions.html#sendEventsOnlyForVariation
            sendEventsOnlyForVariation: true,
        },
    };
}

function getLaunchDarklySingleKindContext({
    user,
    company,
}: {
    user: MeResponseDto;
    company: CompanyResponseDto;
}): LDSingleKindContext {
    const shouldNotSendCompanyUserDataToLaunchDarkly =
        EXCLUDED_COMPANY_DOMAINS.includes(company.domain);

    if (shouldNotSendCompanyUserDataToLaunchDarkly) {
        return DUMMY_USER;
    }

    const emailDomain = user.email.split('@')[1];

    return {
        key: user.entryId,
        kind: 'user',
        anonymous: false,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        name: getFullName(user.firstName, user.lastName),
        avatar: user.avatarUrl,
        company: {
            // TODO: fix these deprecated role based checks
            domain: sharedCurrentUserController.isServiceUser
                ? emailDomain
                : company.domain,
            createdAt: company.createdAt,
        },
        companyName: company.name,
        companyCreatedAt: company.createdAt,
        domain: company.domain,
        groupId: company.accountId,
        accountType: company.accountType,
        jobTitle: user.jobTitle,
        // TODO: fix these deprecated role based checks
        isAdmin: sharedCurrentUserController.isAdmin,
        isEmployee: sharedCurrentUserController.isEmployee,
        isTechgov: sharedCurrentUserController.isTechgov,
        isAuditor: sharedCurrentUserController.isAuditor,
        isRiskManager: sharedCurrentUserController.isRiskManager,
        isControlManager: sharedCurrentUserController.isControlManager,
        isPersonnelComplianceManager: sharedCurrentUserController.isPeopleOps,
        isPolicyManager: sharedCurrentUserController.isPolicyManager,
        enabledFrameworks: company.frameworks.map(({ label }) => label),
    };
}

class FeatureFlagController {
    client: LDClient | null = null;
    flagsVersion = 0;

    constructor() {
        makeAutoObservable(this);
    }

    init = () => {
        if (
            !sharedCurrentUserController.user ||
            !sharedCurrentCompanyController.company
        ) {
            return;
        }

        const clientConfig = getClientConfiguration();
        const launchDarklyContext = getLaunchDarklySingleKindContext({
            user: sharedCurrentUserController.user as MeResponseDto,
            company: sharedCurrentCompanyController.company,
        });

        try {
            const client = initialize(
                clientConfig.clientId,
                launchDarklyContext,
                clientConfig.options,
            );

            client
                .waitForInitialization()
                .then(() => {
                    runInAction(() => {
                        this.client = client;
                        this.flagsVersion = this.flagsVersion + 1;
                    });

                    this.client?.on('change', () => {
                        runInAction(() => {
                            this.flagsVersion = this.flagsVersion + 1;
                        });
                    });
                })
                .catch((error) => {
                    console.error('Error initializing LaunchDarkly', error);
                });
        } catch (error) {
            console.error('Error initializing LaunchDarkly', error);
        }
    };

    identify({
        user,
        company,
    }: {
        user: MeResponseDto;
        company: CompanyResponseDto;
    }) {
        const launchDarklyContext = getLaunchDarklySingleKindContext({
            user,
            company,
        });

        this.client?.identify(launchDarklyContext).catch((error) => {
            console.error('Error identifying user in LaunchDarkly', error);
        });
    }

    /**
     * Don't use variation directly. Use the specific getters instead.
     * If someone knows how to make this private ANd observable, please do so.
     */
    variation = (
        featureKey: FeatureKey,
        defaultValue: LDFlagValue = false,
    ): LDFlagValue => {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions -- Reference to create dependency
        this.flagsVersion;

        return this.client?.variation(featureKey, defaultValue) ?? defaultValue;
    };

    get isEarlyAccessEnabled(): boolean {
        return Boolean(this.variation('release-constellation-opt-in', false));
    }

    get isReleaseCustomWorkflowsEnabled(): boolean {
        return Boolean(this.variation('release-custom-workflows', false));
    }

    get isReleaseVrmPublicTrustCenterViewCollection(): boolean {
        return Boolean(
            this.variation('release-vrm-public-trust-center-view-collection'),
        );
    }

    get isShowSyncronCustomReports(): boolean {
        return Boolean(this.variation('show-syncron-custom-reports'));
    }

    get isReleaseBulkImportEnabled(): boolean {
        return Boolean(this.variation('release-bulk-import', false));
    }
}

export const sharedFeatureFlagsController = new FeatureFlagController();

reaction(
    () => {
        const isAuthenticated = sharedAuthController.isUserAuthenticated;
        const clientId = sharedConfigController.configs.launchDarkly?.clientId;
        const currentUser = sharedCurrentUserController.user;
        const currentCompany = sharedCurrentCompanyController.company;

        return Boolean(
            isAuthenticated && clientId && currentUser && currentCompany,
        );
    },
    (canInitialize: boolean) => {
        if (canInitialize) {
            sharedFeatureFlagsController.init();
        }
    },
);
