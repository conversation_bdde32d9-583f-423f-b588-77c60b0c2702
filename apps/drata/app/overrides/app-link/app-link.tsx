import type { ReactNode } from 'react';
import {
    // eslint-disable-next-line no-restricted-imports -- Official use case of @cosmos/components/link
    Link as CosmosLink,
    type LinkProps as CosmosLinkProps,
} from '@cosmos/components/link';
// eslint-disable-next-line no-restricted-imports -- Official use case of @remix-run/react
import { Link as RemixLink } from '@remix-run/react';

export type AppLinkProps = Omit<
    CosmosLinkProps<typeof RemixLink>,
    'component' | 'to'
> & {
    href: string;
    /**
     * Add any valid text or html as children.
     */
    children?: ReactNode;
};

/**
 * Render a remix link as our own.
 *
 * //https://remix.run/docs/en/main/components/link.
 */
export const AppLink = ({
    colorScheme,
    'data-id': dataId,
    href,
    isExternal,
    label,
    children,
    size,
}: AppLinkProps): React.JSX.Element => {
    return (
        <CosmosLink
            colorScheme={colorScheme}
            component={RemixLink}
            data-id={dataId}
            isExternal={isExternal}
            label={label}
            size={size}
            to={href}
            prefetch={isExternal ? 'none' : 'intent'}
            data-testid="AppLink"
        >
            {children}
        </CosmosLink>
    );
};
