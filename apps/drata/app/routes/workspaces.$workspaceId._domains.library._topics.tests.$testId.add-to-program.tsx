import { sharedConnectionsController } from '@controllers/connections';
import { activeLibraryTestController } from '@controllers/library-test';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { LibraryTestAddToProgramPageHeaderModel } from '@models/library-test';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { AddTestToProgramView } from '@views/add-test-to-program';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: t`Add Test to Program` }];

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { testId } = params;
        const { validateTestTemplate, loadTest } = activeLibraryTestController;
        const { isLoaded } = sharedWorkspacesController;

        if (!Number(testId)) {
            throw new Error('Invalid testId');
        }

        if (!isLoaded) {
            sharedCurrentCompanyController.load();
        }

        validateTestTemplate(Number(testId));
        loadTest(Number(testId));
        sharedConnectionsController.allConfiguredConnectionsQuery.load();

        return {
            pageHeader: new LibraryTestAddToProgramPageHeaderModel(),
            tabs: [],
        };
    },
);

const AddTestToProgram = (): React.JSX.Element => {
    return (
        <AddTestToProgramView
            data-testid="AddTestToProgram"
            data-id="Kj7LpQxZ"
        />
    );
};

export default AddTestToProgram;
