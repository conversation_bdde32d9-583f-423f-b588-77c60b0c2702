import { isEmpty } from 'lodash-es';
import { sharedFrameworkDetailsController } from '@controllers/frameworks';
import { sharedRequirementDetailsController } from '@controllers/requirements';
import { t } from '@globals/i18n/macro';
import { action, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { RequirementDetailsPageHeaderModel } from '@models/requirement-details';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { frameworkId, requirementId } = params;

        if (!Number(requirementId) || !Number(frameworkId)) {
            throw new Error('Requirement and framework id are required.');
        }
        // Reactively load framework details when currentWorkspace becomes available
        when(
            () => !isEmpty(sharedWorkspacesController.currentWorkspace),
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                if (!currentWorkspace) {
                    return;
                }

                sharedFrameworkDetailsController.frameworkDetailsQuery.load({
                    path: {
                        xProductId: Number(currentWorkspace.id),
                        frameworkId: Number(frameworkId),
                    },
                });
            },
        );

        sharedRequirementDetailsController.requirementDetailsQuery.load({
            path: { requirementId: Number(requirementId) },
        });

        return {
            pageHeader: new RequirementDetailsPageHeaderModel(),
            contentNav: {
                tabs: [
                    {
                        topicPath: `compliance/frameworks/all/current/${frameworkId}/requirements/${requirementId}/overview`,
                        label: t`Overview`,
                    },
                    {
                        topicPath: `compliance/frameworks/all/current/${frameworkId}/requirements/${requirementId}/controls`,
                        label: t`Controls`,
                    },
                ],
            },
        };
    },
);

export const meta: MetaFunction = () => [{ title: t`Requirements Details` }];

const FrameworksRequirementsDetailsOverview = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="FrameworksRequirementsDetailsOverview"
            data-id="615d6Eze"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default FrameworksRequirementsDetailsOverview;
