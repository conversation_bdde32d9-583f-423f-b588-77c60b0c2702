import {
    sharedAssetsController,
    sharedAssetsProviderListController,
} from '@controllers/assets';
import {
    sharedUsersController,
    sharedUsersInfiniteController,
} from '@controllers/users';
import { ActionStack } from '@cosmos/components/action-stack';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet, useNavigate } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: t`Assets` }];
};

const ActionStackNavigate = (): React.JSX.Element => {
    const navigate = useNavigate();

    return (
        <ActionStack
            isFullWidth
            data-id={`action-stack`}
            data-testid="ActionStackNavigate"
            actions={[
                {
                    actionType: 'button',
                    id: 'assets-action-stack-1',
                    typeProps: {
                        label: t`Add asset`,
                        onClick: () => {
                            navigate('risk/assets/add');
                        },
                    },
                },
            ]}
        />
    );
};

export const clientLoader = action((): ClientLoader => {
    sharedAssetsController.assets.load();
    sharedAssetsProviderListController.loadProviders();
    sharedUsersController.usersList.load();
    sharedUsersInfiniteController.loadUsers({ withAllUsers: true });

    return {
        pageHeader: {
            title: t`Assets`,
            pageId: 'assets-page-header',
            actionStack: <ActionStackNavigate />,
        },
    };
});

const RiskAssets = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="RiskAssets" data-id="Eg9s6DXK">
            <Outlet />
        </RouteLandmark>
    );
};

export default RiskAssets;
