import { sharedRiskSettingsController } from '@controllers/risk';
import { sharedVendorsProfileRisksController } from '@controllers/vendors';
import { action } from '@globals/mobx';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Vendors Current Risks' }];

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { vendorId } = params;

        if (!vendorId || Number.isNaN(Number(vendorId))) {
            throw new Error('Invalid vendorId');
        }

        sharedVendorsProfileRisksController.setCurrentVendorId(
            Number(vendorId),
        );
        sharedVendorsProfileRisksController.loadInitialData();
        sharedRiskSettingsController.load();

        return null;
    },
);

const VendorsCurrentRisks = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="VendorsCurrentRisks"
            data-id="Bwg9WWyA"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default VendorsCurrentRisks;
