import { useCallback, useMemo, useState } from 'react';
import { sharedAccessReviewApplicationGroupsController } from '@controllers/access-reviews';
import {
    activeAccessReviewsApplicationsController,
    activeAccessReviewsApplicationsUserController,
} from '@controllers/access-reviews-applications';
import {
    Datatable,
    type FetchDataResponseParams,
    type GlobalFilterState,
} from '@cosmos/components/datatable';
import type { UserAccessReviewApplicationResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { accessReviewPersonnelAdapter } from './adapters/access-review-personnel.adapter';
import { getColumns } from './constants/applications-personnel-user.constants';
import { createAccessReviewFiltersModel } from './models/access-review-filters.model';

export const AccessReviewPersonnelView = observer((): JSX.Element => {
    const [isSummaryView, setIsSummaryView] = useState(true);
    const navigate = useNavigate();

    // =========================================================================
    // Controller data
    // =========================================================================
    const {
        accessReviewApplicationUsersList,
        accessReviewApplicationUsersTotal,
        isLoading,
        loadAccessReviewApplicationUsers,
        updateFilterValues,
        accessReviewApplicationConnections,
    } = activeAccessReviewsApplicationsUserController;

    const { currentWorkspace, isLoading: isWorkspaceLoading } =
        sharedWorkspacesController;

    const {
        isManuallyAddedApplication,
        isLoading: isLoadingSource,
        accessReviewsApplicationsDetails,
        warnings,
    } = activeAccessReviewsApplicationsController;

    const {
        isLoading: isLoadingGroups,
        hasNextPage,
        accessReviewApplicationGroupsList,
    } = sharedAccessReviewApplicationGroupsController;

    // Create a non-reactive wrapper to avoid MobX tracking issues
    const loadAccessReviewApplicationUsersConnections = useCallback(() => {
        activeAccessReviewsApplicationsUserController.loadAccessReviewApplicationUsersConnections();
    }, []);

    const { clientType, id, name, source } = accessReviewsApplicationsDetails;

    const isLoadingData =
        isLoading || isLoadingSource || isLoadingGroups || isWorkspaceLoading;

    const workspaceId = currentWorkspace?.id ?? 1;

    // =========================================================================
    // Filters model
    // =========================================================================
    const filtersModel = useMemo(
        () =>
            createAccessReviewFiltersModel({
                clientType,
                source,
                isManuallyAddedApplication,
                accessReviewApplicationGroupsList,
                loadAccessReviewApplicationGroupsForPersonnel: (params) => {
                    sharedAccessReviewApplicationGroupsController.loadApplicationGroups(
                        params,
                    );
                },
                accessReviewApplicationConnections,
                loadAccessReviewApplicationUsersConnections,
                warnings,
                updateFilterValues: (filters: GlobalFilterState['filters']) => {
                    updateFilterValues(filters);
                },
                hasMoreGroups: hasNextPage,
                isLoadingGroups,
            }),
        [
            clientType,
            source,
            isManuallyAddedApplication,
            accessReviewApplicationGroupsList,
            accessReviewApplicationConnections,
            loadAccessReviewApplicationUsersConnections,
            warnings,
            updateFilterValues,
            hasNextPage,
            isLoadingGroups,
        ],
    );

    const { warningFiltersObject, tableFilters } = filtersModel;

    // Handle data fetching with filter cleanup handled by model
    const handleFetchData = useCallback(
        (fetchParams: FetchDataResponseParams | null) => {
            if (!fetchParams) {
                return;
            }

            // Process and clean filter values - model returns cleaned params
            const cleanedParams = filtersModel.handleFilterChange(fetchParams);

            // Load data with cleaned params
            if (cleanedParams) {
                loadAccessReviewApplicationUsers(cleanedParams);
            }
        },
        [loadAccessReviewApplicationUsers, filtersModel],
    );

    // =========================================================================
    // Event handlers
    // =========================================================================

    /**
     * Handles changing between summary and detailed views.
     */
    const handleSummaryViewChange = useCallback((value: boolean) => {
        setIsSummaryView(value);
    }, []);

    /**
     * Handles row click to navigate to personnel details.
     */
    const handleRowClick = useCallback(
        ({ row }: { row: UserAccessReviewApplicationResponseDto }) => {
            navigate(
                `/workspaces/${workspaceId}/governance/access-review/applications/${id}/personnel/${row.clientType}/user/${row.id}`,
            );
        },
        [navigate, workspaceId, id],
    );

    // =========================================================================
    // Memoized values
    // =========================================================================
    const filteredColumns = useMemo(
        () =>
            getColumns().filter((column) =>
                column.showIf({
                    isSummaryView,
                    isManuallyAddedApplication,
                    missingMFA: warningFiltersObject.warningFilters.missingMFA,
                }),
            ),
        [
            isSummaryView,
            isManuallyAddedApplication,
            warningFiltersObject.warningFilters.missingMFA,
        ],
    );

    // Use the adapter to transform data for the view
    const {
        tableData,
        totalCount,
        tableSettingsTriggerProps,
        tableSearchProps,
        emptyStateProps,
        filterViewModeProps,
    } = useMemo(
        () =>
            accessReviewPersonnelAdapter({
                accessReviewApplicationUsersList,
                accessReviewApplicationUsersTotal,
                isManuallyAddedApplication,
                name,
                isSummaryView,
                handleSummaryViewChange,
            }),
        [
            accessReviewApplicationUsersList,
            accessReviewApplicationUsersTotal,
            isManuallyAddedApplication,
            name,
            isSummaryView,
            handleSummaryViewChange,
        ],
    );

    return (
        <Datatable
            key={`${clientType}-${source}-${isManuallyAddedApplication}`}
            isLoading={isLoadingData}
            tableId="personnel-datatable"
            data-testid="PersonnelTable"
            data-id="personnel-datatable"
            total={totalCount}
            columns={filteredColumns}
            filterProps={tableFilters}
            emptyStateProps={emptyStateProps}
            tableSearchProps={tableSearchProps}
            filterViewModeProps={filterViewModeProps}
            tableSettingsTriggerProps={tableSettingsTriggerProps}
            data={tableData}
            onFetchData={handleFetchData}
            onRowClick={({ row }) => {
                handleRowClick({
                    row,
                });
            }}
        />
    );
});
