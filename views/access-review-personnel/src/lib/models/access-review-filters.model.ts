import { isEmpty, isEqual, isNil, isObject } from 'lodash-es';
import type {
    FetchDataResponseParams,
    FilterProps,
} from '@cosmos/components/datatable';
import type { Filter, FilterStateValue } from '@cosmos/components/filter-field';
import type {
    AccessReviewApplicationResponseDto,
    ApplicationGroupResponseDto,
    ClientTypeEnum,
    ConnectionResponseDto,
} from '@globals/api-sdk/types';
import { action, makeAutoObservable } from '@globals/mobx';
import {
    filtersByProvider,
    getFilters,
    type WarningFilters,
} from '../helpers/filter.helper';

interface FilterValue {
    value: unknown;
    filterType: string;
}

export class AccessReviewFiltersModel {
    clientType: ClientTypeEnum | undefined;
    source: string | undefined;
    isManuallyAddedApplication: boolean;
    accessReviewApplicationGroupsList: ApplicationGroupResponseDto[];
    loadAccessReviewApplicationGroupsForPersonnel: (params: {
        search?: string;
        applicationId?: number;
    }) => void;
    accessReviewApplicationConnections: ConnectionResponseDto[];
    loadAccessReviewApplicationUsersConnections: () => void;
    warnings?: AccessReviewApplicationResponseDto['warnings'];
    updateFilterValues: (
        filters: Record<
            string,
            {
                value?: FilterStateValue;
                filterType: Filter['filterType'];
            }
        >,
    ) => void;
    hasMoreGroups: boolean;
    isLoadingGroups: boolean;

    constructor(props: {
        clientType: ClientTypeEnum | undefined;
        source: string | undefined;
        isManuallyAddedApplication: boolean;
        accessReviewApplicationGroupsList: ApplicationGroupResponseDto[];
        loadAccessReviewApplicationGroupsForPersonnel: (params: {
            search?: string;
            applicationId?: number;
        }) => void;
        accessReviewApplicationConnections: ConnectionResponseDto[];
        loadAccessReviewApplicationUsersConnections: () => void;
        warnings?: AccessReviewApplicationResponseDto['warnings'];
        updateFilterValues: (
            filters: Record<
                string,
                {
                    value?: FilterStateValue;
                    filterType: Filter['filterType'];
                }
            >,
        ) => void;
        hasMoreGroups?: boolean;
        isLoadingGroups?: boolean;
    }) {
        this.clientType = props.clientType;
        this.source = props.source;
        this.isManuallyAddedApplication = props.isManuallyAddedApplication;
        this.accessReviewApplicationGroupsList =
            props.accessReviewApplicationGroupsList;
        this.loadAccessReviewApplicationGroupsForPersonnel =
            props.loadAccessReviewApplicationGroupsForPersonnel;
        this.accessReviewApplicationConnections =
            props.accessReviewApplicationConnections;
        this.loadAccessReviewApplicationUsersConnections =
            props.loadAccessReviewApplicationUsersConnections;
        this.warnings = props.warnings;
        this.updateFilterValues = props.updateFilterValues;
        this.hasMoreGroups = props.hasMoreGroups ?? false;
        this.isLoadingGroups = props.isLoadingGroups ?? false;

        makeAutoObservable(this, {
            handleFilterChange: action,
        });
    }

    /**
     * Add a local state for filter values.
     */
    #filterValues: Record<string, FilterValue> = {};

    /**
     * Getter for filter values.
     */
    get filterValues(): Record<string, FilterValue> {
        return this.#filterValues;
    }

    /**
     * Setter for filter values.
     */
    setFilterValues = (values: Record<string, FilterValue>): void => {
        this.#filterValues = values;
    };

    /**
     * Get warning filters based on provider.
     */
    get warningFiltersObject(): WarningFilters {
        return filtersByProvider(
            this.clientType,
            this.isManuallyAddedApplication,
            this.source,
        );
    }

    /**
     * Get table filters configuration with direct update handler.
     */
    get tableFilters(): FilterProps {
        return getFilters(
            this.accessReviewApplicationGroupsList,
            this.loadAccessReviewApplicationGroupsForPersonnel,
            this.accessReviewApplicationConnections,
            this.loadAccessReviewApplicationUsersConnections,
            this.warningFiltersObject,
            this.warnings ?? {
                formerPersonnel: 0,
                missingMfa: 0,
                unlinkedIdentities: 0,
                levelChange: 0,
                serviceAccounts: 0,
                totalWarnings: 0,
                id: 0,
                hasFailed: false,
            },
            this.isManuallyAddedApplication,
            (value) => {
                // Type guard to ensure value is the correct type
                if (value && isObject(value)) {
                    this.updateFilterValues(
                        value as Record<
                            string,
                            {
                                value?: FilterStateValue;
                                filterType: Filter['filterType'];
                            }
                        >,
                    );
                }
            },
            this.hasMoreGroups,
            this.isLoadingGroups,
        );
    }

    /**
     * Process filter values from the Datatable component.
     */
    processFilterValues = (
        filters: FetchDataResponseParams['globalFilter']['filters'],
    ): Record<string, FilterValue> => {
        if (isEmpty(filters)) {
            return {};
        }

        const newFilterValues: Record<string, FilterValue> = {};
        const enabledFilterIds = new Set(
            this.tableFilters.filters.map((filter) => filter.id),
        );

        Object.entries(filters).forEach(([key, filterData]) => {
            // Skip filters that are not enabled for the current provider configuration
            if (!enabledFilterIds.has(key)) {
                return;
            }

            // Handle empty arrays as valid filter values
            if (Array.isArray(filterData.value) || !isNil(filterData.value)) {
                newFilterValues[key] = {
                    value: filterData.value,
                    filterType: filterData.filterType,
                };
            }
        });

        return newFilterValues;
    };

    /**
     * Safely compares two filter values, handling undefined values.
     */
    safeCompareFilters = (newValue: unknown, oldValue: unknown): boolean => {
        // If both are undefined or null, they're equal
        if (!newValue && !oldValue) {
            return true;
        }

        // If only one is undefined/null, they're different
        if (!newValue || !oldValue) {
            return false;
        }

        // Use isEqual for deep comparison
        return isEqual(newValue, oldValue);
    };

    /**
     * Implementation of filter change logic.
     */
    #handleFilterChangeImplementation = (
        fetchParams: FetchDataResponseParams | null,
    ): void => {
        if (!fetchParams?.globalFilter.filters) {
            return;
        }

        const newFilterValues = this.processFilterValues(
            fetchParams.globalFilter.filters,
        );

        // Check if any filter changed using our safe comparison
        const filtersChanged =
            Object.keys(newFilterValues).length !==
                Object.keys(this.#filterValues).length ||
            !isEqual(newFilterValues, this.#filterValues);

        // Check specific filters using safe comparison - with null checks
        const warningsFilterChanged =
            'warnings' in newFilterValues && 'warnings' in this.#filterValues
                ? !this.safeCompareFilters(
                      newFilterValues.warnings.value,
                      this.#filterValues.warnings.value,
                  )
                : 'warnings' in newFilterValues;

        const connectionsFilterChanged =
            'connections' in newFilterValues &&
            'connections' in this.#filterValues
                ? !this.safeCompareFilters(
                      newFilterValues.connections.value,
                      this.#filterValues.connections.value,
                  )
                : 'connections' in newFilterValues;

        const groupIdsFilterChanged =
            'groupIds' in newFilterValues && 'groupIds' in this.#filterValues
                ? !this.safeCompareFilters(
                      newFilterValues.groupIds.value,
                      this.#filterValues.groupIds.value,
                  )
                : 'groupIds' in newFilterValues;

        const personnelFilterChanged =
            'employmentStatus' in newFilterValues &&
            'employmentStatus' in this.#filterValues
                ? !this.safeCompareFilters(
                      newFilterValues.employmentStatus.value,
                      this.#filterValues.employmentStatus.value,
                  )
                : 'employmentStatus' in newFilterValues;

        if (
            filtersChanged ||
            warningsFilterChanged ||
            connectionsFilterChanged ||
            groupIdsFilterChanged ||
            personnelFilterChanged
        ) {
            this.setFilterValues(newFilterValues);
            this.updateFilterValues(fetchParams.globalFilter.filters);
        }
    };

    /**
     * Clean filter parameters by removing filters that don't have matching configurations.
     */
    cleanFilterParams = (
        fetchParams: FetchDataResponseParams,
    ): FetchDataResponseParams => {
        const enabledFilterIds = new Set(
            this.tableFilters.filters.map((filter) => filter.id),
        );

        const cleanedFilters = Object.fromEntries(
            Object.entries(fetchParams.globalFilter.filters).filter(([key]) =>
                enabledFilterIds.has(key),
            ),
        );

        return {
            ...fetchParams,
            globalFilter: {
                ...fetchParams.globalFilter,
                filters: cleanedFilters,
            },
        };
    };

    /**
     * Handle filter changes with automatic cleanup.
     */
    handleFilterChange = function (
        this: AccessReviewFiltersModel,
        fetchParams: FetchDataResponseParams | null,
    ): FetchDataResponseParams | null {
        if (!fetchParams) {
            return null;
        }

        // Clean the parameters first
        const cleanedParams = this.cleanFilterParams(fetchParams);

        // Process the cleaned parameters
        this.#handleFilterChangeImplementation(cleanedParams);

        // Return cleaned parameters for the caller to use
        return cleanedParams;
    };
}

export const createAccessReviewFiltersModel = (
    props: Omit<
        ConstructorParameters<typeof AccessReviewFiltersModel>[0],
        'filterValues' | 'setFilterValues'
    >,
): AccessReviewFiltersModel => {
    return new AccessReviewFiltersModel(props);
};
