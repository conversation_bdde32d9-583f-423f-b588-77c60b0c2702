import { isNil, isString, noop } from 'lodash-es';
import type { FilterProps } from '@cosmos/components/datatable';
import type { RadioFieldGroupProps } from '@cosmos/components/radio-field-group';
import type {
    AccessApplicationWarningsResponseDto,
    AccessReviewApplicationResponseDto,
    ApplicationGroupResponseDto,
    ClientTypeEnum,
    ConnectionResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { PROVIDER_FILTERS } from '../constants/provider-filters.constants';
import { getUserRolesLabels } from '../constants/user-roles.constants';
import type { ConnectionLoadParams, GroupLoadParams } from './types';

export interface WarningFilters {
    personnel: boolean;
    warnings: boolean;
    permissions: boolean;
    warningFilters: {
        formerPersonnelWithAccess: boolean;
        missingMFA: boolean;
        unlinkUsers: boolean;
        accessLevelChange: boolean;
        serviceAccount: boolean;
    };
    connection: boolean;
    employeeStatus: boolean;
    group: boolean;
}

interface Connection {
    id: number | string;
    clientAlias?: string;
    clientId?: string;
    accountId?: string;
}

interface WarningType {
    key: keyof WarningFilters['warningFilters'];
    count: number;
    label: string;
    id: string;
    value: string;
}

/**
 * Returns the appropriate filter configuration based on provider type.
 */
export const filtersByProvider = (
    clientType: ClientTypeEnum | undefined | null,
    isManuallyAddedApplication: boolean,
    source: string | undefined,
): WarningFilters => {
    if (isManuallyAddedApplication) {
        return PROVIDER_FILTERS.MANUAL_APPLICATION;
    }
    if (clientType === 'MICROSOFT_365') {
        return source === 'DIRECT_CONNECTION'
            ? PROVIDER_FILTERS.MICROSOFT_365
            : PROVIDER_FILTERS.MICROSOFT_365_PARTNER;
    }

    if (clientType === 'OKTA_IDENTITY') {
        return source === 'DIRECT_CONNECTION'
            ? PROVIDER_FILTERS.OKTA_IDENTITY
            : PROVIDER_FILTERS.OKTA_IDENTITY_PARTNER;
    }
    if (isNil(clientType)) {
        return PROVIDER_FILTERS.DEFAULT;
    }

    // For all other client types, use the existing logic
    if (
        !isNil(clientType) &&
        isString(clientType) &&
        clientType in PROVIDER_FILTERS
    ) {
        return PROVIDER_FILTERS[clientType as keyof typeof PROVIDER_FILTERS];
    }

    if (clientType.startsWith('STACKONE')) {
        return PROVIDER_FILTERS.STACKONE;
    }

    if (clientType.startsWith('MERGEDEV')) {
        return PROVIDER_FILTERS.MERGEDEV;
    }

    return PROVIDER_FILTERS.DEFAULT;
};

export const getPermissionsFilter = (): FilterProps['filters'][number] => ({
    filterType: 'radio',
    id: 'permission',
    label: t`Permissions`,
    value: 'ALL_PERMISSIONS',
    options: [
        {
            label: t`All permissions`,
            value: 'ALL_PERMISSIONS',
        },
        {
            label: t`Admin`,
            value: 'ADMIN_PERMISSION',
        },
    ],
});

// Define allowed warnings for manually added applications
const MANUAL_APPS_ALLOWED_WARNINGS = [
    'FORMER_PERSONNEL_WITH_ACCESS',
    'UNLINKED_USERS',
    'SERVICE_ACCOUNTS',
];

/**
 * Returns the warnings filter configuration based on enabled warning types.
 */
export const getWarningFilters = (
    warningFilters: WarningFilters,
    warnings?: AccessApplicationWarningsResponseDto,
    isManuallyAdded = false,
): FilterProps['filters'][number] => {
    const {
        formerPersonnel = 0,
        missingMfa = 0,
        unlinkedIdentities = 0,
        levelChange = 0,
        serviceAccounts = 0,
    } = warnings ?? {};

    const warningTypes: WarningType[] = [
        {
            key: 'formerPersonnelWithAccess',
            count: formerPersonnel,
            label: t`Former personnel with access`,
            id: 'FORMER_PERSONNEL_WITH_ACCESS',
            value: 'FORMER_PERSONNEL_WITH_ACCESS',
        },
        {
            key: 'missingMFA',
            count: missingMfa,
            label: t`Missing MFA`,
            id: 'MISSING_MFA',
            value: 'MISSING_MFA',
        },
        {
            key: 'unlinkUsers',
            count: unlinkedIdentities,
            label: t`Unlinked users`,
            id: 'UNLINKED_USERS',
            value: 'UNLINKED_USERS',
        },
        {
            key: 'accessLevelChange',
            count: levelChange,
            label: t`Job title change`,
            id: 'ACCESS_LEVEL_CHANGE',
            value: 'ACCESS_LEVEL_CHANGE',
        },
        {
            key: 'serviceAccount',
            count: serviceAccounts,
            label: t`Service accounts`,
            id: 'SERVICE_ACCOUNTS',
            value: 'SERVICE_ACCOUNTS',
        },
    ];

    // Start with the "All warnings" option
    const options: RadioFieldGroupProps['options'] = [
        {
            label: t`Any or no warnings`,
            value: '',
        },
    ];

    // Add enabled warning types to options
    warningTypes.forEach(({ key, count, label, value }) => {
        // Skip if the warning type is not enabled in warningFilters
        if (!warningFilters.warningFilters[key]) {
            return;
        }

        // Check if warning is allowed for manual applications
        const isManualAppWarningAllowed =
            MANUAL_APPS_ALLOWED_WARNINGS.includes(value);

        // Skip if it's a manually added application and this warning type is not allowed
        if (isManuallyAdded && !isManualAppWarningAllowed) {
            return;
        }

        const countLabel = count > 0 ? `(${count})` : '';

        options.push({
            label: `${label} ${countLabel}`.trim(),
            value,
        });
    });

    return {
        filterType: 'radio',
        id: 'warnings',
        label: t`Warnings`,
        value: '',
        options,
    };
};

/**
 * Returns the account filters configuration.
 */
export const getAccountFilters = (
    connections: Connection[],
    loadConnections: (params: ConnectionLoadParams) => void,
): FilterProps['filters'][number] => ({
    filterType: 'combobox',
    id: 'connections',
    label: t`Account ID / alias`,
    isMultiSelect: true,
    placeholder: t`Search by account`,
    removeAllSelectedItemsLabel: t`Clear all`,
    options: connections.map((connection) => ({
        id: String(connection.id),
        label:
            connection.clientAlias ||
            connection.clientId ||
            connection.accountId ||
            t`Unknown`,
        value: String(connection.id),
    })),
    onFetchOptions: loadConnections,
});

/**
 * Personnel filter configuration with direct update handler.
 */
export const getPersonnelFilter = (
    onDirectUpdate: (value: unknown) => void,
): FilterProps['filters'][number] => ({
    filterType: 'combobox',
    id: 'employmentStatus',
    label: t`Personnel status`,
    isMultiSelect: true,
    placeholder: t`Search by status`,
    removeAllSelectedItemsLabel: t`Clear all`,
    options: Object.entries(getUserRolesLabels()).map(([value, label]) => ({
        id: value,
        label,
        value,
    })),
    defaultSelectedOptions: [
        {
            id: 'ALL_CURRENT_PERSONNEL',
            label: t`All current personnel`,
            value: 'ALL_CURRENT_PERSONNEL',
        },
    ],
    onFetchOptions: ({ search }) => {
        if (search) {
            // Your existing logic here
        }
        onDirectUpdate({ search });
    },
});

/**
 * Returns the group filters configuration with infinite scroll support.
 */
export const getGroupFilters = (
    groups: ApplicationGroupResponseDto[],
    loadGroups: (params: GroupLoadParams) => void,
    hasMoreGroups?: boolean,
    isLoadingGroups?: boolean,
): FilterProps['filters'][number] => ({
    filterType: 'combobox',
    id: 'groupIds',
    label: t`Group`,
    isMultiSelect: true,
    placeholder: t`Search by group`,
    removeAllSelectedItemsLabel: t`Clear all`,
    options: groups.map((group) => ({
        id: String(group.id),
        label: group.name,
        value: String(group.id),
    })),
    getRemoveIndividualSelectedItemClickLabel: ({ itemLabel }) =>
        t`Remove ${itemLabel}`,
    hasMore: hasMoreGroups,
    isLoading: isLoadingGroups,
    onFetchOptions: ({ search, increasePage }) => {
        if (increasePage) {
            loadGroups({ search, increasePage });
        } else if (search) {
            loadGroups({ search });
        } else {
            // Default case: load initial data when both parameters are falsy
            loadGroups({});
        }
    },
});

/**
 * Returns the complete filter configuration based on enabled filter types.
 */
export const getFilters = (
    groups: ApplicationGroupResponseDto[],
    loadGroups: (params: {
        search?: string;
        applicationId?: number;
        increasePage?: boolean;
    }) => void,
    connections: ConnectionResponseDto[],
    loadConnections: (params: { search?: string }) => void,
    warningFiltersObject: WarningFilters,
    warnings: AccessReviewApplicationResponseDto['warnings'],
    isManuallyAdded: boolean,
    onDirectPersonnelFilterUpdate?: (value: unknown) => void,
    hasMoreGroups?: boolean,
    isLoadingGroups?: boolean,
): FilterProps => {
    const filterArray: FilterProps['filters'] = [];

    // Add personnel filter with direct update handler if provided
    if (warningFiltersObject.personnel) {
        filterArray.push(
            onDirectPersonnelFilterUpdate
                ? getPersonnelFilter(onDirectPersonnelFilterUpdate)
                : getPersonnelFilter(noop),
        );
    }

    if (warningFiltersObject.warnings) {
        filterArray.push(
            getWarningFilters(
                warningFiltersObject,
                warnings ?? undefined,
                isManuallyAdded,
            ),
        );
    }

    if (warningFiltersObject.permissions) {
        filterArray.push(getPermissionsFilter());
    }

    if (warningFiltersObject.connection) {
        filterArray.push(getAccountFilters(connections, loadConnections));
    }

    if (warningFiltersObject.group) {
        filterArray.push(
            getGroupFilters(groups, loadGroups, hasMoreGroups, isLoadingGroups),
        );
    }

    return {
        clearAllButtonLabel: 'Reset',
        triggerLabel: 'Filters',
        filters: filterArray,
    };
};
