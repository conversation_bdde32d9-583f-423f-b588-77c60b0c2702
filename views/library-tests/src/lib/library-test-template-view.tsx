import { uniqueId } from 'lodash-es';
import { libraryTestTemplatesController } from '@controllers/library-tests';
import { Datatable } from '@cosmos/components/datatable';
import type { LibraryTestTemplateResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { getLibraryTestTemplateBulkActionDropdownItems } from '../constants/library-test-template-bulk-action.constants';
import { LIBRARY_TEST_TEMPLATE_COLUMNS } from '../constants/library-test-template-data-table.constants';

export const LibraryTestTemplateView = observer((): React.JSX.Element => {
    const {
        getTestTemplates,
        isLoading,
        getTotal,
        loadTestTemplates,
        selectedTemplates,
        handleRowSelection,
        isAllRowsSelected,
        filterProps,
    } = libraryTestTemplatesController;

    const { currentWorkspace } = sharedWorkspacesController;
    const navigate = useNavigate();

    const handleRowClick = ({
        row,
    }: {
        row: LibraryTestTemplateResponseDto;
    }) => {
        navigate(
            `/workspaces/${currentWorkspace?.id}/library/tests/${row.testId}/overview`,
        );
    };

    return (
        <Datatable<LibraryTestTemplateResponseDto>
            isRowSelectionEnabled
            isLoading={isLoading}
            tableId="library-test-datatable"
            data-id="library-test-datatable"
            data-testid="LibraryTestDataTable"
            columns={LIBRARY_TEST_TEMPLATE_COLUMNS}
            total={getTotal}
            filterProps={filterProps}
            data={getTestTemplates}
            /**
             * A big whitespace is rendered during loading state. So we use a random id as fallback.
             */
            getRowId={(row) =>
                row.templateId ? String(row.templateId) : uniqueId()
            }
            bulkActionDropdownItems={getLibraryTestTemplateBulkActionDropdownItems(
                selectedTemplates,
                isAllRowsSelected,
                getTotal,
            )}
            tableSearchProps={{
                hideSearch: false,
                placeholder: t`Search by name or description`,
                debounceDelay: 500,
            }}
            filterViewModeProps={{
                props: {
                    selectedOption: 'pinned',
                    initialSelectedOption: 'pinned',
                    togglePinnedLabel: t`Pin filters to page`,
                    toggleUnpinnedLabel: t`Move filters to dropdown`,
                },
                viewMode: 'toggleable',
            }}
            emptyStateProps={{
                illustrationName: `Warning`,
                title: t`Tests Library`,
                description: t`No test templates were found`,
            }}
            onFetchData={loadTestTemplates}
            onRowSelection={handleRowSelection}
            onRowClick={handleRowClick}
        />
    );
});
