import { useCallback, useState } from 'react';
import { <PERSON>mboboxField } from '@cosmos/components/combobox-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Modal } from '@cosmos/components/modal';
import { plural, t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export interface AddWorkspacesModalProps {
    onClose: () => void;
    onConfirm: (selectedWorkspaces: Workspace[]) => void;
    availableWorkspaces: Workspace[];
    initialSelectedWorkspaces?: Workspace[];
}

export interface Workspace
    extends Required<Pick<ListBoxItemData, 'id' | 'label' | 'value'>> {}

export const AddWorkspacesModal = observer(
    ({
        onClose,
        onConfirm,
        availableWorkspaces,
        initialSelectedWorkspaces = [],
    }: AddWorkspacesModalProps): React.JSX.Element => {
        const [selectedWorkspaces, setSelectedWorkspaces] = useState<
            Workspace[]
        >(initialSelectedWorkspaces);
        const [filteredOptions, setFilteredOptions] = useState<
            ListBoxItemData[]
        >(availableWorkspaces as ListBoxItemData[]);

        const handleConfirm = () => {
            onConfirm(selectedWorkspaces);
            onClose();
        };

        const handleChange = (value: ListBoxItemData[]) => {
            setSelectedWorkspaces(value as Workspace[]);
        };

        const handleFetchOptions = useCallback(
            ({ search }: { search?: string }) => {
                if (search) {
                    const filtered = availableWorkspaces.filter((workspace) =>
                        workspace.label
                            .toLowerCase()
                            .includes(search.toLowerCase()),
                    );

                    setFilteredOptions(filtered as ListBoxItemData[]);
                } else {
                    setFilteredOptions(
                        availableWorkspaces as ListBoxItemData[],
                    );
                }
            },
            [availableWorkspaces],
        );

        const workspacesSelected = plural(selectedWorkspaces.length, {
            one: '# workspace selected',
            other: '# workspaces selected',
        });

        return (
            <Modal
                data-testid="AddWorkspacesModal"
                data-id="add-workspaces-modal"
                size="lg"
                onClose={onClose}
            >
                <Modal.Header
                    title={t`Add workspaces`}
                    closeButtonAriaLabel={t`Close add workspaces modal`}
                    description={workspacesSelected}
                    onClose={onClose}
                />
                <Modal.Body>
                    <ComboboxField
                        isMultiSelect
                        formId="add-workspaces-form"
                        name="workspaces"
                        label={t`Search workspaces`}
                        loaderLabel={t`Loading workspaces...`}
                        removeAllSelectedItemsLabel={t`Clear all`}
                        data-testid="workspaces-combobox"
                        data-id="workspaces-combobox"
                        options={filteredOptions}
                        searchDebounce={300}
                        defaultSelectedOptions={
                            initialSelectedWorkspaces as ListBoxItemData[]
                        }
                        onFetchOptions={handleFetchOptions}
                        onChange={handleChange}
                    />
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Close`,
                            level: 'tertiary',
                            onClick: onClose,
                            'data-id': 'close-modal-button',
                        },
                        {
                            label: t`Confirm`,
                            level: 'primary',
                            onClick: handleConfirm,
                            'data-id': 'confirm-workspaces-button',
                        },
                    ]}
                />
            </Modal>
        );
    },
);
