import { useCallback, useMemo } from 'react';
import { sharedAddTestToProgramController } from '@controllers/add-test-to-program';
import { activeLibraryTestController } from '@controllers/library-test';
import { But<PERSON> } from '@cosmos/components/button';
import { EmptyState } from '@cosmos/components/empty-state';
import { Grid } from '@cosmos/components/grid';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import {
    Wizard,
    type WizardStepDataProps,
} from '@cosmos-lab/components/wizard';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { observer } from '@globals/mobx';
import { useLocation, useNavigate } from '@remix-run/react';
import { useFormSubmit } from '@ui/forms';
import { ApplyTypeStep } from './components/apply-type-step';
import { ChooseWorkspacesStep } from './components/choose-workspaces-step';
import { ModifyDetailsStep } from './components/modify-details-step';

export const AddTestToProgramView = observer((): React.JSX.Element => {
    const navigate = useNavigate();
    const location = useLocation();
    const { formRef, triggerSubmit } = useFormSubmit();

    const { isMultipleWorkspacesEnabled, isLoading } =
        sharedEntitlementFlagController;
    const { monitoringControlInstance, templateHasErrors } =
        activeLibraryTestController;

    const handleCancel = useCallback(() => {
        // Navigate back to test details page
        const testDetailsPath = location.pathname
            .split('/')
            .slice(0, -1)
            .join('/');

        navigate(testDetailsPath);
    }, [location.pathname, navigate]);

    const handleComplete = useCallback(() => {
        if (!monitoringControlInstance?.id) {
            logger.error('Invalid monitoringControlInstance.id');

            return;
        }

        return sharedAddTestToProgramController
            .importTest(monitoringControlInstance.id)
            .then(() => {
                // Navigate back to test details page after completion
                const testDetailsPath = location.pathname
                    .split('/')
                    .slice(0, -1)
                    .join('/');

                navigate(testDetailsPath);
            });
    }, [location.pathname, navigate, monitoringControlInstance]);

    const ModifyDetailsComponentWithRef = useCallback(
        () => <ModifyDetailsStep formRef={formRef} data-id="TKsRpIg3" />,
        [formRef],
    );

    const steps = useMemo(() => {
        const stepsArray: WizardStepDataProps[] = [];

        if (isMultipleWorkspacesEnabled) {
            stepsArray.push({
                component: ChooseWorkspacesStep,
                stepTitle: t`Choose workspaces`,
                isStepSkippable: false,
                backButtonLabelOverride: t`Cancel`,
                forwardButtonLabelOverride: t`Continue`,
                onStepChange:
                    sharedAddTestToProgramController.validateWorkspacesStep,
            });
        }

        stepsArray.push(
            {
                component: ModifyDetailsComponentWithRef,
                stepTitle: t`Modify details`,
                isStepSkippable: false,
                forwardButtonLabelOverride: t`Continue`,
                backButtonLabelOverride: isMultipleWorkspacesEnabled
                    ? t`Back`
                    : t`Cancel`,
                onStepChange: triggerSubmit,
            },
            {
                component: ApplyTypeStep,
                stepTitle: t`Apply type`,
                isStepSkippable: false,
                backButtonLabelOverride: t`Back`,
                forwardButtonLabelOverride: t`Add to program`,
            },
        );

        return stepsArray;
    }, [
        isMultipleWorkspacesEnabled,
        ModifyDetailsComponentWithRef,
        triggerSubmit,
    ]);

    if (templateHasErrors) {
        return (
            <EmptyState
                title={t`You can't start setup because criteria are not met`}
                description={t`Please review the requirements in the test template before adding it to a program.`}
                illustrationName="Warning"
                data-id="template-error-empty-state"
                rightAction={
                    <Button
                        label={t`Go to test details`}
                        level="secondary"
                        onClick={handleCancel}
                    />
                }
            />
        );
    }

    if (isLoading) {
        return (
            <Stack
                direction="column"
                align="center"
                gap="6x"
                p="8x"
                data-testid="AddTestToProgramLoader"
                data-id="5DYP_Z0x"
            >
                <Loader isSpinnerOnly size="lg" label={t`Loading...`} />
            </Stack>
        );
    }

    return (
        <Grid
            columns={{ initial: '1', md: '3' }}
            data-id="add-test-program-grid"
        >
            <Grid gridColumn={{ initial: 'span 1', md: 'span 2' }}>
                <Wizard
                    data-testid="AddTestToProgramWizard"
                    data-id="Tz8mRvX3"
                    steps={steps}
                    onCancel={handleCancel}
                    onComplete={handleComplete}
                />
            </Grid>
        </Grid>
    );
});
