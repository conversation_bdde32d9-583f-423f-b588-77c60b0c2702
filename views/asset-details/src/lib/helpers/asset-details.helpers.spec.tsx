import { noop } from 'lodash-es';
import { describe, expect, test, vi } from 'vitest';
import {
    DEVICE_COMPLIANCE_COMPUTED_STATUS,
    type DeviceCompliance,
} from '@controllers/asset';
import {
    buildDeleteAssetAction,
    buildDeviceComplianceKVP,
    buildOwnerKVP,
    getDeviceComplianceStatusKVPValue,
} from './asset-details.helpers';

describe('asset-details helpers', () => {
    describe('getDeviceComplianceStatusKVPValue', () => {
        test('returns correct KVP value for each status', () => {
            const statuses = Object.values(DEVICE_COMPLIANCE_COMPUTED_STATUS);

            statuses.forEach((status) => {
                const result = getDeviceComplianceStatusKVPValue(status);

                expect(result).toBeDefined();
                expect(result).toStrictEqual({
                    label: expect.any(String),
                    colorScheme: expect.any(String),
                    iconName: expect.any(String),
                });
            });
        });
    });

    describe('buildDeviceComplianceKVP', () => {
        test('returns empty array when deviceCompliance is null', () => {
            const result = buildDeviceComplianceKVP(null);

            expect(result).toStrictEqual([]);
        });

        test('builds correct KVP array with badge type', () => {
            const deviceCompliance: DeviceCompliance = {
                status: DEVICE_COMPLIANCE_COMPUTED_STATUS.COMPLIANT,
            };
            const result = buildDeviceComplianceKVP(deviceCompliance);

            expect(result).toHaveLength(1);
            expect(result[0]).toStrictEqual({
                id: 'assets-details-device-compliance',
                type: 'BADGE',
                label: 'Device compliance',
                value: expect.any(Object),
            });
        });
    });

    describe('buildOwnerKVP', () => {
        test('builds owner KVP with complete data', () => {
            const owner = {
                firstName: 'John',
                lastName: 'Doe',
                avatarUrl: 'https://example.com/avatar.jpg',
            };

            const result = buildOwnerKVP(owner);

            expect(result).toStrictEqual([
                {
                    id: 'assets-details-owner',
                    type: 'USER',
                    label: 'Owner',
                    value: {
                        username: 'John Doe',
                        avatarProps: {
                            fallbackText: 'JD',
                            imgSrc: owner.avatarUrl,
                            imgAlt: 'John Doe',
                        },
                    },
                },
            ]);
        });

        test('builds owner KVP with missing data', () => {
            const result = buildOwnerKVP(undefined);

            expect(result).toStrictEqual([
                {
                    id: 'assets-details-owner',
                    type: 'USER',
                    label: 'Owner',
                    value: {
                        username: '',
                        avatarProps: {
                            fallbackText: '',
                            imgSrc: '',
                            imgAlt: '',
                        },
                    },
                },
            ]);
        });
    });

    describe('buildDeleteAssetAction', () => {
        test('returns delete action when enabled', () => {
            const mockOnDelete = vi.fn();
            const result = buildDeleteAssetAction(true, mockOnDelete);

            expect(result).toHaveLength(1);
            expect(result[0]).toStrictEqual({
                id: 'assets-details-action-stack-delete',
                actionType: 'button',
                typeProps: {
                    label: 'Delete asset',
                    colorScheme: 'danger',
                    level: 'tertiary',
                    startIconName: 'Trash',
                    onClick: mockOnDelete,
                },
            });
        });

        test('returns delete action when enabled without onClick', () => {
            const result = buildDeleteAssetAction(true);

            expect(result).toHaveLength(1);
            expect(result[0]).toStrictEqual({
                id: 'assets-details-action-stack-delete',
                actionType: 'button',
                typeProps: {
                    label: 'Delete asset',
                    colorScheme: 'danger',
                    level: 'tertiary',
                    startIconName: 'Trash',
                    onClick: noop,
                },
            });
        });

        test('returns empty array when disabled', () => {
            const result = buildDeleteAssetAction(false);

            expect(result).toStrictEqual([]);
        });
    });
});
