import { isNil, noop } from 'lodash-es';
import type {
    DeviceCompliance,
    DeviceComplianceComputedStatus,
} from '@controllers/asset';
import type { Action } from '@cosmos/components/action-stack';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import type { AssetResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { getFullName, getInitials } from '@helpers/formatters';

export const getDeviceComplianceStatusKVPValue = (
    status: DeviceComplianceComputedStatus,
): KeyValuePairProps['value'] => {
    switch (status) {
        case 'COMPLIANT': {
            return {
                label: t`Compliant`,
                colorScheme: 'success',
                iconName: 'CheckCircle',
            };
        }
        case 'EXCLUDED': {
            return {
                label: t`Excluded`,
                colorScheme: 'neutral',
                iconName: 'OutOfScope',
            };
        }
        case 'UNABLE_TO_GET_DATA': {
            return {
                label: t`Unable to get data`,
                colorScheme: 'warning',
                iconName: 'WarningTriangle',
            };
        }
        case 'NON_COMPLIANT': {
            return {
                label: t`Not compliant`,
                colorScheme: 'critical',
                iconName: 'NotReady',
            };
        }
        case 'OUT_OF_SCOPE': {
            return {
                label: t`Out of scope`,
                colorScheme: 'neutral',
                iconName: 'OutOfScope',
            };
        }
        default: {
            // this should never happen
            return {
                label: t`Unknown`,
                colorScheme: 'neutral',
                iconName: 'WarningTriangle',
            };
        }
    }
};

export const buildDeviceComplianceKVP = (
    deviceCompliance: DeviceCompliance,
): KeyValuePairProps[] => {
    return isNil(deviceCompliance)
        ? []
        : [
              {
                  id: 'assets-details-device-compliance',
                  type: 'BADGE',
                  label: 'Device compliance',
                  value: getDeviceComplianceStatusKVPValue(
                      deviceCompliance.status,
                  ),
              },
          ];
};

export const buildOwnerKVP = (
    assetOwner?: Pick<
        AssetResponseDto['owner'],
        'firstName' | 'lastName' | 'avatarUrl'
    >,
): KeyValuePairProps[] => {
    const ownerFullName = getFullName(
        assetOwner?.firstName,
        assetOwner?.lastName,
    );

    return [
        {
            id: 'assets-details-owner',
            type: 'USER',
            label: t`Owner`,
            value: {
                username: ownerFullName,
                avatarProps: {
                    fallbackText: getInitials(ownerFullName),
                    imgSrc: assetOwner?.avatarUrl ?? '',
                    imgAlt: ownerFullName,
                },
            },
        },
    ];
};

export const buildDeleteAssetAction = (
    isEnabled: boolean,
    onDelete?: () => void,
): Action[] => {
    return isEnabled
        ? [
              {
                  id: 'assets-details-action-stack-delete',
                  actionType: 'button',
                  typeProps: {
                      label: 'Delete asset',
                      colorScheme: 'danger',
                      level: 'tertiary',
                      startIconName: 'Trash',
                      onClick: onDelete ?? noop,
                  },
              },
          ]
        : [];
};
