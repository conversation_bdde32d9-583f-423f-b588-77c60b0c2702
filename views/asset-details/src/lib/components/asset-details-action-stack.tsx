import {
    activeAssetController,
    sharedAssetMutationController,
} from '@controllers/asset';
import { ActionStack } from '@cosmos/components/action-stack';
import { dimensionSm } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { sharedAssetsDetailsModel } from '@models/assets';
import { useNavigate } from '@remix-run/react';
import { buildDeleteAssetAction } from '../helpers/asset-details.helpers';

export const AssetDetailsActionStack = observer((): React.JSX.Element => {
    const navigate = useNavigate();

    const handleDeleteAsset = action(() => {
        const assetId = activeAssetController.assetDetails?.id;

        if (!assetId) {
            return;
        }

        const handleOnConfirm = action(() => {
            sharedAssetMutationController.deleteAsset(
                assetId,
                () => {
                    navigate('risk/assets');
                },
                closeConfirmationModal,
            );
        });

        openConfirmationModal({
            title: t`Are you sure?`,
            body: t`This will permanently delete the asset. This action cannot be undone.`,
            confirmText: t`Delete`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: handleOnConfirm,
            onCancel: closeConfirmationModal,
        });
    });

    return (
        <ActionStack
            isFullWidth
            data-id="asset-details-page-header-action-stack"
            gap={dimensionSm}
            stacks={[
                {
                    id: 'asset-details-page-header-action-stack-actions',
                    actions: [
                        ...buildDeleteAssetAction(
                            activeAssetController.isDrataProvider,
                            handleDeleteAsset,
                        ),
                        ...sharedAssetsDetailsModel.unlinkDeviceActions,
                        ...sharedAssetsDetailsModel.linkDeviceActions,
                    ],
                },
            ]}
        />
    );
});
