import { useCallback } from 'react';
import type { NavigateFunction } from '@remix-run/react';
import type { WizardState } from '../access-review-create-period-wizard-context';
import { sharedAccessReviewCreatePeriodWizardController } from '../access-review-create-period-wizard-controller';

interface UseWizardNavigationProps {
    navigate: NavigateFunction;
    workspaceId: string | undefined;
    isEditMode: boolean;
    periodId: number | null;
}

interface UseWizardNavigationReturn {
    handleCancel: () => void;
    handleComplete: (wizardState: WizardState) => Promise<void>;
}

/**
 * Hook that handles wizard navigation logic including cancel and completion workflows.
 */
export const useWizardNavigation = ({
    navigate,
    workspaceId,
    isEditMode,
    periodId,
}: UseWizardNavigationProps): UseWizardNavigationReturn => {
    const handleCancel = useCallback(() => {
        if (!workspaceId) {
            return;
        }
        navigate(
            `/workspaces/${workspaceId}/governance/access-review/applications`,
        );
    }, [navigate, workspaceId]);

    const handleComplete = useCallback(
        (wizardState: WizardState) => {
            if (!workspaceId) {
                return Promise.reject(new Error('Workspace ID is required'));
            }

            if (isEditMode && periodId) {
                return sharedAccessReviewCreatePeriodWizardController.updatePeriodWorkflow(
                    Number(periodId),
                    wizardState,
                    navigate,
                    workspaceId,
                );
            }

            return sharedAccessReviewCreatePeriodWizardController.createPeriodWorkflow(
                wizardState,
                navigate,
                workspaceId,
            );
        },
        [navigate, workspaceId, isEditMode, periodId],
    );

    return {
        handleCancel,
        handleComplete,
    };
};
