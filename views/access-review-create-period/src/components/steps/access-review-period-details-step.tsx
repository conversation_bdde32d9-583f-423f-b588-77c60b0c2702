import { useEffect, useRef } from 'react';
import { z } from 'zod';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { dimension170x } from '@cosmos/constants/tokens';
import { t, Trans } from '@globals/i18n/macro';
import { addDaysToDate } from '@helpers/date-time';
import { Form, type FormValues } from '@ui/forms';
import { useWizardContext } from '../../lib/use-wizard-context';

const FORM_ID = 'access-review-period-details-form';

interface FormRefHandle extends HTMLFormElement {
    submitForm: () => Promise<boolean>;
}

export const AccessReviewPeriodDetailsStep = (): React.JSX.Element => {
    const { state, updatePeriodDetails, setDetailsStepValidation } =
        useWizardContext();
    const formRef = useRef<FormRefHandle>(null);
    const startDate = new Date().toISOString().split('T')[0] as TDateISODate;
    const endDate = addDaysToDate(startDate, 3)
        .toISOString()
        .split('T')[0] as TDateISODate;

    // Register validation function with wizard context
    useEffect(() => {
        const validateStep = async (): Promise<boolean> => {
            if (formRef.current?.submitForm) {
                return formRef.current.submitForm();
            }

            return false;
        };

        setDetailsStepValidation(validateStep);
    }, [setDetailsStepValidation]);

    /**
     * Handle form submission and update wizard context.
     */
    const handleFormSubmit = (values: FormValues) => {
        const dateRange = values.dateRange as {
            start: TDateISODate | null;
            end: TDateISODate | null;
        };

        updatePeriodDetails(dateRange.start, dateRange.end);
    };

    return (
        <Stack
            gap="xl"
            direction="column"
            data-testid="AccessReviewPeriodDetailsStep"
            data-id="access-review-period-details-step"
            style={{ maxWidth: dimension170x }}
        >
            <Stack gap="lg" direction="column">
                <Text type="title">
                    <Trans>Define your review period</Trans>
                </Text>
                <Text>
                    <Trans>
                        Select the start and end dates for your company to
                        perform the next access review. Reviewers receive an
                        email when you add or remove them from a review.
                    </Trans>
                </Text>
            </Stack>

            <Form
                hasExternalSubmitButton
                key={`${state.startDate}-${state.endDate}`}
                formId={FORM_ID}
                data-id={FORM_ID}
                ref={formRef}
                schema={{
                    dateRange: {
                        type: 'dateRange',
                        label: t`Review period`,
                        initialValue: {
                            start: startDate,
                            end: endDate,
                        },
                        locale: 'en-US',
                        validator: z.object({
                            start: z
                                .string({
                                    message: t`Start date is required`,
                                })
                                .date(t`Please select a valid start date`),
                            end: z
                                .string({
                                    message: t`End date is required`,
                                })
                                .date(t`Please select a valid end date`),
                        }),
                    },
                }}
                onSubmit={handleFormSubmit}
            />
        </Stack>
    );
};
