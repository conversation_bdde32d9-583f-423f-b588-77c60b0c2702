import { useCallback } from 'react';
import { z } from 'zod';
import { sharedUsersInfiniteController } from '@controllers/users';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Form, type FormValues, useFormSubmit } from '@ui/forms';
import { URL_REGEX } from '../../constants';
import { createMaxLengthValidator } from '../../helpers/length-validator';

interface EditApplicationModalProps {
    application: {
        id: number;
        name: string;
        type: string;
        source: string;
        reviewers: {
            id: number;
            name: string;
            email: string;
            avatar: string;
        }[];
        websiteUrl?: string;
    };
    onConfirm: (application: {
        reviewers: {
            id: number;
            name: string;
            email: string;
            avatar: string;
        }[];
    }) => void;
    onCancel: () => void;
}

const FORM_ID = 'edit-application-form';

export const EditApplicationModal = observer(
    ({
        application,
        onConfirm,
        onCancel,
    }: EditApplicationModalProps): React.JSX.Element => {
        const {
            hasNextPage: hasMoreUsers,
            isFetching: isFetchingUsers,
            isLoading: isLoadingUsers,
            onFetchUsers,
            options,
        } = sharedUsersInfiniteController;

        const { formRef, triggerSubmit } = useFormSubmit();

        // Only show website URL field for manually added applications
        const showWebsiteUrl = application.source === 'MANUALLY_ADDED';

        const onSubmit = useCallback(
            (values: FormValues) => {
                const updatedApplication = {
                    ...application,
                    name: values.applicationName as string,
                    websiteUrl: showWebsiteUrl
                        ? (values.websiteUrl as string)
                        : application.websiteUrl,
                    reviewers: (
                        values.reviewersId as {
                            value: string;
                            label: string;
                        }[]
                    ).map((reviewer) => ({
                        id: parseInt(reviewer.value),
                        name: reviewer.label,
                        email: '', // Email will be populated from the user data
                        avatar: '',
                    })),
                };

                onConfirm(updatedApplication);
            },
            [application, onConfirm, showWebsiteUrl],
        );

        const applicationName = application.name;

        return (
            <>
                <Modal.Header
                    title={t`Edit ${applicationName} details`}
                    description=""
                    closeButtonAriaLabel={t`Close`}
                    size="md"
                    data-id="edit-application-modal-header"
                    onClose={onCancel}
                />
                <Modal.Body size="md">
                    <Form
                        hasExternalSubmitButton
                        formId={FORM_ID}
                        ref={formRef}
                        data-testid="EditApplicationModal"
                        data-id="edit-application-modal-form"
                        schema={{
                            reviewersId: {
                                type: 'combobox',
                                label: t`Reviewers`,
                                initialValue: application.reviewers.map(
                                    (reviewer) => ({
                                        id: reviewer.id.toString(),
                                        label: reviewer.name,
                                        value: reviewer.id.toString(),
                                    }),
                                ),
                                isMultiSelect: true,
                                isLoading: isFetchingUsers && isLoadingUsers,
                                removeAllSelectedItemsLabel: t`Clear all`,
                                getSearchEmptyState: () =>
                                    t`No employees found`,
                                loaderLabel: t`Loading`,
                                placeholder: t`Search by name or email`,
                                options,
                                hasMore: hasMoreUsers,
                                onFetchOptions: onFetchUsers,
                                validator: z
                                    .array(
                                        z.object({
                                            value: z.string(),
                                        }),
                                    )
                                    .min(1, {
                                        message: t`Reviewers field must have at least 1 item`,
                                    }),
                            },
                            applicationName: {
                                type: 'text',
                                label: t`Application name`,
                                initialValue: application.name,
                                validator: createMaxLengthValidator(
                                    t`Application name`,
                                    true,
                                ),
                            },
                            ...(showWebsiteUrl && {
                                websiteUrl: {
                                    type: 'text',
                                    label: t`Website URL`,
                                    initialValue: application.websiteUrl || '',
                                    validator: createMaxLengthValidator(
                                        t`Website URL`,
                                        false,
                                    ).refine(
                                        (value: string | undefined) =>
                                            !value || URL_REGEX.test(value),
                                        {
                                            message: t`Website URL must be a valid URL`,
                                        },
                                    ),
                                },
                            }),
                        }}
                        onSubmit={onSubmit}
                    />
                </Modal.Body>
                <Modal.Footer
                    size="md"
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'tertiary',
                            onClick: onCancel,
                        },
                        {
                            label: t`Save`,
                            level: 'primary',
                            onClick: () => {
                                triggerSubmit().catch(() => {
                                    console.error('Failed to submit form');
                                });
                            },
                        },
                    ]}
                />
            </>
        );
    },
);
