import { describe, expect, test } from 'vitest';
import { createMaxLengthValidator, MAX_FIELD_LENGTH } from './length-validator';

describe('length-validator', () => {
    describe('createMaxLengthValidator', () => {
        describe('when isRequired is false (optional field)', () => {
            test('should accept valid string within max length', () => {
                const validator = createMaxLengthValidator('Test Field');
                const validString = 'a'.repeat(MAX_FIELD_LENGTH);

                const result = validator.safeParse(validString);

                expect(result.success).toBeTruthy();
                expect(result.data).toBe(validString);
            });

            test('should accept empty string', () => {
                const validator = createMaxLengthValidator('Test Field');

                const result = validator.safeParse('');

                expect(result.success).toBeTruthy();
                expect(result.data).toBe('');
            });

            test('should accept undefined value', () => {
                const validator = createMaxLengthValidator('Test Field');

                const result = validator.safeParse(undefined);

                expect(result.success).toBeTruthy();
                expect(result.data).toBeUndefined();
            });

            test('should reject string exceeding max length', () => {
                const validator = createMaxLengthValidator('Test Field');
                const tooLongString = 'a'.repeat(MAX_FIELD_LENGTH + 1);

                const result = validator.safeParse(tooLongString);

                expect(result.success).toBeFalsy();
                expect(
                    (result as { error: { issues: { message: string }[] } })
                        .error.issues[0].message,
                ).toBe(
                    `Test Field must contain at most ${MAX_FIELD_LENGTH} characters`,
                );
            });

            test('should use custom field name in error message', () => {
                const validator = createMaxLengthValidator('Custom Field Name');
                const tooLongString = 'a'.repeat(MAX_FIELD_LENGTH + 1);

                const result = validator.safeParse(tooLongString);

                expect(result.success).toBeFalsy();
                expect(
                    (result as { error: { issues: { message: string }[] } })
                        .error.issues[0].message,
                ).toBe(
                    `Custom Field Name must contain at most ${MAX_FIELD_LENGTH} characters`,
                );
            });
        });

        describe('when isRequired is true (required field)', () => {
            test('should accept valid string within max length', () => {
                const validator = createMaxLengthValidator(
                    'Required Field',
                    true,
                );
                const validString = 'a'.repeat(MAX_FIELD_LENGTH);

                const result = validator.safeParse(validString);

                expect(result.success).toBeTruthy();
                expect(result.data).toBe(validString);
            });

            test('should accept single character string', () => {
                const validator = createMaxLengthValidator(
                    'Required Field',
                    true,
                );

                const result = validator.safeParse('a');

                expect(result.success).toBeTruthy();
                expect(result.data).toBe('a');
            });

            test('should reject empty string', () => {
                const validator = createMaxLengthValidator(
                    'Required Field',
                    true,
                );

                const result = validator.safeParse('');

                expect(result.success).toBeFalsy();
                expect(
                    (result as { error: { issues: { message: string }[] } })
                        .error.issues[0].message,
                ).toBe('Required Field is required');
            });

            test('should reject undefined value', () => {
                const validator = createMaxLengthValidator(
                    'Required Field',
                    true,
                );

                const result = validator.safeParse(undefined);

                expect(result.success).toBeFalsy();
                expect(
                    (result as { error: { issues: { message: string }[] } })
                        .error.issues[0].message,
                ).toBe('Required');
            });

            test('should reject string exceeding max length', () => {
                const validator = createMaxLengthValidator(
                    'Required Field',
                    true,
                );
                const tooLongString = 'a'.repeat(MAX_FIELD_LENGTH + 1);

                const result = validator.safeParse(tooLongString);

                expect(result.success).toBeFalsy();
                expect(
                    (result as { error: { issues: { message: string }[] } })
                        .error.issues[0].message,
                ).toBe(
                    `Required Field must contain at most ${MAX_FIELD_LENGTH} characters`,
                );
            });

            test('should use custom field name in required error message', () => {
                const validator = createMaxLengthValidator(
                    'Custom Required Field',
                    true,
                );

                const result = validator.safeParse('');

                expect(result.success).toBeFalsy();
                expect(
                    (result as { error: { issues: { message: string }[] } })
                        .error.issues[0].message,
                ).toBe('Custom Required Field is required');
            });
        });

        describe('edge cases', () => {
            test('should handle string exactly at max length', () => {
                const validator = createMaxLengthValidator('Edge Case Field');
                const exactLengthString = 'a'.repeat(MAX_FIELD_LENGTH);

                const result = validator.safeParse(exactLengthString);

                expect(result.success).toBeTruthy();
                expect(result.data).toBe(exactLengthString);
            });

            test('should handle string one character over max length', () => {
                const validator = createMaxLengthValidator('Edge Case Field');
                const overLengthString = 'a'.repeat(MAX_FIELD_LENGTH + 1);

                const result = validator.safeParse(overLengthString);

                expect(result.success).toBeFalsy();
            });

            test('should handle special characters and unicode', () => {
                const validator = createMaxLengthValidator('Unicode Field');
                const unicodeString = `${'🚀'.repeat(50)}test with emojis and accents`;

                const result = validator.safeParse(unicodeString);

                expect(result.success).toBeTruthy();
                expect(result.data).toBe(unicodeString);
            });

            test('should reject non-string values', () => {
                const validator = createMaxLengthValidator('Type Test Field');

                const numberResult = validator.safeParse(123);
                const objectResult = validator.safeParse({});
                const arrayResult = validator.safeParse([]);

                expect(numberResult.success).toBeFalsy();
                expect(objectResult.success).toBeFalsy();
                expect(arrayResult.success).toBeFalsy();
            });
        });
    });

    describe('max field length constant', () => {
        test('should be exported and have correct value', () => {
            expect(MAX_FIELD_LENGTH).toBe(191);
            expect(typeof MAX_FIELD_LENGTH).toBe('number');
        });
    });
});
