import { useMemo } from 'react';
import { sharedAwsGovCloudCreateConnectionController } from '@controllers/connections';
import { ComboboxField } from '@cosmos/components/combobox-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { action, observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

export const ConnectionsSetupAwsGovCloudWizardConfigureSettingsStep = observer(
    (): React.JSX.Element => {
        const workspaceOptions = useMemo(() => {
            return sharedWorkspacesController.workspaces.map((workspace) => ({
                label: workspace.name,
                value: workspace.id.toString(),
            }));
        }, []);

        const { setupController } = sharedAwsGovCloudCreateConnectionController;

        const handleChange = action((selectedItems: ListBoxItemData[]) => {
            const selectedIds = selectedItems.map((item) => {
                return Number(item.value);
            });

            setupController.setWorkspaces(selectedIds);
        });

        const selectedWorkspaces = useMemo(() => {
            return workspaceOptions
                .filter((workspace) =>
                    setupController.workspaces.includes(
                        Number(workspace.value),
                    ),
                )
                .map((workspace) => ({
                    ...workspace,
                    id: workspace.value,
                }));
        }, [workspaceOptions, setupController.workspaces]);

        return (
            <Stack
                direction="column"
                gap="12x"
                data-testid="ConnectionsSetupAwsGovCloudWizardConfigureSettingsStep"
                data-id="Zqb5utAZ"
            >
                <Text type="headline" size="400">
                    Assign workspaces for this connection
                </Text>
                <Stack direction="column" gap="6x">
                    <ComboboxField
                        isMultiSelect
                        formId="aws-gov-cloud-form"
                        label="Workspaces assigned"
                        loaderLabel="Loading workspaces..."
                        name="assignedWorkspaces"
                        test-id="aws-gov-cloud-workspaces-combobox"
                        removeAllSelectedItemsLabel="Clear all"
                        defaultSelectedOptions={selectedWorkspaces}
                        getSearchEmptyState={() => 'No workspaces found'}
                        options={workspaceOptions.map((workspace) => ({
                            ...workspace,
                            id: workspace.value,
                        }))}
                        feedback={
                            setupController.isSelectedWorkspacesValid
                                ? undefined
                                : {
                                      type: 'error',
                                      message: 'Select one or more workspaces',
                                  }
                        }
                        onChange={handleChange}
                        onBlur={() => {
                            setupController.hasAttemptedValidation = true;
                        }}
                    />
                </Stack>
            </Stack>
        );
    },
);
