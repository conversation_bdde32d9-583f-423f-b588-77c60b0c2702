import { Metadata } from '@cosmos/components/metadata';
import type { RiskControlResponseDto } from '@globals/api-sdk/types';
import { getControlStatusDisplay } from '@helpers/control-status';

export const ControlCodeCell = ({
    row: { original: control },
}: {
    row: { original: RiskControlResponseDto };
}): React.JSX.Element => {
    const { colorScheme, iconName } = getControlStatusDisplay({
        isReady: control.isReady,
        archivedAt: control.archivedAt ?? '',
    });

    return (
        <Metadata
            label={control.code}
            iconName={iconName}
            colorScheme={colorScheme}
            data-testid="ControlCodeCell"
            data-id="QZmLdv9S"
        />
    );
};
