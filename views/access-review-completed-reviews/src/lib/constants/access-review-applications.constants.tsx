import {
    AccessReviewApplicationCell,
    AccessReviewApplicationSourceCell,
    AccessReviewApplicationStatusCell,
    AccessReviewApplicationWarningsCell,
    CompletedReviewActionCell,
} from '@components/access-review';
import type { DatatableProps } from '@cosmos/components/datatable';
import type { AccessReviewPeriodApplicationResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const getAccessReviewApplicationCompletedColumns =
    (): DatatableProps<AccessReviewPeriodApplicationResponseDto>['columns'] => [
        {
            accessorKey: 'id',
            header: '',
            id: 'actions',
            enableSorting: false,
            size: 50,
            cell: CompletedReviewActionCell,
            meta: {
                shouldIgnoreRowClick: true,
            },
        },
        {
            accessorKey: 'name',
            header: t`Application`,
            id: 'Application',
            enableSorting: false,
            size: 200,
            cell: AccessReviewApplicationCell,
        },
        {
            accessorKey: 'status',
            header: t`Review status`,
            id: 'status',
            enableSorting: false,
            size: 200,
            cell: AccessReviewApplicationStatusCell,
        },
        {
            accessorKey: 'clientType',
            header: t`Warnings`,
            id: 'totalWarnings',
            enableSorting: false,
            size: 150,
            cell: AccessReviewApplicationWarningsCell,
        },
        {
            accessorKey: 'source',
            header: t`Type`,
            id: 'source',
            enableSorting: false,
            size: 600,
            cell: AccessReviewApplicationSourceCell,
        },
    ];

// For backward compatibility, create the constant that calls the function
export const ACCESS_REVIEW_APPLICATION_COMPLETED_COLUMNS =
    getAccessReviewApplicationCompletedColumns();
