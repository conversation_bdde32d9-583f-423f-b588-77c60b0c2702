import type { RiskResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export function getRiskTreatmentLabel(
    status: RiskResponseDto['treatmentPlan'],
): string {
    switch (status) {
        case 'UNTREATED': {
            return t`Untreated`;
        }
        case 'ACCEPT': {
            return t`Accept`;
        }
        case 'TRANSFER': {
            return t`Transfer`;
        }
        case 'AVOID': {
            return t`Avoid`;
        }
        case 'MITIGATE': {
            return t`Mitigate`;
        }
        default: {
            return '—';
        }
    }
}

export function getRiskTypeLabel(status: RiskResponseDto['type']): string {
    switch (status) {
        case 'INTERNAL': {
            return t`Internal`;
        }
        case 'EXTERNAL': {
            return t`External`;
        }
        default: {
            return '—';
        }
    }
}
