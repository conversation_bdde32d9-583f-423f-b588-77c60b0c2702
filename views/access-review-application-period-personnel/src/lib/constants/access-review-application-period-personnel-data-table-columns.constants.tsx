import type { DatatableProps } from '@cosmos/components/datatable';
import type { UserAccessReviewPeriodApplicationResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { ConnectionCell } from '../cells/connection-cell';
import { EmployeeStatusCell } from '../cells/employee-status-cell';
import { GroupsCell } from '../cells/groups-cell';
import { IdentityMfaCell } from '../cells/identity-mfa-cell';
import { JobTitleCell } from '../cells/job-title-cell';
import { PersonnelOwnerCell } from '../cells/personnel-owner-cell';
import { ReviewStatusCell } from '../cells/review-status-cell';
import { RolesCell } from '../cells/roles-cell';

interface ColumnRenderContext {
    isManuallyAddedApplication: boolean;
    isSummaryView: boolean;
    missingMFA: boolean;
    hasLimitedAccess: boolean;
}

type ExtendedColumnDef<T> = DatatableProps<T>['columns'][number] & {
    showIf: (context: ColumnRenderContext) => boolean;
};

export const getColumns =
    (): ExtendedColumnDef<UserAccessReviewPeriodApplicationResponseDto>[] =>
        [
            {
                id: 'CONNECTION',
                header: t`Account ID / alias`,
                accessorKey: 'clientType',
                enableSorting: false,
                cell: ConnectionCell,
                showIf: (ctx) =>
                    !ctx.isSummaryView && !ctx.isManuallyAddedApplication,
            },
            {
                id: 'USERNAME',
                header: t`Personnel`,
                accessorKey: 'username',
                enableSorting: true,
                cell: PersonnelOwnerCell,
                showIf: () => true,
            },
            {
                id: 'REVIEW_STATUS',
                showIf: (ctx) => !ctx.hasLimitedAccess,
                header: t`Review status`,
                minSize: 225,
                enableSorting: false,
                accessorKey: 'applicationUserId',
                cell: ReviewStatusCell,
                meta: {
                    shouldIgnoreRowClick: true,
                },
            },
            {
                id: 'JOB_TITLE',
                accessorKey: 'user',
                header: t`Job title`,
                cell: JobTitleCell,
                enableSorting: true,
                showIf: () => true,
            },
            {
                id: 'EMPLOYMENT_STATUS',
                accessorKey: 'employmentStatus',
                header: t`Personnel status`,
                cell: EmployeeStatusCell,
                enableSorting: true,
                showIf: () => true,
            },
            {
                id: 'IDENTITY_MFA',
                accessorKey: 'hasMfa',
                header: t`Identity MFA`,
                enableSorting: true,
                cell: IdentityMfaCell,
                showIf: (ctx) =>
                    !ctx.isSummaryView &&
                    !ctx.isManuallyAddedApplication &&
                    ctx.missingMFA,
            },
            {
                id: 'ROLES',
                accessorKey: 'roles',
                header: t`Roles`,
                enableSorting: false,
                cell: RolesCell,
                meta: {
                    shouldIgnoreRowClick: true,
                },
                showIf: () => true,
            },
            {
                id: 'GROUPS',
                accessorKey: 'groups',
                header: t`Groups`,
                enableSorting: false,
                cell: GroupsCell,
                showIf: () => true,
            },
        ] as const;
