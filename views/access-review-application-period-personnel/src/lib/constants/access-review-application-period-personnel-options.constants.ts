import type { AccessReviewStatus } from '@controllers/access-reviews';
import { t } from '@globals/i18n/macro';

export const getAccessReviewStatusOptions = (): {
    label: string;
    value: AccessReviewStatus;
}[] => [
    {
        label: t`Approved`,
        value: 'APPROVED',
    },
    {
        label: t`Rejected`,
        value: 'REJECTED',
    },
    {
        label: t`Out of scope`,
        value: 'OUT_OF_SCOPE',
    },
];
