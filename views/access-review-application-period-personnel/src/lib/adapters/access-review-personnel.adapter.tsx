import { openAccessReviewUpdatePersonnelModal } from '@components/access-review-empty-state';
import { Button } from '@cosmos/components/button';
import type {
    FilterViewModeProps,
    TableSettingsTriggerProps,
} from '@cosmos/components/datatable';
import type { EmptyStateProps } from '@cosmos/components/empty-state';
import type { SchemaDropdownProps } from '@cosmos/components/schema-dropdown';
import type {
    AccessReviewPeriodApplicationResponseDto,
    UserAccessReviewPeriodApplicationResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

/**
 * Constants for the adapter.
 */
const VIEW_MODES = {
    SUMMARY: 'summary',
    DETAILED: 'detailed',
} as const;

const COLOR_SCHEMES = {
    PRIMARY: 'primary',
    NEUTRAL: 'neutral',
} as const;

const FILTER_VIEW_MODE = 'pinned' as const;

export interface AccessReviewPersonnelAdapterResult {
    tableData: UserAccessReviewPeriodApplicationResponseDto[];
    totalCount: number;
    tableSettingsTriggerProps: TableSettingsTriggerProps;
    tableSearchProps: { hideSearch: boolean; placeholder: string };
    emptyStateProps: EmptyStateProps;
    filterViewModeProps: FilterViewModeProps;
}

export const accessReviewPersonnelAdapter = ({
    accessReviewPeriodApplicationUsersList,
    totalReviewApplicationUsers,
    isSummaryView,
    application,
    handleSummaryViewChange,
    hasActiveFilters = false,
}: {
    accessReviewPeriodApplicationUsersList: UserAccessReviewPeriodApplicationResponseDto[];
    totalReviewApplicationUsers: number;
    isSummaryView: boolean;
    application: AccessReviewPeriodApplicationResponseDto | undefined;
    handleSummaryViewChange: (value: boolean) => void;
    hasActiveFilters?: boolean;
}): AccessReviewPersonnelAdapterResult => {
    const { name = '', source = '' } = application ?? {};

    /**
     * Table settings items for view mode switching.
     */
    const settingsItems = [
        {
            id: VIEW_MODES.SUMMARY,
            label: t`Summary`,
            colorScheme: isSummaryView
                ? COLOR_SCHEMES.PRIMARY
                : COLOR_SCHEMES.NEUTRAL,
            onSelect: () => {
                handleSummaryViewChange(true);
            },
        },
        {
            id: VIEW_MODES.DETAILED,
            label: t`Detailed`,
            colorScheme: isSummaryView
                ? COLOR_SCHEMES.NEUTRAL
                : COLOR_SCHEMES.PRIMARY,
            onSelect: () => {
                handleSummaryViewChange(false);
            },
        },
    ];

    /**
     * Table settings trigger props for the settings dropdown.
     */
    const tableSettingsTriggerProps: TableSettingsTriggerProps = {
        actionType: 'dropdown',
        id: 'table-settings-trigger',
        typeProps: {
            isIconOnly: true,
            label: t`Settings`,
            level: 'tertiary',
            colorScheme: 'neutral',
            startIconName: 'Settings',
            items: settingsItems as SchemaDropdownProps['items'],
        },
    };

    /**
     * Table search props for the search input.
     */
    const tableSearchProps = {
        hideSearch: false,
        placeholder: t`Search personnel by name or job title`,
    };

    /**
     * Filter view mode props for toggling between pinned and dropdown filters.
     */
    const filterViewModeProps: FilterViewModeProps = {
        props: {
            selectedOption: FILTER_VIEW_MODE,
            initialSelectedOption: FILTER_VIEW_MODE,
            togglePinnedLabel: t`Pin filters to page`,
            toggleUnpinnedLabel: t`Move filters to dropdown`,
        },
        viewMode: 'toggleable',
    };

    /**
     * Empty state configuration.
     */
    const manuallyAddedEmptyState: EmptyStateProps = {
        illustrationName: 'AddCircle',
        imageSize: 'md',
        title: t`You’ve manually added "${name}" for access review`,
        description: t`Upload a list of personnel to get it ready for an access review.`,
        leftAction: (
            <Button
                label={t`Upload personnel`}
                level="primary"
                onClick={openAccessReviewUpdatePersonnelModal}
            />
        ),
    };

    const filteredEmptyState: EmptyStateProps = {
        title: t`We couldn't find any results for the information you requested. Try resetting or removing some of your filters.`,
    };

    const shouldShowManualEmptyState =
        source === 'MANUALLY_ADDED' && !hasActiveFilters;

    const emptyStateProps: EmptyStateProps = shouldShowManualEmptyState
        ? manuallyAddedEmptyState
        : filteredEmptyState;

    return {
        tableData: accessReviewPeriodApplicationUsersList,
        totalCount: totalReviewApplicationUsers,
        tableSettingsTriggerProps,
        tableSearchProps,
        emptyStateProps,
        filterViewModeProps,
    };
};
