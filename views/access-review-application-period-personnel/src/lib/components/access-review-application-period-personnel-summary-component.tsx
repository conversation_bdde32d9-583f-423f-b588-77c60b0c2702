import { sharedAccessReviewPeriodApplicationSummaryController } from '@controllers/access-reviews';
import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { Skeleton } from '@cosmos/components/skeleton';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const AccessReviewApplicationPeriodPersonnelSummaryComponent = observer(
    (): React.JSX.Element => {
        const {
            totalApproved,
            totalRejected,
            totalNotReviewed,
            totalOutOfScope,
            isLoading,
        } = sharedAccessReviewPeriodApplicationSummaryController;

        const summaryData = [
            {
                statValue: totalApproved,
                title: t`Approved`,
            },
            {
                statValue: totalRejected,
                title: t`Rejected`,
            },
            {
                statValue: totalNotReviewed,
                title: t`Not reviewed`,
            },
            {
                statValue: totalOutOfScope,
                title: t`Out of scope`,
            },
        ];

        if (isLoading) {
            return (
                <Skeleton
                    barCount={4}
                    data-testid="AccessReviewApplicationPeriodPersonnelSummaryComponent"
                    data-id="m8_X93kx"
                />
            );
        }

        return (
            <Grid
                columns="4"
                gap="4x"
                data-testid="AccessReviewApplicationPeriodPersonnelSummaryComponent"
                data-id="m8_X93kx"
            >
                {summaryData.map((data) => {
                    return (
                        <Box
                            key={data.title}
                            borderColor="neutralBorderInitial"
                            borderWidth="borderWidth1"
                            borderRadius="borderRadius2x"
                            pl="4x"
                            data-id="zmKhq4uU"
                        >
                            <StatBlock
                                data-id="cosmos-stat-block"
                                statValue={data.statValue}
                                title={data.title}
                            />
                        </Box>
                    );
                })}
            </Grid>
        );
    },
);
