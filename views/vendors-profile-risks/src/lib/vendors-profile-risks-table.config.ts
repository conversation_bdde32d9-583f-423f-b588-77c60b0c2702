import {
    VendorsRisksCellInherentScoreComponent,
    VendorsRisksCellOwnerAvatarComponent,
    VendorsRisksCellResidualScoreComponent,
    VendorsRisksCellScoreSelectImpactComponent,
    VendorsRisksCellScoreSelectLikelihoodComponent,
    VendorsRisksCellScoreSelectResidualImpactComponent,
    VendorsRisksCellScoreSelectResidualLikelihoodComponent,
    VendorsRisksCellTreatmentSelect,
} from '@components/vendors-risks';
import type { VendorsRisksTableItem } from '@controllers/vendors';
import type { DatatableProps } from '@cosmos/components/datatable';
import {
    getAnticipatedCompletionDateHeader,
    getInherentImpactHeader,
    getInherentLikelihoodHeader,
    getOwnerHeader,
    getResidualImpactHeader,
    getResidualLikelihoodHeader,
    getResidualScoreHeader,
    getRiskIdentifiedDateHeader,
    getRiskNameHeader,
    getRiskScoreHeader,
    getTreatmentHeader,
} from './helpers/vendors-current-risks-table-header-labels.helpers';

export const getTableColumns =
    (): DatatableProps<VendorsRisksTableItem>['columns'] => [
        {
            accessorKey: 'identifiedAt',
            header: getRiskIdentifiedDateHeader(),
            id: 'identifiedAt',
        },
        {
            accessorKey: 'title',
            header: getRiskNameHeader(),
            id: 'riskName',
        },
        {
            cell: VendorsRisksCellScoreSelectImpactComponent,
            header: getInherentImpactHeader(),
            id: 'impact',
            accessorKey: 'title', // use title as accessor key to always render the cell
            meta: {
                shouldIgnoreRowClick: true,
            },
        },
        {
            cell: VendorsRisksCellScoreSelectLikelihoodComponent,
            header: getInherentLikelihoodHeader(),
            id: 'likelihood',
            accessorKey: 'title', // use title as accessor key to always render the cell
            meta: {
                shouldIgnoreRowClick: true,
            },
        },
        {
            cell: VendorsRisksCellInherentScoreComponent,
            header: getRiskScoreHeader(),
            id: 'riskScore',
            accessorKey: 'score',
            enableSorting: false,
        },
        {
            cell: VendorsRisksCellScoreSelectResidualImpactComponent,
            header: getResidualImpactHeader(),
            id: 'residualImpact',
            accessorKey: 'title', // use title as accessor key to always render the cell
            meta: {
                shouldIgnoreRowClick: true,
            },
        },
        {
            cell: VendorsRisksCellScoreSelectResidualLikelihoodComponent,
            id: 'residualLikelihood',
            header: getResidualLikelihoodHeader(),
            accessorKey: 'title', // use title as accessor key to always render the cell
            meta: {
                shouldIgnoreRowClick: true,
            },
        },
        {
            cell: VendorsRisksCellResidualScoreComponent,
            header: getResidualScoreHeader(),
            id: 'residualScore',
            accessorKey: 'residualScore',
            enableSorting: false,
        },
        {
            header: getOwnerHeader(),
            id: 'owners',
            accessorKey: 'owners',
            enableSorting: false,
            cell: VendorsRisksCellOwnerAvatarComponent,
            meta: {
                shouldIgnoreRowClick: true,
            },
        },
        {
            cell: VendorsRisksCellTreatmentSelect,
            header: getTreatmentHeader(),
            id: 'treatment',
            accessorKey: 'treatmentPlan',
            minSize: 160,
            maxSize: 160,
            size: 160,
            meta: {
                shouldIgnoreRowClick: true,
            },
        },
        {
            accessorKey: 'anticipatedCompletionDate',
            header: getAnticipatedCompletionDateHeader(),
            id: 'anticipatedCompletionDate',
        },
    ];
