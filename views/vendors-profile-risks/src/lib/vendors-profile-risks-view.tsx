import { isEmpty, isNil } from 'lodash-es';
import { useCallback, useMemo } from 'react';
import {
    getRiskFilterOptions,
    RISK_FILTER_ID,
} from '@components/risk-register';
import { VendorsRisksOverviewPanelComponent } from '@components/vendors-risks-overview-panel';
import {
    handleOpenDetailsPanel,
    sharedVendorsDetailsController,
    sharedVendorsProfileRisksController,
} from '@controllers/vendors';
import { Button } from '@cosmos/components/button';
import {
    Datatable,
    DEFAULT_TABLE_SEARCH_PROPS,
} from '@cosmos/components/datatable';
import { EmptyState } from '@cosmos/components/empty-state';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { getTableActions } from './helpers/get-table-actions.helper';
import { getTableColumns } from './vendors-profile-risks-table.config';

export const VendorsProfileRisksView = observer((): React.JSX.Element => {
    const {
        allVendorsRisks,
        isLoading,
        hasSearchFilter,
        loadVendorsRisks,
        total,
    } = sharedVendorsProfileRisksController;
    const { vendorDetails } = sharedVendorsDetailsController;

    const navigate = useNavigate();
    const EXTERNAL_RISK_FILTER_VALUE = useMemo(
        () =>
            getRiskFilterOptions().find(
                (option) => option.value === 'EXTERNAL_RISKS',
            )?.value,
        [],
    );

    const onAddRiskClick = useCallback(() => {
        navigate('add');
    }, [navigate]);

    const emptyRisksListDescription = useMemo(() => {
        return (
            t`Add and track risks related to ` +
            (vendorDetails?.name ?? t`this vendor`) +
            t`.`
        );
    }, [vendorDetails?.name]);

    if (!isLoading && !hasSearchFilter && isEmpty(allVendorsRisks)) {
        return (
            <EmptyState
                title={t`Stay on top of risks that vendors pose to your organization.`}
                description={emptyRisksListDescription}
                illustrationName="RiskManagement"
                leftAction={
                    sharedFeatureAccessModel.isRiskManagerWithRestrictedView ? null : (
                        <Button
                            label={t`Add risk`}
                            level="primary"
                            data-id="add-risk-empty-state-button"
                            onClick={onAddRiskClick}
                        />
                    )
                }
            />
        );
    }

    return (
        <Stack direction="column" gap="2xl" data-id="jUNdRA3f">
            {!isLoading && !isNil(vendorDetails) && (
                <Text>
                    {t`These are all the risks from `}
                    <AppLink
                        href={`/workspaces/${sharedWorkspacesController.currentWorkspace?.id}/risk/register/management?${RISK_FILTER_ID}=${EXTERNAL_RISK_FILTER_VALUE}`}
                        data-id={`vendor-risk-management-link`}
                        label={t`Risk Management`}
                    />
                    {t` related to`} {vendorDetails.name}
                </Text>
            )}

            <Datatable
                isLoading={isLoading}
                tableId="vendor-risks-overview-datatable"
                total={total}
                data={allVendorsRisks}
                data-id="vendor-risks-overview-datatable"
                columns={getTableColumns()}
                tableActions={getTableActions(onAddRiskClick)}
                tableSearchProps={DEFAULT_TABLE_SEARCH_PROPS}
                emptyStateProps={{
                    title: t`No risks found for this vendor`,
                    description: t`This vendor doesn't have any risks matching your search criteria.`,
                    illustrationName: 'RiskManagement',
                }}
                filterViewModeProps={{
                    viewMode: 'pinned',
                }}
                onFetchData={loadVendorsRisks}
                onRowClick={({ row }) => {
                    handleOpenDetailsPanel(row.riskId, () => (
                        <VendorsRisksOverviewPanelComponent data-id="AuHVo_C4" />
                    ));
                }}
            />
        </Stack>
    );
});
