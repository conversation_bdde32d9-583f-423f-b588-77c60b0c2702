import { t } from '@globals/i18n/macro';

export const getRiskIdentifiedDateHeader = (): string => {
    return t`Risk identified date`;
};

export const getRiskNameHeader = (): string => {
    return t`Risk name`;
};

export const getInherentImpactHeader = (): string => {
    return t`Inherent impact`;
};

export const getInherentLikelihoodHeader = (): string => {
    return t`Inherent likelihood`;
};

export const getRiskScoreHeader = (): string => {
    return t`Risk Score`;
};

export const getResidualImpactHeader = (): string => {
    return t`Residual impact`;
};

export const getResidualLikelihoodHeader = (): string => {
    return t`Residual likelihood`;
};

export const getResidualScoreHeader = (): string => {
    return t`Residual Score`;
};

export const getOwnerHeader = (): string => {
    return t`Owners`;
};

export const getTreatmentHeader = (): string => {
    return t`Treatment`;
};

export const getAnticipatedCompletionDateHeader = (): string => {
    return t`Anticipated completion date`;
};
