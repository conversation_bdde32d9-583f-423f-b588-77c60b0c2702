import { RiskCategoryCell } from '@components/risk-register';
import {
    FlatfileEntityType,
    sharedFlatfileController,
} from '@controllers/flatfile';
import type { DatatableProps } from '@cosmos/components/datatable';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { CompletedDateCell } from '../cells/completed-date-cell';
import { InherentImpactCell } from '../cells/inherent-impact-cell';
import { InherentLikelihoodCell } from '../cells/inherent-likelihood-cell';
import { InherentScoreCell } from '../cells/inherent-score-cell';
import { IsInternalCell } from '../cells/is-internal-cell';
import { MitigatingControlsCell } from '../cells/mitigating-controls-cell';
import { ResidualImpactCell } from '../cells/residual-impact-cell';
import { ResidualLikelihoodCell } from '../cells/residual-likelihood-cell';
import { ResidualScoreCell } from '../cells/residual-score-cell';
import { RiskCodeCell } from '../cells/risk-code-cell';
import { RiskManagementActionButtonCell } from '../cells/risk-management-action-button-cell';
import { RiskNameCell } from '../cells/risk-name-cell';
import { RiskOwnerCell } from '../cells/risk-owner-cell';
import { RiskTreatmentCell } from '../cells/risk-treatment-cell';
import { TargetedCompletionDateCell } from '../cells/targeted-completion-date-cell';

export const RISK_MANAGEMENT_COLUMNS = [
    {
        id: 'risk-management-action-button',
        isActionColumn: true,
        cell: RiskManagementActionButtonCell,
    },
    {
        accessorKey: 'riskId',
        header: 'Risk code',
        id: 'code',
        enableSorting: true,
        cell: RiskCodeCell,
    },
    {
        accessorKey: 'title',
        header: 'Name',
        id: 'name',
        enableSorting: true,
        cell: RiskNameCell,
    },
    {
        accessorKey: 'controls',
        header: 'Mitigating controls',
        id: 'mitigatingControls',
        enableSorting: false,
        cell: MitigatingControlsCell,
    },
    {
        accessorKey: 'treatmentPlan',
        header: 'Treatment',
        id: 'treatment',
        enableSorting: true,
        cell: RiskTreatmentCell,
    },
    {
        accessorKey: 'impact',
        header: 'Inherent impact',
        id: 'inherentImpact',
        enableSorting: true,
        cell: InherentImpactCell,
    },
    {
        accessorKey: 'likelihood',
        header: 'Inherent likelihood',
        id: 'inherentLikelihood',
        enableSorting: true,
        cell: InherentLikelihoodCell,
    },
    {
        accessorKey: 'score',
        header: 'Inherent score',
        id: 'inherentScore',
        enableSorting: true,
        cell: InherentScoreCell,
    },
    {
        accessorKey: 'residualImpact',
        header: 'Residual impact',
        id: 'residualImpact',
        enableSorting: true,
        cell: ResidualImpactCell,
    },
    {
        accessorKey: 'residualLikelihood',
        header: 'Residual likelihood',
        id: 'residualLikelihood',
        enableSorting: true,
        cell: ResidualLikelihoodCell,
    },
    {
        accessorKey: 'residualScore',
        header: 'Residual score',
        id: 'residualScore',
        enableSorting: true,
        cell: ResidualScoreCell,
    },
    {
        accessorKey: 'owners',
        header: 'Owners',
        id: 'owners',
        enableSorting: true,
        cell: RiskOwnerCell,
    },
    {
        accessorKey: 'type',
        header: 'Type',
        id: 'isInternal',
        enableSorting: true,
        cell: IsInternalCell,
    },
    {
        accessorKey: 'categories',
        header: 'Categories',
        id: 'categories',
        enableSorting: true,
        cell: RiskCategoryCell,
    },
    {
        accessorKey: 'anticipatedCompletionDate',
        header: 'Targeted completion date',
        id: 'targetedCompletionDate',
        enableSorting: true,
        cell: TargetedCompletionDateCell,
    },
    {
        accessorKey: 'completionDate',
        header: 'Completed date',
        id: 'completedDate',
        enableSorting: true,
        cell: CompletedDateCell,
    },
] as const satisfies DatatableProps<RiskWithCustomFieldsResponseDto>['columns'];

export const RISK_MANAGEMENT_TABLE_ACTIONS = [
    {
        actionType: 'dropdown',
        id: 'add-risk-dropdown',
        typeProps: {
            label: 'Add risk',
            endIconName: 'ChevronDown',
            level: 'primary',
            items: [
                {
                    id: 'import-risks',
                    'data-testid': 'ImportRiskAction',
                    label: 'Import risks',
                    type: 'item',
                    onClick: () => {
                        sharedFlatfileController.createSpace({
                            entityType: FlatfileEntityType.RISK,
                        });
                    },
                },
            ],
        },
    },
] as const satisfies DatatableProps<RiskWithCustomFieldsResponseDto>['tableActions'];
