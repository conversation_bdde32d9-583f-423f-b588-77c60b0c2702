import {
    getRiskFilterOptions,
    RISK_FILTER_ID,
} from '@components/risk-register';
import type { FilterProps } from '@cosmos/components/datatable';

export const getRiskManagementFilters = (): FilterProps => ({
    clearAllButtonLabel: 'Reset',
    filters: [
        {
            filterType: 'checkbox',
            id: 'riskStatus',
            label: 'Risk Status',
            options: [
                {
                    label: 'Active',
                    value: 'active',
                },
                {
                    label: 'Closed',
                    value: 'closed',
                },
                {
                    label: 'Archived',
                    value: 'archived',
                },
            ],
        },
        {
            filterType: 'radio',
            id: 'assessment',
            label: 'Assessment',
            options: [
                {
                    label: 'Not scored',
                    value: 'not-scored',
                },
                {
                    label: 'Scored',
                    value: 'scored',
                },
            ],
        },
        {
            filterType: 'checkbox',
            id: 'treatment',
            label: 'Treatment',
            options: [
                {
                    label: 'Accept',
                    value: 'accept',
                },
                {
                    label: 'Mitigate',
                    value: 'mitigate',
                },
                {
                    label: 'Avoid',
                    value: 'avoid',
                },
                {
                    label: 'Transfer',
                    value: 'transfer',
                },
                {
                    label: 'Untreated',
                    value: 'untreated',
                },
            ],
        },
        {
            filterType: 'radio',
            id: RISK_FILTER_ID,
            label: 'Risks',
            options: getRiskFilterOptions(),
        },
        {
            filterType: 'radio',
            id: 'riskOwners',
            label: 'Risk owners',
            options: [
                {
                    label: 'Owners assigned',
                    value: 'owners-assigned',
                },
                {
                    label: 'No owners assigned',
                    value: 'no-owners-assigned',
                },
            ],
        },
        {
            isMultiSelect: true,
            filterType: 'combobox',
            id: 'owners',
            label: 'Owners',
            placeholder: 'Search',
            options: [
                {
                    id: 'owner-1',
                    label: 'Owner 1',
                    value: 'OWNER-1',
                },
                {
                    id: 'owner-2',
                    label: 'Owner 2',
                    value: 'OWNER-2',
                },
            ],
        },
        {
            isMultiSelect: true,
            filterType: 'combobox',
            id: 'categories',
            label: 'Categories',
            placeholder: 'Search',
            options: [
                {
                    id: 'category-1',
                    label: 'Category 1',
                    value: 'CATEGORY-1',
                },
                {
                    id: 'category-2',
                    label: 'Category 2',
                    value: 'CATEGORY-2',
                },
                {
                    id: 'category-3',
                    label: 'Category 3',
                    value: 'CATEGORY-3',
                },
                {
                    id: 'category-4',
                    label: 'Category 4',
                    value: 'CATEGORY-4',
                },
            ],
        },
    ],
});
