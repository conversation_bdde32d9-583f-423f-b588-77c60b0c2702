import { useMemo } from 'react';
import { handleOpenRiskPanel } from '@components/risks';
import { sharedRiskManagementController } from '@controllers/risk';
import { Datatable, type DatatableProps } from '@cosmos/components/datatable';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { observer } from '@globals/mobx';
import {
    RISK_MANAGEMENT_COLUMNS,
    RISK_MANAGEMENT_TABLE_ACTIONS,
} from './constants/risk-register-management.constants';
import { getRiskManagementFilters } from './helpers/get-risk-management-filters.helper';

export const RiskRegisterManagementView = observer((): React.JSX.Element => {
    const { risks, total, isLoading, loadRiskManagement } =
        sharedRiskManagementController;

    const tableActions = useMemo(() => {
        if (sharedFeatureAccessModel.isReleaseBulkImportEnabled) {
            return RISK_MANAGEMENT_TABLE_ACTIONS;
        }

        // When bulk import is disabled, return dropdown without the 'import-risks' item
        return [
            {
                ...RISK_MANAGEMENT_TABLE_ACTIONS[0],
                typeProps: {
                    ...RISK_MANAGEMENT_TABLE_ACTIONS[0].typeProps,
                    items: [], // Remove all items (only 'import-risks' exists)
                },
            },
        ] as DatatableProps<RiskWithCustomFieldsResponseDto>['tableActions'];
    }, []);

    return (
        <Datatable
            isRowSelectionEnabled
            isLoading={isLoading}
            tableId="risk-register-library-datatable"
            total={total}
            data={risks}
            columns={RISK_MANAGEMENT_COLUMNS}
            filterProps={getRiskManagementFilters()}
            tableActions={tableActions}
            data-testid="RiskRegisterManagementView"
            data-id="QiFohnTC"
            tableSearchProps={{
                placeholder:
                    'Search by name, description, control code, or requirement ',
                hideSearch: false,
                debounceDelay: 1000,
                defaultValue: '',
            }}
            onFetchData={loadRiskManagement}
            onRowClick={({ row }) => {
                handleOpenRiskPanel(row.id, row.riskId);
            }}
        />
    );
});
