import { sharedAuditHubEvidenceController } from '@controllers/audit-hub';
import { sharedAuditHubEvidenceViewerController } from '@controllers/audit-hub-evidence-viewer';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { Datatable } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import {
    EVIDENCE_REQUEST_DETAILS_PAGINATION_OPTIONS,
    getEvidenceRequestDetailsColumns,
} from './constants/evidence-columns.constant';
import { getEvidenceRequestDetailsFilters } from './helpers/evidence-filters.helper';

export const EvidenceRequestDetailsEvidenceView = observer((): JSX.Element => {
    const navigate = useNavigate();
    const {
        auditCustomerRequestEvidences,
        auditCustomerRequestEvidencesIsLoading,
        auditCustomerRequestEvidencesTotal,
        auditCustomerRequestAvailableEvidenceTypes,
        loadEvidencesPage,
    } = sharedAuditHubEvidenceController;

    const {
        clientId,
        auditorFrameworkId,
        getRequestId: requestId,
    } = sharedCustomerRequestDetailsController;

    return (
        <Datatable
            isRowSelectionEnabled
            isLoading={auditCustomerRequestEvidencesIsLoading}
            tableId="datatable-audit-hub-evidence-request-details"
            data={auditCustomerRequestEvidences}
            total={auditCustomerRequestEvidencesTotal}
            data-testid="EvidenceRequestDetailsView"
            data-id="U9j_XYCl"
            columns={getEvidenceRequestDetailsColumns()}
            filterProps={getEvidenceRequestDetailsFilters(
                auditCustomerRequestAvailableEvidenceTypes,
            )}
            defaultPaginationOptions={
                EVIDENCE_REQUEST_DETAILS_PAGINATION_OPTIONS.defaultPaginationOptions
            }
            bulkActionDropdownItems={[
                {
                    actionType: 'button',
                    id: 'download-button',
                    typeProps: {
                        startIconName: 'Download',
                        label: t`Download selection`,
                        level: 'tertiary',
                        onClick: () => {
                            // TODO IN https://drata.atlassian.net/browse/ENG-70415
                        },
                    },
                },
            ]}
            tableActions={[
                {
                    actionType: 'button',
                    id: 'download-button',
                    typeProps: {
                        startIconName: 'Download',
                        isIconOnly: false,
                        label: t`Download all`,
                        level: 'tertiary',
                        colorScheme: 'neutral',
                        isLoading: false,
                        a11yLoadingLabel: 'Downloading evidence',
                        onClick: () => {
                            // TODO IN https://drata.atlassian.net/browse/ENG-70415
                        },
                    },
                },
            ]}
            filterViewModeProps={{
                viewMode: 'unpinned',
            }}
            emptyStateProps={{
                illustrationName: 'Warning',
                title: t`Evidence`,
                description: t`No evidence were found`,
            }}
            onFetchData={loadEvidencesPage}
            onRowClick={({ row: evidenceData, _internal }) => {
                const { index: rowIndex } = _internal;

                sharedAuditHubEvidenceViewerController.setSelectedEvidence(
                    evidenceData,
                    rowIndex,
                );

                navigate(
                    `/audit-hub/clients/${clientId}/audits/${auditorFrameworkId}/evidence-requests/${requestId}/evidence-viewer`,
                );
            }}
        />
    );
});
