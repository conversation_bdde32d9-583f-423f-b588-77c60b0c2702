import { isNil } from 'lodash-es';
import { useState } from 'react';
import {
    AccessReviewActiveDataTable,
    AccessReviewPeriodRangeComponent,
    CompleteReviewModal,
} from '@components/access-review';
import { sharedActiveAccessReviewPeriodsController } from '@controllers/access-reviews';
import { modalController } from '@controllers/modal';
import { Banner } from '@cosmos/components/banner';
import { Button } from '@cosmos/components/button';
import { EmptyState } from '@cosmos/components/empty-state';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { compareDayIsPast, formatDate } from '@helpers/date-time';
import { useNavigate } from '@remix-run/react';
import { AppLink } from '@ui/app-link';

const COMPLETE_REVIEW_MODAL_ID = 'complete-review-modal';

interface GovernanceAccessReviewActivePresentationProps {
    isLoading: boolean;
    hasActivePeriod: boolean | undefined;
    totalApplications: number;
    hasLimitedAccess: boolean;
    onCreateReviewPeriod: () => void;
    onEditReviewPeriod: () => void;
    shouldOpenCompleteReviewModal: () => void;
    onGoToConnections: () => void;
    isDueDate: boolean;
    totalNotCompletedApplications: number;
}

const MINIMUM_TOTAL_APPLICATIONS = 1;

const NoActivePeriodEmptyState = ({
    hasLimitedAccess,
    onCreateReviewPeriod,
}: Pick<
    GovernanceAccessReviewActivePresentationProps,
    'hasLimitedAccess' | 'onCreateReviewPeriod'
>) => {
    const canCreatePeriod = !hasLimitedAccess;

    return (
        <EmptyState
            isStacked
            title={t`Use review periods to carry out time bound access reviews`}
            description={t`Initiate periodic reviews and upon completion, evidence generated is automatically linked to relevant controls, ensuring compliance and saving you time.`}
            illustrationName="AddCircle"
            data-testid="NoActivePeriodEmptyState"
            data-id="7i1iTLWv"
            leftAction={
                canCreatePeriod ? (
                    <Button
                        colorScheme="primary"
                        size="md"
                        label={t`Create review period`}
                        onClick={onCreateReviewPeriod}
                    />
                ) : null
            }
            rightAction={
                <AppLink
                    isExternal
                    size="md"
                    href="https://help.drata.com/en/articles/8895897-access-reviews#h_4d04aa7427"
                    label={t`Learn about access review periods`}
                />
            }
        />
    );
};

const NoApplicationsEmptyState = ({
    hasLimitedAccess,
    onEditReviewPeriod,
    onGoToConnections,
}: Pick<
    GovernanceAccessReviewActivePresentationProps,
    'hasLimitedAccess' | 'onEditReviewPeriod' | 'onGoToConnections'
>) => {
    const canManageApplications = !hasLimitedAccess;

    return (
        <EmptyState
            isStacked
            illustrationName="AddCircle"
            title={t`No applications in the review period`}
            data-testid="NoApplicationsEmptyState"
            data-id="C3IF-1s_"
            description={
                hasLimitedAccess
                    ? t`Contact your administrator to review the issue`
                    : t`You can add applications to this active review period by editing it.`
            }
            leftAction={
                canManageApplications ? (
                    <Button
                        label={t`Go to connections`}
                        level="secondary"
                        onClick={onGoToConnections}
                    />
                ) : null
            }
            rightAction={
                canManageApplications ? (
                    <Button
                        label={t`Edit review period`}
                        onClick={onEditReviewPeriod}
                    />
                ) : null
            }
        />
    );
};

const ReviewStatusBanners = ({
    isDueDate,
    shouldShowCompletedApplicationsBanner,
    totalNotCompletedApplications,
}: {
    isDueDate: boolean;
    shouldShowCompletedApplicationsBanner: boolean;
    totalNotCompletedApplications: number;
}): React.JSX.Element => (
    <>
        {isDueDate && (
            <Banner
                displayMode="section"
                severity="critical"
                title={t`This review period is overdue`}
                body={t`Make sure reviewers have completed all user access reviews for each application before completing this review period.`}
                data-testid="OverdueBanner"
                data-id="overdue-banner"
            />
        )}
        {shouldShowCompletedApplicationsBanner && (
            <Banner
                displayMode="section"
                severity="warning"
                title={t`Complete all application reviews`}
                data-testid="IncompleteApplicationsBanner"
                data-id="incomplete-apps-banner"
                body={
                    totalNotCompletedApplications === 1
                        ? t`You have 1 application that has not been reviewed. Review it as soon as possible to be able to complete this review period.`
                        : t`You have ${totalNotCompletedApplications} applications that have not been reviewed. Review them as soon as possible to be able to complete this review period.`
                }
            />
        )}
    </>
);

const GovernanceAccessReviewActivePresentation = ({
    isLoading,
    hasActivePeriod,
    totalApplications,
    hasLimitedAccess,
    onCreateReviewPeriod,
    onEditReviewPeriod,
    isDueDate,
    totalNotCompletedApplications,
    shouldOpenCompleteReviewModal,
    onGoToConnections,
}: GovernanceAccessReviewActivePresentationProps): React.JSX.Element => {
    const [
        shouldShowCompletedApplicationsBanner,
        setShouldShowCompletedApplicationsBanner,
    ] = useState(false);

    const shouldOpenCompleteReviewModalHandler = (shouldShow: boolean) => {
        setShouldShowCompletedApplicationsBanner(shouldShow);

        if (shouldShow) {
            return;
        }

        shouldOpenCompleteReviewModal();
    };

    if (isLoading) {
        return (
            <Stack
                direction="column"
                align="center"
                gap="6x"
                p="8x"
                data-testid="GovernanceAccessReviewActiveLoader"
                data-id="loading-state"
            >
                <Loader isSpinnerOnly label={t`Loading access review data`} />
            </Stack>
        );
    }

    if (!hasActivePeriod) {
        return (
            <NoActivePeriodEmptyState
                hasLimitedAccess={hasLimitedAccess}
                onCreateReviewPeriod={onCreateReviewPeriod}
            />
        );
    }

    if (totalApplications < MINIMUM_TOTAL_APPLICATIONS) {
        return (
            <NoApplicationsEmptyState
                hasLimitedAccess={hasLimitedAccess}
                onEditReviewPeriod={onEditReviewPeriod}
                onGoToConnections={onGoToConnections}
            />
        );
    }

    return (
        <Stack
            py="8x"
            direction="column"
            gap="xl"
            data-testid="GovernanceAccessReviewActivePresentation"
            data-id="yzcfZDE1"
        >
            <ReviewStatusBanners
                isDueDate={isDueDate}
                totalNotCompletedApplications={totalNotCompletedApplications}
                shouldShowCompletedApplicationsBanner={
                    shouldShowCompletedApplicationsBanner
                }
            />
            <AccessReviewPeriodRangeComponent
                totalNotCompletedApplications={totalNotCompletedApplications}
                shouldOpenCompleteReviewModal={
                    shouldOpenCompleteReviewModalHandler
                }
            />
            <AccessReviewActiveDataTable />
        </Stack>
    );
};

export const GovernanceAccessReviewActiveView = observer(
    (): React.JSX.Element => {
        const navigate = useNavigate();
        const {
            isLoading,
            totalApplications,
            hasActivePeriod,
            activeAccessReviewPeriod,
        } = sharedActiveAccessReviewPeriodsController;
        const { currentWorkspaceId } = sharedWorkspacesController;
        const { hasLimitedAccess } = sharedFeatureAccessModel;

        const totalNotCompletedApplicationsFiltered =
            activeAccessReviewPeriod?.applications.filter(
                (app) => app.status !== 'COMPLETED' && app.status !== 'DELETED',
            ).length ?? 0;

        const isActivePeriodInDueDate = isNil(activeAccessReviewPeriod?.endDate)
            ? false
            : compareDayIsPast(activeAccessReviewPeriod.endDate, new Date());

        const handleCreateReviewPeriod = (): void => {
            navigate(
                `/workspaces/${currentWorkspaceId}/governance/access-review/create-period`,
            );
        };

        const openReviewModalHandler = (): void => {
            if (!activeAccessReviewPeriod) {
                console.error('No active access review period found');

                return;
            }

            // Format the review period range for the modal
            const reviewPeriodRange =
                activeAccessReviewPeriod.startDate &&
                activeAccessReviewPeriod.endDate
                    ? formatDate(
                          'sentence_range',
                          activeAccessReviewPeriod.startDate.split('T')[0],
                          activeAccessReviewPeriod.endDate.split('T')[0],
                      )
                    : '-';

            modalController.openModal({
                id: COMPLETE_REVIEW_MODAL_ID,
                content: () => (
                    <CompleteReviewModal
                        modalId={COMPLETE_REVIEW_MODAL_ID}
                        reviewPeriodRange={reviewPeriodRange}
                        data-id="tTj7yfj1"
                    />
                ),
                centered: true,
                disableClickOutsideToClose: false,
            });
        };

        const handleGoToConnections = (): void => {
            navigate(
                `/workspaces/${currentWorkspaceId}/connections/all/active`,
            );
        };

        const handleEditReviewPeriod = (): void => {
            navigate(
                `/workspaces/${currentWorkspaceId}/governance/access-review/edit-period/${activeAccessReviewPeriod?.id}`,
            );
        };

        return (
            <GovernanceAccessReviewActivePresentation
                isLoading={isLoading}
                hasActivePeriod={hasActivePeriod}
                totalApplications={totalApplications}
                hasLimitedAccess={hasLimitedAccess}
                data-id="das1QBB-"
                isDueDate={isActivePeriodInDueDate}
                shouldOpenCompleteReviewModal={openReviewModalHandler}
                totalNotCompletedApplications={
                    totalNotCompletedApplicationsFiltered
                }
                onCreateReviewPeriod={handleCreateReviewPeriod}
                onEditReviewPeriod={handleEditReviewPeriod}
                onGoToConnections={handleGoToConnections}
            />
        );
    },
);
