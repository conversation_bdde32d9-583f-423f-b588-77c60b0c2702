import { type ComponentProps, useState } from 'react';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { SimpleTable } from '@cosmos-lab/components/simple-table';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { MAX_COUNT_OF_ITEMS_TO_DISPLAY_BY_DEFAULT } from '../constants/tasks.constants';

const START_IDX = 0;

interface ShowMoreTasksSectionProps {
    caption: string;
    columns: ComponentProps<typeof SimpleTable>['columns'];
    incompleteTasks: ComponentProps<typeof SimpleTable>['data'];
}

export const ShowMoreTasksSection = observer(
    ({
        caption,
        columns,
        incompleteTasks,
    }: ShowMoreTasksSectionProps): React.JSX.Element => {
        const [shouldShowAll, setShouldShowAll] = useState<boolean>(false);
        const hasMoreTasks =
            incompleteTasks.length > MAX_COUNT_OF_ITEMS_TO_DISPLAY_BY_DEFAULT;

        return (
            <>
                <SimpleTable
                    caption={caption}
                    columns={columns}
                    data-testid="incompleteTasks"
                    data={
                        shouldShowAll
                            ? incompleteTasks
                            : incompleteTasks.slice(
                                  START_IDX,
                                  MAX_COUNT_OF_ITEMS_TO_DISPLAY_BY_DEFAULT,
                              )
                    }
                    options={{
                        shouldHideHeader: true,
                    }}
                />

                {hasMoreTasks && (
                    <Box>
                        <Button
                            label={shouldShowAll ? t`Show less` : t`Show more`}
                            level="tertiary"
                            size="md"
                            aria-expanded={shouldShowAll}
                            aria-controls="show-more-content"
                            endIconName={
                                shouldShowAll ? 'ChevronUp' : 'ChevronDown'
                            }
                            onClick={() => {
                                setShouldShowAll(!shouldShowAll);
                            }}
                        />
                    </Box>
                )}
            </>
        );
    },
);
