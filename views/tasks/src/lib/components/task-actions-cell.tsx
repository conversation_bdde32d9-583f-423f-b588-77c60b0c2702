import { isFunction } from 'lodash-es';
import { useMemo } from 'react';
import type { TaskActionsCellData } from '@controllers/tasks';
import { type Action, ActionStack } from '@cosmos/components/action-stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';

interface TaskActionsCellProps {
    cellData: TaskActionsCellData;
}

export const TaskActionsCell = observer(
    ({ cellData }: TaskActionsCellProps): React.JSX.Element => {
        const { severity, onEdit, onReview, onToggleComplete } = cellData;

        const navigate = useNavigate();

        const actionsStack = useMemo(() => {
            const actions: Action[] = [];

            if (isFunction(onEdit)) {
                actions.push({
                    actionType: 'button',
                    id: 'edit-button',
                    typeProps: {
                        startIconName: 'Edit',
                        label: t`Edit`,
                        isIconOnly: true,
                        level: 'tertiary',
                        colorScheme: 'neutral',
                        onClick: onEdit,
                    },
                });
            }

            if (isFunction(onReview)) {
                actions.push({
                    actionType: 'button',
                    id: 'review-button',
                    typeProps: {
                        label: t`Review`,
                        level: 'secondary',
                        colorScheme: 'neutral',
                        onClick: () => {
                            onReview(navigate);
                        },
                    },
                });
            }

            if (isFunction(onToggleComplete)) {
                actions.push(
                    severity === 'success'
                        ? {
                              actionType: 'button',
                              id: 'mark-as-incomplete-button',
                              typeProps: {
                                  startIconName: 'CheckCircle',
                                  label: t`Mark as incomplete`,
                                  isIconOnly: true,
                                  level: 'tertiary',
                                  colorScheme: 'neutral',
                                  onClick: onToggleComplete,
                              },
                          }
                        : {
                              actionType: 'button',
                              id: 'mark-as-complete-button',
                              typeProps: {
                                  startIconName: 'NotStarted',
                                  label: t`Mark as complete`,
                                  isIconOnly: true,
                                  level: 'secondary',
                                  colorScheme: 'primary',
                                  onClick: onToggleComplete,
                              },
                          },
                );
            }

            return actions;
        }, [navigate, onEdit, onReview, onToggleComplete, severity]);

        return (
            <ActionStack
                isFullWidth
                gap="lg"
                data-testid="TaskActionsCell"
                data-id="Tzo2UK2G"
                stacks={[
                    {
                        id: 'action-stack-controls',
                        shouldFlex: true,
                        shouldWrap: false,
                        alignment: 'end',
                        actions: actionsStack,
                    },
                ]}
            />
        );
    },
);
