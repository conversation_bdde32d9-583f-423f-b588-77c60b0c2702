import { isEmpty } from 'lodash-es';
import { type ComponentProps, useEffect, useMemo } from 'react';
import {
    CustomTaskDetailsByCategoryController,
    sharedCustomTasksController,
    type TaskAccordionType,
} from '@controllers/tasks';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { SimpleTable } from '@cosmos-lab/components/simple-table';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import { CompletedTasksSection } from './completed-tasks-section';
import { ShowMoreTasksSection } from './show-more-tasks-section';

interface TasksListBodyProps {
    columns: ComponentProps<typeof SimpleTable>['columns'];
    caption: string;
    type: TaskAccordionType;
    month: number;
}

export const TasksListBody = observer(
    ({
        columns,
        caption,
        type,
        month,
    }: TasksListBodyProps): React.JSX.Element => {
        const { filteredYear: taskYear, filteredOwnerId: ownerId } =
            sharedCustomTasksController;

        const {
            tasksCompleted,
            tasksNotCompleted,
            isLoadingTasksCompleted,
            isLoadingTasksNotCompleted,
            loadTaskDetails,
        } = useMemo(() => new CustomTaskDetailsByCategoryController(), []);

        const hasComplete = !isEmpty(tasksCompleted);
        const isAllComplete =
            !isEmpty(tasksCompleted) && isEmpty(tasksNotCompleted);

        useEffect(() => {
            loadTaskDetails(Number(taskYear), month, type, ownerId);
        }, [loadTaskDetails, month, ownerId, taskYear, type]);

        if (isLoadingTasksNotCompleted || isLoadingTasksCompleted) {
            return <Loader isSpinnerOnly label={t`Loading...`} />;
        }

        if (isAllComplete) {
            return (
                <SimpleTable
                    caption={caption}
                    columns={columns}
                    data={toJS(tasksCompleted)}
                    options={{
                        shouldHideHeader: true,
                    }}
                />
            );
        }

        return (
            <Stack
                direction="column"
                data-testid="TasksListBody"
                data-id="pGug6fJm"
            >
                <ShowMoreTasksSection
                    caption={caption}
                    columns={columns}
                    incompleteTasks={toJS(tasksNotCompleted)}
                />
                {hasComplete && (
                    <CompletedTasksSection
                        completedCount={toJS(tasksCompleted.length)}
                    >
                        <SimpleTable
                            caption={caption}
                            columns={columns}
                            data={toJS(tasksCompleted)}
                            options={{
                                shouldHideHeader: true,
                            }}
                        />
                    </CompletedTasksSection>
                )}
            </Stack>
        );
    },
);
