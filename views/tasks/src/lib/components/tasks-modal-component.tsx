import { modalController } from '@controllers/modal';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { TASKS_MODAL_ID } from '../constants/tasks.constants';
import { TaskFormComponent } from './tasks-form-component';

export const TasksModalComponent = (): React.JSX.Element => {
    return (
        <>
            <Modal.Header
                title={t`Create task`}
                closeButtonAriaLabel="Close create auditor"
                onClose={() => {
                    modalController.closeModal(TASKS_MODAL_ID);
                }}
            />
            <Modal.Body>
                <TaskFormComponent />
            </Modal.Body>
            <Modal.Footer
                leftActionStack={[
                    {
                        label: t`Delete task`,
                        level: 'tertiary',
                        colorScheme: 'danger',
                        onClick: () => {
                            alert('Delete clicked');
                        },
                    },
                ]}
                rightActionStack={[
                    {
                        label: t`Close`,
                        level: 'secondary',
                        onClick: () => {
                            modalController.closeModal(TASKS_MODAL_ID);
                        },
                    },
                    {
                        label: t`Save`,
                        level: 'primary',
                        colorScheme: 'primary',
                        onClick: () => {
                            alert('Save clicked');
                        },
                    },
                ]}
            />
        </>
    );
};
