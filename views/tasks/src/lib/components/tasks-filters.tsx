import { constant } from 'lodash-es';
import { useCallback, useEffect, useMemo } from 'react';
import { TasksLink } from '@components/tasks-link';
import {
    sharedCustomTasksController,
    sharedCustomTasksUsersController,
} from '@controllers/tasks';
import { Box } from '@cosmos/components/box';
import { ComboboxField } from '@cosmos/components/combobox-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { SelectField } from '@cosmos/components/select-field';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useQueryParams } from '@globals/use-query-params';
import { getMonthLabel } from '@helpers/date-time';
import { TASK_MONTH_PARAM } from '../constants/tasks.constants';
import {
    getTaskTypeOptions,
    type TASK_TYPES_VALUES,
} from '../helpers/tasks.helpers';
import { getTasksMonthLinks } from '../helpers/tasks-month-links.helper';

const TASKS_FILTER_ID = 'tasks-filter';

export const TasksFilters = observer(() => {
    const { params: parsedQueryParams, updateParams } = useQueryParams();
    const TASKS_TYPE_OPTIONS = useMemo(() => getTaskTypeOptions(), []);

    const {
        tasksYearOptions: yearOptions,
        hasErrorInTasksYearRange,
        tasksOverview,
        hasErrorInTasksOverview,
        filteredYear,
        setFilters,
    } = sharedCustomTasksController;

    const {
        customTasksUsers,
        hasNextPage: hasMoreUsers,
        isFetching: isFetchingUsers,
        isLoading: isLoadingUsers,
        onFetchUsers,
    } = sharedCustomTasksUsersController;

    const defaultUser = useMemo(
        () =>
            customTasksUsers.find(
                (user) => user.id === parsedQueryParams.ownerId,
            ),
        [customTasksUsers, parsedQueryParams.ownerId],
    );

    const defaultTypes = useMemo(() => {
        const types = parsedQueryParams.taskTypes;

        if (!Array.isArray(types)) {
            return undefined;
        }

        return TASKS_TYPE_OPTIONS.filter((option) =>
            types.includes(option.value),
        );
    }, [TASKS_TYPE_OPTIONS, parsedQueryParams.taskTypes]);

    const defaultYear = useMemo(() => {
        return yearOptions.find(
            (option) => option.value === String(filteredYear),
        );
    }, [yearOptions, filteredYear]);

    const handleTypeChange = useCallback(
        (value: ListBoxItemData[] | ListBoxItemData) => {
            if (!Array.isArray(value)) {
                return;
            }

            const types = value.map(
                (item) => item.value,
            ) as typeof TASK_TYPES_VALUES;

            updateParams({ taskTypes: types });
            setFilters({
                types,
            });
        },
        [setFilters, updateParams],
    );

    const handleUserChange = useCallback(
        (value: ListBoxItemData | ListBoxItemData[]) => {
            if (Array.isArray(value)) {
                return;
            }

            const ownerId = value.id ? Number(value.id) : undefined;

            updateParams({ ownerId });
            setFilters({
                ownerId,
            });
        },
        [setFilters, updateParams],
    );

    const handleYearChange = useCallback(
        (year: ListBoxItemData) => {
            updateParams({ taskYear: year.value });
            setFilters({
                year: Number(year.value),
            });
        },
        [setFilters, updateParams],
    );

    useEffect(() => {
        onFetchUsers();
    }, [onFetchUsers]);

    const showMonth = useCallback(
        (month: number) => updateParams({ [TASK_MONTH_PARAM]: month }),
        [updateParams],
    );

    return (
        <Box
            borderRadius="borderRadiusLg"
            borderWidth="borderWidthSm"
            borderColor="neutralBorderFaded"
            overflow="auto"
            data-id="QiIlFBGG"
        >
            <Stack direction="column" p="xl" gap="xl">
                <ComboboxField
                    isMultiSelect
                    formId={TASKS_FILTER_ID}
                    name="TYPE"
                    label={t`Type`}
                    placeholder={t`All types`}
                    loaderLabel={t`Loading...`}
                    removeAllSelectedItemsLabel={t`Remove all`}
                    data-id={`${TASKS_FILTER_ID}-typeField`}
                    options={TASKS_TYPE_OPTIONS}
                    defaultSelectedOptions={defaultTypes}
                    getSearchEmptyState={constant(t`No types found`)}
                    getRemoveIndividualSelectedItemClickLabel={({
                        itemLabel,
                    }) => t`Remove ${itemLabel}`}
                    onChange={handleTypeChange}
                />

                <ComboboxField
                    formId={TASKS_FILTER_ID}
                    name="OWNER"
                    label={t`Owner`}
                    placeholder={t`All owners`}
                    loaderLabel={t`Loading personnel...`}
                    removeAllSelectedItemsLabel={undefined}
                    data-id={`${TASKS_FILTER_ID}-ownerField`}
                    defaultValue={defaultUser}
                    options={customTasksUsers}
                    clearSelectedItemButtonLabel={t`Clear`}
                    hasMore={hasMoreUsers}
                    isLoading={isFetchingUsers && isLoadingUsers}
                    getSearchEmptyState={constant(t`No personnel found`)}
                    onFetchOptions={onFetchUsers}
                    onChange={handleUserChange}
                />

                {!hasErrorInTasksYearRange && (
                    <SelectField
                        formId={TASKS_FILTER_ID}
                        name="YEAR"
                        label={t`Year`}
                        loaderLabel={t`Loading...`}
                        value={undefined}
                        options={yearOptions}
                        defaultValue={defaultYear}
                        onChange={handleYearChange}
                    />
                )}

                {!hasErrorInTasksOverview && (
                    <Stack direction="column">
                        {getTasksMonthLinks(tasksOverview).map(
                            ({ month, count, overdueCount }) => (
                                <TasksLink
                                    key={month}
                                    label={getMonthLabel(month)}
                                    count={count}
                                    overdueCount={overdueCount}
                                    data-id="Sb3v_kRv"
                                    onClick={() => showMonth(month)}
                                />
                            ),
                        )}
                    </Stack>
                )}
            </Stack>
        </Box>
    );
});
