import { useState } from 'react';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

interface CompletedTasksSectionProps {
    completedCount: number;
    children: React.JSX.Element;
}

export const CompletedTasksSection = observer(
    ({
        completedCount,
        children,
    }: CompletedTasksSectionProps): React.JSX.Element => {
        const [isVisible, setIsVisible] = useState(true);

        return (
            <>
                <Stack gap="md" align="center" pt="md">
                    <Text type="title" size="300">
                        {completedCount} {t`completed tasks`}
                    </Text>
                    <Button
                        label={isVisible ? t`Hide` : t`Show`}
                        level="tertiary"
                        size="md"
                        aria-expanded={isVisible}
                        aria-controls="completed-tasks-content"
                        onClick={() => {
                            setIsVisible(!isVisible);
                        }}
                    />
                </Stack>
                {isVisible && children}
            </>
        );
    },
);
