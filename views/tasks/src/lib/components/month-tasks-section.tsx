import { isNil } from 'lodash-es';
import {
    getCategoriesOverview,
    sharedCustomTasksController,
    TASK_ACCORDION_TYPES,
} from '@controllers/tasks';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { TasksHeadline } from './tasks-headline';
import { TasksListAccordion } from './tasks-list-accordion';

export const MonthTasksSection = observer((): React.JSX.Element => {
    const {
        tasksOverview,
        isLoadingTasksOverview,
        hasErrorInTasksOverview,
        filteredYear: taskYear,
    } = sharedCustomTasksController;

    if (isLoadingTasksOverview) {
        return <Loader isSpinnerOnly label={t`Loading...`} />;
    }

    if (hasErrorInTasksOverview) {
        console.error('There was an error loading tasks');

        // TODO: handle errors at https://drata.atlassian.net/browse/ENG-68601
        return <>{null}</>;
    }

    return (
        <>
            {Array.from({ length: 12 }, (_, index) => index).map((index) => {
                const categories = getCategoriesOverview(
                    tasksOverview.find((month) => month.month === index + 1),
                );

                return (
                    <Stack
                        key={index}
                        direction="column"
                        gap="lg"
                        p="sm"
                        data-id="aKuv6e5w"
                    >
                        <TasksHeadline
                            month={index + 1}
                            year={Number(taskYear)}
                        />

                        {TASK_ACCORDION_TYPES.map((type) => {
                            const taskCategory = categories.find(
                                (category) => category.type === type,
                            );

                            if (isNil(taskCategory)) {
                                return null;
                            }

                            return (
                                <TasksListAccordion
                                    month={index + 1}
                                    key={type}
                                    data-id="ZAhYLzwO"
                                    taskCategory={taskCategory}
                                />
                            );
                        })}
                    </Stack>
                );
            })}
        </>
    );
});
