import { noop } from 'lodash-es';
import { observer } from '@globals/mobx';
import { TaskFormModel } from '@models/tasks';
import { Form } from '@ui/forms';
import { TASKS_FORM_ID } from '../constants/tasks.constants';

export const TaskFormComponent = observer((): React.JSX.Element => {
    const { schema } = new TaskFormModel();

    return (
        <div data-id="test" data-testid="TaskFormComponent">
            <Form
                hasExternalSubmitButton
                data-id={TASKS_FORM_ID}
                formId={TASKS_FORM_ID}
                schema={schema}
                onSubmit={noop}
            />
        </div>
    );
});
