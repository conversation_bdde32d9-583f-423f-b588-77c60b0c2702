import { isEmpty, isNumber } from 'lodash-es';
import { useMemo } from 'react';
import type { TaskCategory } from '@controllers/tasks';
import { Accordion } from '@cosmos/components/accordion';
import { Box } from '@cosmos/components/box';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { dimension64x } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getAccordionConfig } from '../helpers/get-accordion-config.helper';
import { getCategoryTitleSuffix } from '../helpers/get-category-title-suffix.helper';
import { getFeedbackMessage } from '../helpers/get-feedback-message.helper';
import { TasksListBody } from './tasks-list-body';

export interface TasksListAccordionProps {
    month: number;
    taskCategory: TaskCategory;
}

export const TasksListAccordion = observer(
    ({ month, taskCategory }: TasksListAccordionProps): React.JSX.Element => {
        const { type, severity, minDate, categoryOverdue } = taskCategory;
        const { title, columns } = getAccordionConfig(type);
        const titleSuffix = getCategoryTitleSuffix(taskCategory);
        const feedbackTitle = getFeedbackMessage(severity, minDate);

        const caption = useMemo(() => {
            const lowercaseTitle = title.toLowerCase();

            return t`You can find the ${lowercaseTitle} tasks on the table below`;
        }, [title]);

        return (
            <>
                {!isEmpty(taskCategory) && (
                    <Box
                        minWidth={`calc(3 * ${dimension64x})`}
                        data-id="sxnegg7d"
                    >
                        <Accordion
                            data-testid="TasksListAccordion"
                            data-id={`TasksListAccordion-${type}`}
                            title={
                                isEmpty(titleSuffix)
                                    ? title
                                    : `${title} ${titleSuffix}`
                            }
                            supportingContent={
                                severity === 'neutral' ? (
                                    <Text type="title" size="100">
                                        {feedbackTitle}
                                    </Text>
                                ) : (
                                    <Stack gap="md" align="center">
                                        {isNumber(categoryOverdue) &&
                                            categoryOverdue > 0 && (
                                                <Metadata
                                                    type="number"
                                                    colorScheme={severity}
                                                    label={String(
                                                        categoryOverdue,
                                                    )}
                                                />
                                            )}
                                    </Stack>
                                )
                            }
                            body={
                                <TasksListBody
                                    month={month}
                                    type={type}
                                    columns={columns}
                                    caption={caption}
                                />
                            }
                        />
                    </Box>
                )}
            </>
        );
    },
);
