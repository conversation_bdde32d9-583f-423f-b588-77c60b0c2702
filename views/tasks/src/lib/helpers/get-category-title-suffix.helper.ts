import type { TaskCategory } from '@controllers/tasks';
import { t } from '@globals/i18n/macro';

/**
 * Determines the title suffix for a task category based on its status.
 * Complete or overdue status show no open tasks count label
 * complete and overdue tasks in category no open tasks count label
 * overdue and upcoming then show open tasks count label.
 *
 * @param category - The task category object.
 * @returns A string with the open tasks count or null if all tasks are completed or overdue.
 */
export const getCategoryTitleSuffix = (
    category: TaskCategory,
): string | null => {
    const isCompleteStatus = category.severity === 'success';
    const remainingTasks =
        category.count -
        (category.categoryCompleted ?? 0) -
        (category.categoryOverdue ?? 0);

    const isAllTasksOverdue =
        category.severity === 'critical' &&
        category.categoryOverdue === category.count;

    const isAllTasksCompletedOrOverdue =
        (category.categoryCompleted ?? 0) + (category.categoryOverdue ?? 0) ===
        category.count;

    const isCompletedOrOverdue =
        isCompleteStatus || isAllTasksOverdue || isAllTasksCompletedOrOverdue;

    if (isCompletedOrOverdue) {
        return null;
    }

    return t`(${remainingTasks} open tasks)`;
};
