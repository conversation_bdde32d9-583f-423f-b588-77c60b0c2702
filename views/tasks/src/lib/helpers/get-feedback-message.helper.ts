import type { ComponentProps } from 'react';
import type { Feedback } from '@cosmos/components/feedback';
import { t } from '@globals/i18n/macro';
import { formatDate } from '@helpers/date-time';

export type TaskCategorySeverity = ComponentProps<typeof Feedback>['severity'];

/**
 * Generates a feedback message based on the task category severity and minimum date.
 *
 * @param severity - The severity level of the task category.
 * @param minDate - The minimum date of tasks in the category.
 * @returns A string message describing the status of the tasks.
 */
export const getFeedbackMessage = (
    severity: TaskCategorySeverity,
    minDate?: string | null,
): string => {
    if (severity === 'critical') {
        if (!minDate) {
            return '';
        }
        const overdue = formatDate('overdue', minDate);

        return t`Past due ${overdue}`;
    }
    if (severity === 'neutral') {
        if (!minDate) {
            return '';
        }
        const nextTaskDue = formatDate('field', minDate);

        return t`Next task due ${nextTaskDue}`;
    }

    return 'All Done';
};
