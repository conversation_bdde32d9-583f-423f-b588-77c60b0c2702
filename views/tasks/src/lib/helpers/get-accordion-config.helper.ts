import type { ComponentProps } from 'react';
import type { TaskAccordionType } from '@controllers/tasks';
import type { SimpleTable } from '@cosmos-lab/components/simple-table';
import { t } from '@globals/i18n/macro';
import {
    getRequiredApprovalsTableColumns,
    getTaskTableColumns,
} from './tasks.helpers';

export const getAccordionConfig = (
    type: TaskAccordionType,
): {
    title: string;
    columns: ComponentProps<typeof SimpleTable>['columns'];
} => {
    const TASKS_TABLE_COLUMNS = getTaskTableColumns();
    const REQUIRED_APPROVALS_TABLE_COLUMNS = getRequiredApprovalsTableColumns();

    switch (type) {
        case 'evidence': {
            return {
                title: t`Evidence renewals`,
                columns: TASKS_TABLE_COLUMNS,
            };
        }
        case 'policyRenewals': {
            return {
                title: t`Policy renewals`,
                columns: TASKS_TABLE_COLUMNS,
            };
        }
        case 'vendor': {
            return {
                title: t`Vendors reminders`,
                columns: TASKS_TABLE_COLUMNS,
            };
        }
        case 'general': {
            return {
                title: t`General`,
                columns: TASKS_TABLE_COLUMNS,
            };
        }
        case 'control': {
            return {
                title: t`Controls`,
                columns: TASKS_TABLE_COLUMNS,
            };
        }
        case 'controlApprovals': {
            return {
                title: t`Required control approvals`,
                columns: REQUIRED_APPROVALS_TABLE_COLUMNS,
            };
        }
        case 'policyApprovals': {
            return {
                title: t`Required policy approvals`,
                columns: REQUIRED_APPROVALS_TABLE_COLUMNS,
            };
        }
        case 'risk': {
            return {
                title: t`Risk management`,
                columns: TASKS_TABLE_COLUMNS,
            };
        }

        default: {
            throw new Error(`Invalid type: ${String(type)}`);
        }
    }
};
