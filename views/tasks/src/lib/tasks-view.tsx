import { sharedCustomTasksController } from '@controllers/tasks';
import { Grid } from '@cosmos/components/grid';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { dimension56x, dimension64x } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { MonthTasksSection } from './components/month-tasks-section';
import { TasksFilters } from './components/tasks-filters';

export const TasksView = observer(() => {
    const topLayOutHeight = dimension64x;
    const calculatedGridHeight = `calc(100vh - ${topLayOutHeight})`;

    const {
        hasErrorInTasksYearRange,
        isLoadingYearRange,
        hasErrorInTasksOverview,
        isLoadingTasksOverview,
    } = sharedCustomTasksController;

    // TODO: handle errors at https://drata.atlassian.net/browse/ENG-68601
    if (hasErrorInTasksYearRange || hasErrorInTasksOverview) {
        console.error('There was an error loading tasks');
    }

    return (
        <>
            {isLoadingYearRange || isLoadingTasksOverview ? (
                <Loader isSpinnerOnly label={t`Loading...`} />
            ) : (
                <Grid
                    width="100%"
                    height={calculatedGridHeight}
                    columns={`${dimension56x} 1fr`}
                    gap="3xl"
                    px="3xl"
                    pt="3xl"
                    data-id="BAANWvoa"
                >
                    <Stack height={calculatedGridHeight} overflow="auto">
                        <TasksFilters />
                    </Stack>
                    <Stack
                        height={calculatedGridHeight}
                        overflow="auto"
                        direction="column"
                        gap="lg"
                        data-id="gp6LyaUG"
                    >
                        <MonthTasksSection />
                    </Stack>
                </Grid>
            )}
        </>
    );
});
