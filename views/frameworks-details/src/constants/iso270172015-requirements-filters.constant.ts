import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

export function getISO270172015RequirementsFilters(): FilterProps {
    const filterLabel = t`Clause`;

    return createRequirementFilters([
        createCategoryFilter(
            [
                {
                    label: t`Information Security Policies`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .ISO_INFORMATION_SECURITY_POLICIES
                    ],
                },
                {
                    label: t`Cryptography`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.ISO_CRYPTOGRAPHY
                    ],
                },
                {
                    label: t`Supplier Relationships`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.ISO_SUPPLIER_RELATIONSHIPS
                    ],
                },
                {
                    label: t`Organization of Information Security`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .ISO_ORGANIZATION_OF_INFORMATION_SECURITY
                    ],
                },
                {
                    label: t`Physical and Environmental Security`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .ISO_PHYSICAL_AND_ENVIRONMENTAL_SECURITY
                    ],
                },
                {
                    label: t`Information Security Incident Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .ISO_INFORMATION_SECURITY_INCIDENT_MANAGEMENT
                    ],
                },
                {
                    label: t`Human Resources Security`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.ISO_HUMAN_RESOURCES_SECURITY
                    ],
                },
                {
                    label: t`Operations Security`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.ISO_OPERATIONS_SECURITY
                    ],
                },
                {
                    label: t`Compliance`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.ISO_COMPLIANCE
                    ],
                },
                {
                    label: t`Asset Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.ISO_ASSET_MANAGEMENT
                    ],
                },
                {
                    label: t`Communications Security`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.ISO_COMMUNICATIONS_SECURITY
                    ],
                },
                {
                    label: t`Access Control`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.ISO_ACCESS_CONTROL
                    ],
                },
                {
                    label: t`System Acquisition, Development and Maintenance`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .ISO_SYSTEM_ACQUISITION_DEVELOPMENT_AND_MAINTENANCE
                    ],
                },
            ],
            filterLabel,
        ),
    ]);
}
