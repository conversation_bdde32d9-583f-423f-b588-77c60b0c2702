import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

export function getCOBITRequirementsFilters(): FilterProps {
    const filterLabel = t`Domain`;

    return createRequirementFilters([
        createCategoryFilter(
            [
                {
                    label: t`Evaluate, Direct and Monitor`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .COBIT_EVALUATE_DIRECT_AND_MONITOR
                    ],
                },
                {
                    label: t`Align, Plan and Organize`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.COBIT_ALIGN_PLAN_AND_ORGANIZE
                    ],
                },
                {
                    label: t`Build, Acquire and Implement`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .COBIT_BUILD_ACQUIRE_AND_IMPLEMENT
                    ],
                },
                {
                    label: t`Deliver, Service and Support`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .COBIT_DELIVER_SERVICE_AND_SUPPORT
                    ],
                },
                {
                    label: t`Monitor, Evaluate and Assess`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .COBIT_MONITOR_EVALUATE_AND_ASSESS
                    ],
                },
            ],
            filterLabel,
        ),
    ]);
}
