import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

export function getNISTCSFRequirementsFilters(): FilterProps {
    const filterLabel = t`Function`;

    return createRequirementFilters([
        createCategoryFilter(
            [
                {
                    label: t`Identify`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NISTCSF_IDENTIFY
                    ],
                },
                {
                    label: t`Protect`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NISTCSF_PROTECT
                    ],
                },
                {
                    label: t`Detect`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NISTCSF_DETECT
                    ],
                },
                {
                    label: t`Respond`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NISTCSF_RESPOND
                    ],
                },
                {
                    label: t`Recover`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NISTCSF_RECOVER
                    ],
                },
            ],
            filterLabel,
        ),
    ]);
}
