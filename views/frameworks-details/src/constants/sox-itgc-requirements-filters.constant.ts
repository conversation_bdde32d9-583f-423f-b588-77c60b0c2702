import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

export function getSOXITGCRequirementsFilters(): FilterProps {
    const filterLabel = t`Control objectives`;

    return createRequirementFilters([
        createCategoryFilter(
            [
                {
                    label: t`Program Development`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.SOX_ITGC_PROGRAM_DEVELOPMENT
                    ],
                },
                {
                    label: t`Change Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.SOX_ITGC_CHANGE_MANAGEMENT
                    ],
                },
                {
                    label: t`System Operations`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.SOX_ITGC_SYSTEM_OPERATIONS
                    ],
                },
                {
                    label: t`Access Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.SOX_ITGC_ACCESS_MANAGEMENT
                    ],
                },
            ],
            filterLabel,
        ),
    ]);
}
