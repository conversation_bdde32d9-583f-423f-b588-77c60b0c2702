import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

export function getPCIRequirementsFilters(): FilterProps {
    const filterLabel = t`Requirements`;

    return createRequirementFilters([
        createCategoryFilter(
            [
                {
                    label: t`Firewall`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI_FIREWALL
                    ],
                },
                {
                    label: t`Passwords`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI_PASSWORDS
                    ],
                },
                {
                    label: t`Data at Rest Protection`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI_DATA_AT_REST_PROTECTION
                    ],
                },
                {
                    label: t`Data in Transit Encryption`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI_DATA_IN_TRANSIT_ENCRYPTION
                    ],
                },
                {
                    label: t`Malware Protection`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI_MALWARE_PROTECTION
                    ],
                },
                {
                    label: t`Secure System Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI_SECURE_SYSTEM_MANAGEMENT
                    ],
                },
                {
                    label: t`Access Restriction`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI_ACCESS_RESTRICTION
                    ],
                },
                {
                    label: t`System Access Control`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI_SYSTEM_ACCESS_CONTROL
                    ],
                },
                {
                    label: t`Physical Access Control`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI_PHYSICAL_ACCESS_CONTROL
                    ],
                },
                {
                    label: t`Network Access Monitoring`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI_NETWORK_ACCESS_MONITORING
                    ],
                },
                {
                    label: t`Vulnerability Testing`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI_VULNERABILITY_TESTING
                    ],
                },
                {
                    label: t`Information Security Policy`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.PCI_INFORMATION_SECURITY_POLICY
                    ],
                },
            ],
            filterLabel,
        ),
    ]);
}
