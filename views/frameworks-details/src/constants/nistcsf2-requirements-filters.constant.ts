import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

export function getNISTCSF2RequirementsFilters(): FilterProps {
    const filterLabel = t`Function`;

    return createRequirementFilters([
        createCategoryFilter(
            [
                {
                    label: t`Govern`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NISTCSF2_GOVERN_GV
                    ],
                },
                {
                    label: t`Identify`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NISTCSF2_IDENTIFY_ID
                    ],
                },
                {
                    label: t`Protect`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NISTCSF2_PROTECT_PR
                    ],
                },
                {
                    label: t`Detect`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NISTCSF2_DETECT_DE
                    ],
                },
                {
                    label: t`Respond`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NISTCSF2_RESPOND_RS
                    ],
                },
                {
                    label: t`Recover`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NISTCSF2_RECOVER_RC
                    ],
                },
            ],
            filterLabel,
        ),
    ]);
}
