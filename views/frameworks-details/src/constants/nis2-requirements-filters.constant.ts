import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

export function getNIS2RequirementsFilters(): FilterProps {
    const filterLabel = t`Section`;

    return createRequirementFilters([
        createCategoryFilter(
            [
                {
                    label: t`Governance`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NIS2_GOVERNANCE
                    ],
                },
                {
                    label: t`Risk Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NIS2_RISK_MANAGEMENT
                    ],
                },
                {
                    label: t`Reporting`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NIS2_REPORTING
                    ],
                },
            ],
            filterLabel,
        ),
    ]);
}
