import { isEmpty } from 'lodash-es';
import type { FilterProps } from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import {
    RequirementIndexCategory,
    RequirementIndexSubcategory,
    RequirementIndexTag,
} from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

/**
 * Gets category options for DORA.
 */
function getDORACategoryOptions() {
    return [
        {
            label: t`Regulation`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.DORA_REGULATION
            ],
        },
        {
            label: t`ICT RMF (RTS)`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.DORA_ICT_RMF_RTS
            ],
        },
    ];
}

/**
 * Gets subcategory options for a specific DORA category.
 */
function getDORASubcategoryOptions(category: RequirementIndexCategory) {
    switch (category) {
        case RequirementIndexCategory.DORA_REGULATION: {
            return [
                {
                    label: t`ICT Risk Management`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.DORA_ICT_RISK_MANAGEMENT
                    ],
                },
                {
                    label: t`ICT-Related Incident Management`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .DORA_ICT_RELATED_INCIDENT_MANAGEMENT
                    ],
                },
                {
                    label: t`Digital Operational Resilience Testing`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .DORA_DIGITAL_OPERATIONAL_RESILIENCE_TESTING
                    ],
                },
                {
                    label: t`ICT Third-Party Risk Management`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .DORA_ICT_THIRD_PARTY_RISK_MANAGEMENT
                    ],
                },
                {
                    label: t`Information Sharing Arrangements`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .DORA_INFORMATION_SHARING_ARRANGEMENTS
                    ],
                },
            ];
        }
        case RequirementIndexCategory.DORA_ICT_RMF_RTS: {
            return [
                {
                    label: t`ICT Security Policies and Procedures`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .DORA_ICT_SECURITY_POLICIES_AND_PROCEDURES
                    ],
                },
                {
                    label: t`Human Resources Policy and Access Control`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .DORA_HUMAN_RESOURCES_POLICY_AND_ACCESS_CONTROL
                    ],
                },
                {
                    label: t`ICT-Related Incident Detection and Response`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .DORA_ICT_RELATED_INCIDENT_DETECTION_AND_RESPONSE
                    ],
                },
                {
                    label: t`ICT Business Continuity Management`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .DORA_ICT_BUSINESS_CONTINUITY_MANAGEMENT
                    ],
                },
                {
                    label: t`ICT Risk Management Framework Review Report`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .DORA_ICT_RISK_MANAGEMENT_FRAMEWORK_REVIEW_REPORT
                    ],
                },
                {
                    label: t`Simplified ICT Risk Management Framework`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .DORA_SIMPLIFIED_ICT_RISK_MANAGEMENT_FRAMEWORK
                    ],
                },
            ];
        }
        default: {
            return [];
        }
    }
}

/**
 * Creates a level filter for DORA.
 */
function createLevelFilter(): Filter {
    return {
        filterType: 'radio',
        id: 'level',
        label: t`Level`,
        options: [
            {
                label: t`Standard`,
                value: RequirementIndexTag[RequirementIndexTag.STANDARD],
            },
            {
                label: t`Simplified`,
                value: RequirementIndexTag[RequirementIndexTag.SIMPLIFIED],
            },
        ],
    };
}

/**
 * Creates a subcategory filter based on the selected category.
 *
 * @param selectedCategory - The selected category value to determine subcategory options.
 * @returns Subcategory filter object or null if no category is selected.
 */
function createSubcategoryFilter(selectedCategory?: string): Filter | null {
    if (!selectedCategory) {
        return null;
    }

    const categoryEnum = RequirementIndexCategory[
        selectedCategory as keyof typeof RequirementIndexCategory
    ] as RequirementIndexCategory;

    const options = getDORASubcategoryOptions(categoryEnum);

    if (isEmpty(options)) {
        return null;
    }

    // Find the category label from the options
    const categoryOption = getDORACategoryOptions().find(
        (option) => option.value === selectedCategory,
    );

    return {
        filterType: 'radio',
        id: `subcategory-${selectedCategory}`,
        label: categoryOption?.label || t`Subcategory`,
        options,
    };
}

/**
 * Gets DORA requirements filters with optional dynamic subcategory filter.
 *
 * @param selectedCategory - Optional selected category to show relevant subcategories.
 * @returns FilterProps configuration for DORA requirements.
 */
export function getDORARequirementsFilters(
    selectedCategory?: string,
): FilterProps {
    const filterLabel = t`Code`;

    const filters: Filter[] = [
        createLevelFilter(),
        createCategoryFilter(getDORACategoryOptions(), filterLabel),
    ];

    // Add subcategory filter if a category is selected
    const subcategoryFilter = createSubcategoryFilter(selectedCategory);

    if (subcategoryFilter) {
        filters.push(subcategoryFilter);
    }

    return createRequirementFilters(filters);
}
