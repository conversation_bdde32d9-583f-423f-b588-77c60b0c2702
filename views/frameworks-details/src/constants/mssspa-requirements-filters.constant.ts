import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

export function getMSSSPARequirementsFilters(): FilterProps {
    const filterLabel = t`Section`;

    return createRequirementFilters([
        createCategoryFilter(
            [
                {
                    label: t`Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.MSSSPA_MANAGEMENT
                    ],
                },
                {
                    label: t`Notice`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.MSSSPA_NOTICE
                    ],
                },
                {
                    label: t`Choice and Consent`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.MSSSPA_CHOICE_AND_CONSENT
                    ],
                },
                {
                    label: t`Collection`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.MSSSPA_COLLECTION
                    ],
                },
                {
                    label: t`Retention`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.MSSSPA_RETENTION
                    ],
                },
                {
                    label: t`Data Subjects`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.MSSSPA_DATA_SUBJECTS
                    ],
                },
                {
                    label: t`Disclosure to Third Parties`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .MSSSPA_DISCLOSURE_TO_THIRD_PARTIES
                    ],
                },
                {
                    label: t`Quality`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.MSSSPA_QUALITY
                    ],
                },
                {
                    label: t`Monitoring and Enforcement`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .MSSSPA_MONITORING_AND_ENFORCEMENT
                    ],
                },
                {
                    label: t`Security`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.MSSSPA_SECURITY
                    ],
                },
            ],
            filterLabel,
        ),
    ]);
}
