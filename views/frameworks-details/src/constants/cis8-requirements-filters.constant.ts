import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

export function getCIS8RequirementsFilters(): FilterProps {
    const filterLabel = t`Controls`;

    return createRequirementFilters([
        createCategoryFilter(
            [
                {
                    label: t`Access Control Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.CIS81_ACCESS_CONTROL_MANAGEMENT
                    ],
                },
                {
                    label: t`Account Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.CIS81_ACCOUNT_MANAGEMENT
                    ],
                },
                {
                    label: t`Application Software Security`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CIS81_APPLICATION_SOFTWARE_SECURITY
                    ],
                },
                {
                    label: t`Audit Log Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.CIS81_AUDIT_LOG_MANAGEMENT
                    ],
                },
                {
                    label: t`Continuous Vulnerability Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CIS81_CONTINUOUS_VULNERABILITY_MANAGEMENT
                    ],
                },
                {
                    label: t`Data Protection`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.CIS81_DATA_PROTECTION
                    ],
                },
                {
                    label: t`Incident Response Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CIS81_INCIDENT_RESPONSE_MANAGEMENT
                    ],
                },
                {
                    label: t`Inventory and Control of Enterprise Assets`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CIS81_INVENTORY_AND_CONTROL_OF_ENTERPRISE_ASSETS
                    ],
                },
            ],
            filterLabel,
        ),
    ]);
}
