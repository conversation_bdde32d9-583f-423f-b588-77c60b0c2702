import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

/**
 * Gets Drata Essentials category options with labels and values.
 */
function getDrataEssentialsCategoryOptions() {
    return [
        {
            label: t`Govern`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.DRATA_ESSENTIALS_GOVERN
            ],
        },
        {
            label: t`Identify`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.DRATA_ESSENTIALS_IDENTIFY
            ],
        },
        {
            label: t`Protect`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.DRATA_ESSENTIALS_PROTECT
            ],
        },
        {
            label: t`Detect`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.DRATA_ESSENTIALS_DETECT
            ],
        },
        {
            label: t`Respond`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.DRATA_ESSENTIALS_RESPOND
            ],
        },
        {
            label: t`Recover`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.DRATA_ESSENTIALS_RECOVER
            ],
        },
    ];
}

/**
 * Gets Drata Essentials requirements filters.
 *
 * @returns FilterProps configuration for Drata Essentials requirements.
 */
export function getDrataEssentialsRequirementsFilters(): FilterProps {
    const filterLabel = t`Function`;

    return createRequirementFilters([
        createCategoryFilter(getDrataEssentialsCategoryOptions(), filterLabel),
    ]);
}
