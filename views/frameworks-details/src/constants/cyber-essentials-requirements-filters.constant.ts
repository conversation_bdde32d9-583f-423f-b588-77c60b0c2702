import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

export function getCyberEssentialsRequirementsFilters(): FilterProps {
    const filterLabel = t`Themes`;

    return createRequirementFilters([
        createCategoryFilter(
            [
                {
                    label: t`Firewalls`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.CYBER_ESSENTIALS_FIREWALLS
                    ],
                },
                {
                    label: t`Secure Configuration - Computers and Network Devices`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CYBER_ESSENTIALS_SECURE_CONFIGURATION_COMPUTERS_AND_NETWORK_DEVICES
                    ],
                },
                {
                    label: t`Secure Configuration - Device Unlocking Credentials`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CYBER_ESSENTIALS_SECURE_CONFIGURATION_DEVICE_UNLOCKING_CREDENTIALS
                    ],
                },
                {
                    label: t`Security Update Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CYBER_ESSENTIALS_SECURITY_UPDATE_MANAGEMENT
                    ],
                },
                {
                    label: t`User Access Control`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CYBER_ESSENTIALS_USER_ACCESS_CONTROL
                    ],
                },
                {
                    label: t`Malware Protection`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CYBER_ESSENTIALS_MALWARE_PROTECTION
                    ],
                },
                {
                    label: t`Data Backup`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.CYBER_ESSENTIALS_DATA_BACKUP
                    ],
                },
                {
                    label: t`Asset Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CYBER_ESSENTIALS_ASSET_MANAGEMENT
                    ],
                },
                {
                    label: t`Vulnerability Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CYBER_ESSENTIALS_VULNERABILITY_MANAGEMENT
                    ],
                },
            ],
            filterLabel,
        ),
    ]);
}
