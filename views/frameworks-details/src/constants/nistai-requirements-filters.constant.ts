import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

export function getNISTAIRequirementsFilters(): FilterProps {
    const filterLabel = t`Themes`;

    return createRequirementFilters([
        createCategoryFilter(
            [
                {
                    label: t`Govern`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NISTAI_GOVERN
                    ],
                },
                {
                    label: t`Manage`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NISTAI_MANAGE
                    ],
                },
                {
                    label: t`Map`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NISTAI_MAP
                    ],
                },
                {
                    label: t`Measure`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.NISTAI_MEASURE
                    ],
                },
            ],
            filterLabel,
        ),
    ]);
}
