import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

export function getCCMRequirementsFilters(): FilterProps {
    const filterLabel = t`Domain`;

    return createRequirementFilters([
        createCategoryFilter(
            [
                {
                    label: t`Audit and Assurance`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.CCM_AUDIT_AND_ASSURANCE
                    ],
                },
                {
                    label: t`Application and Interface Security`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CCM_APPLICATION_AND_INTERFACE_SECURITY
                    ],
                },
                {
                    label: t`Business Continuity Management and Operational Resilience`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CCM_BUSINESS_CONTINUITY_MANAGEMENT_AND_OPERATIONAL_RESILIENCE
                    ],
                },
                {
                    label: t`Change Control and Configuration Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CCM_CHANGE_CONTROL_AND_CONFIGURATION_MANAGEMENT
                    ],
                },
                {
                    label: t`Cryptography, Encryption and Key Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CCM_CRYPTOGRAPHY_ENCRYPTION_AND_KEY_MANAGEMENT
                    ],
                },
                {
                    label: t`Datacenter Security`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.CCM_DATACENTER_SECURITY
                    ],
                },
                {
                    label: t`Data Security and Privacy Lifecycle Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CCM_DATA_SECURITY_AND_PRIVACY_LIFECYCLE_MANAGEMENT
                    ],
                },
                {
                    label: t`Governance, Risk and Compliance`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CCM_GOVERNANCE_RISK_AND_COMPLIANCE
                    ],
                },
                {
                    label: t`Human Resources`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.CCM_HUMAN_RESOURCES
                    ],
                },
                {
                    label: t`Identity and Access Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CCM_IDENTITY_AND_ACCESS_MANAGEMENT
                    ],
                },
                {
                    label: t`Interoperability and Portability`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CCM_INTEROPERABILITY_AND_PORTABILITY
                    ],
                },
                {
                    label: t`Infrastructure and Virtualization Security`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CCM_INFRASTRUCTURE_AND_VIRTUALIZATION_SECURITY
                    ],
                },
                {
                    label: t`Logging and Monitoring`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory.CCM_LOGGING_AND_MONITORING
                    ],
                },
                {
                    label: t`Security Incident Management, E-Discovery, and Cloud Forensics`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CCM_SECURITY_INCIDENT_MANAGEMENT_EDISCOVERY_AND_CLOUD_FORENSICS
                    ],
                },
                {
                    label: t`Supply Chain Management, Transparency, and Accountability`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CCM_SUPPLY_CHAIN_MANAGEMENT_TRANSPARENCY_AND_ACCOUNTABILITY
                    ],
                },
                {
                    label: t`Threat and Vulnerability Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CCM_THREAT_AND_VULNERABILITY_MANAGEMENT
                    ],
                },
                {
                    label: t`Universal Endpoint Management`,
                    value: RequirementIndexCategory[
                        RequirementIndexCategory
                            .CCM_UNIVERSAL_ENDPOINT_MANAGEMENT
                    ],
                },
            ],
            filterLabel,
        ),
    ]);
}
