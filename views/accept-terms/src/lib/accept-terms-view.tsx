import { styled } from 'styled-components';
import { z } from 'zod';
import { sharedAuthController } from '@controllers/auth';
import { Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { LogoLoader } from '@cosmos-lab/components/logo-loader';
import { sharedCurrentUserController } from '@globals/current-user';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Navigate } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { Form, FormWrapper } from '@ui/forms';

const FORM_ID = 'accept-terms-form';

export const AcceptTermsView = observer((): React.JSX.Element => {
    const { isAuditor, homePagePathname } = sharedCurrentUserController;

    const { isAcceptTermsPending, hasAcceptTermsError, hasAcceptTermsSuccess } =
        sharedAuthController;

    const handleSubmit = () => {
        sharedAuthController.acceptTermsAndConditions();
    };

    return (
        <Stack
            gap="6x"
            direction="column"
            data-testid="AcceptTermsView"
            data-id="LktK_-ee"
        >
            <Box>
                <Text type="subheadline" size="400" as="p">
                    <Trans>Welcome</Trans>
                </Text>
            </Box>
            <Stack gap="3x" direction="column">
                <Box>
                    <Text type="title" size="200" as="p">
                        <Trans>Please accept terms to continue</Trans>
                    </Text>
                </Box>
                <Box>
                    <Text type="body" size="200" as="p">
                        {isAuditor
                            ? t`Prior to using Drata for your Clients' compliance audit needs, please review and agree to the `
                            : t`To use Drata for your company's compliance needs, please review then agree to the `}
                        <Trans>
                            <AppLink isExternal href="https://drata.com/terms">
                                Terms of Service
                            </AppLink>
                            and{' '}
                            <AppLink
                                isExternal
                                href="https://drata.com/privacy"
                            >
                                Privacy Policy
                            </AppLink>
                        </Trans>
                    </Text>
                </Box>
            </Stack>

            <Form
                formId={FORM_ID}
                data-id="accept-terms-form"
                submitButtonLabel="Continue to"
                schema={{
                    acceptance: {
                        type: 'checkbox',
                        label: t`I agree to Drata’s Privacy Notice`,
                        initialValue: false,
                        validator: z.boolean().refine((val) => val, {
                            message: t`You must agree to the terms and conditions`,
                        }),
                    },
                }}
                onSubmit={handleSubmit}
            />

            <LogoLoader
                fadeOut={!isAcceptTermsPending}
                ariaLabel="Logging In"
            />
            {!isAcceptTermsPending &&
                !hasAcceptTermsError &&
                hasAcceptTermsSuccess && (
                    <Navigate
                        replace
                        data-testid="Redirect"
                        data-id="zcE3bjjJ"
                        to={homePagePathname}
                    />
                )}
        </Stack>
    );
});
