import {
    type ArtifactFormProps,
    ArtifactNewEvidenceForm,
} from '@components/evidence-library';
import { sharedUsersInfiniteController } from '@controllers/users';
import { ComboboxField } from '@cosmos/components/combobox-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { SelectField } from '@cosmos/components/select-field';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Divider } from '@cosmos-lab/components/divider';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedEvidenceLibraryAddEvidenceFormModel } from '../models/evidence-library-add-evidence-form.model';

interface EvidenceSourceStepProps {
    formId: string;
    formRef: React.RefObject<HTMLFormElement>;
}

export const EvidenceSourceStep = observer(
    ({ formId, formRef }: EvidenceSourceStepProps): React.JSX.Element => {
        const {
            storeEvidenceSourceStepValues,
            source,
            availableSources,
            isSourcesLoading,
            url,
            ticketUrl,
            file,
            creationDate,
            renewalDate,
            renewalFrequency,
            owner,
            resetFileArtifact,
            externalFile,
        } = sharedEvidenceLibraryAddEvidenceFormModel;

        const { options, hasNextPage, isFetching, isLoading, onFetchUsers } =
            sharedUsersInfiniteController;

        const handleOnChangeSource = (selectedSource: ListBoxItemData) => {
            sharedEvidenceLibraryAddEvidenceFormModel.setSource(selectedSource);
        };

        const handleOwnerChange = (
            selectedOwner: ListBoxItemData | ListBoxItemData[],
        ) => {
            storeEvidenceSourceStepValues({ owner: selectedOwner });
        };

        if (isSourcesLoading) {
            return <Skeleton barCount={10} />;
        }

        const currentOwnerValue = owner
            ? {
                  id: owner.id,
                  label: owner.fullName,
                  avatar: {
                      imgSrc: owner.avatarUrl,
                      fallbackText: owner.initials,
                  },
                  description: owner.email,
                  value: owner.id,
                  userData: owner.userData,
              }
            : undefined;

        return (
            <Stack
                direction="column"
                width="100%"
                data-testid="AddSources"
                data-id="BtebkL0h"
                gap="2xl"
            >
                <Stack direction="column" gap="2xl">
                    <Text size="400" type="subheadline">
                        <Trans>Add source</Trans>
                    </Text>
                    <SelectField
                        formId={formId}
                        label={t`Source`}
                        name="source"
                        options={availableSources}
                        loaderLabel={t`Loading...`}
                        value={source}
                        onChange={handleOnChangeSource}
                    />
                </Stack>
                {source.value === 'NONE' && (
                    <ComboboxField
                        formId={formId}
                        name="owner"
                        label={t`Owner`}
                        loaderLabel={t`Loading users...`}
                        placeholder={t`Search by name`}
                        isLoading={isFetching && isLoading}
                        getSearchEmptyState={() => t`No users found`}
                        options={options}
                        hasMore={hasNextPage}
                        defaultValue={currentOwnerValue}
                        onFetchOptions={onFetchUsers}
                        onChange={handleOwnerChange}
                    />
                )}
                {source.value !== 'NONE' && (
                    <>
                        <Divider />
                        <Stack direction="column" gap="2xl">
                            <Text size="400" type="subheadline">
                                <Trans>Add artifact</Trans>
                            </Text>
                            <ArtifactNewEvidenceForm
                                formId={formId}
                                formRef={formRef}
                                mode={source.value as ArtifactFormProps['mode']}
                                meta={{
                                    urlInitialValue: url,
                                    ticketUrlInitialValue: ticketUrl,
                                    fileInitialValue: file,
                                    creationDateInitialValue: creationDate,
                                    renewalDateInitialValue: renewalDate,
                                    renewalFrequencyInitialValue:
                                        renewalFrequency,
                                    ownerInitialValue: owner,
                                    onDelete: resetFileArtifact,
                                    externalFileInitialValue: externalFile,
                                }}
                                onSubmit={storeEvidenceSourceStepValues}
                            />
                        </Stack>
                    </>
                )}
            </Stack>
        );
    },
);
