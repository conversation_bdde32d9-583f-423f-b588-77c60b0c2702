import { isEmpty, isNil } from 'lodash-es';
import type { EvidenceOwner, ExternalFile } from '@components/evidence-library';
import { sharedConnectionsController } from '@controllers/connections';
import { sharedLinkControlsController } from '@controllers/evidence-library';
import type { UserListBoxItemData } from '@controllers/users';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import type { CosmosFileObject } from '@cosmos/components/file-upload';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { ControlListResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { getFullName, getInitials } from '@helpers/formatters';
import type { FormValues } from '@ui/forms';
import { getAvailableSourceOptions } from '@views/evidence-add-evidence';

export class EvidenceLibraryAddEvidenceFormModel {
    name = '';
    description = '';
    implementationGuidance = '';
    /**
     * TODO: Did my best to use the getSourceOptions() function, but it's not working. An unrelated unit test fails.
     * If anyone knows how to fix it, please let me know how to do it or remove the TODO by yourself.
     */
    source: ListBoxItemData = {
        id: 'source-option-1',
        label: 'File',
        value: 'NEW_EVIDENCE',
    };
    url = '';
    ticketUrl = '';
    file: CosmosFileObject[] = [];
    creationDate?: TDateISODate;
    renewalDate?: TDateISODate;
    /**
     * TODO: Did my best to use the RENEWAL_FREQUENCY_OPTIONS object, but it's not working. An unrelated unit test fails.
     * If anyone knows how to fix it, please let me know how to do it or remove the TODO by yourself.
     */
    renewalFrequency: ListBoxItemData = {
        id: 'renewal-frequency-option-1-year',
        label: '1 Year',
        value: 'ONE_YEAR',
    };
    owner?: EvidenceOwner;

    externalFile?: ExternalFile;

    constructor() {
        makeAutoObservable(this);
    }

    setSource = (source: ListBoxItemData): void => {
        this.source = source;
    };

    get isSourcesLoading(): boolean {
        return sharedConnectionsController.isLoading;
    }

    get availableSources(): ListBoxItemData[] {
        const { allConfiguredConnections, isLoading } =
            sharedConnectionsController;

        const { currentWorkspace } = sharedWorkspacesController;

        if (isLoading) {
            return getAvailableSourceOptions(false);
        }

        const hasJiraConnection = allConfiguredConnections.find(
            (connection) => connection.clientType === 'JIRA',
        );

        if (!hasJiraConnection) {
            return getAvailableSourceOptions(false);
        }

        const hasValidJiraConnectionOnCurrentWorkspace =
            hasJiraConnection.workspaces.some(
                (workspace) => workspace.id === currentWorkspace?.id,
            );

        return getAvailableSourceOptions(
            hasValidJiraConnectionOnCurrentWorkspace,
        );
    }

    get hasFile(): boolean {
        return !isEmpty(this.file) || !isEmpty(this.externalFile);
    }

    get controls(): ControlListResponseDto[] {
        return sharedLinkControlsController.selectedControls;
    }

    get shouldDisplayImpactOnControlReadinessModal(): boolean {
        return !isEmpty(this.controls) && this.source.value === 'NONE';
    }

    resetFormValues = (): void => {
        this.name = '';
        this.description = '';
        this.implementationGuidance = '';
        /**
         * TODO: Did my best to use the getSourceOptions() function, but it's not working. An unrelated unit test fails.
         * If anyone knows how to fix it, please let me know how to do it or remove the TODO by yourself.
         */
        this.source = {
            id: 'source-option-1',
            label: 'File',
            value: 'NEW_EVIDENCE',
        };
        this.url = '';
        this.ticketUrl = '';
        this.file = [];
        this.creationDate = undefined;
        this.renewalDate = undefined;
        /**
         * TODO: Did my best to use the RENEWAL_FREQUENCY_OPTIONS object, but it's not working. An unrelated unit test fails.
         * If anyone knows how to fix it, please let me know how to do it or remove the TODO by yourself.
         */
        this.renewalFrequency = {
            id: 'renewal-frequency-option-1-year',
            label: '1 Year',
            value: 'ONE_YEAR',
        };
        this.owner = undefined;
        this.externalFile = undefined;

        sharedLinkControlsController.removeAllControls();
    };

    resetFileArtifact = (): void => {
        this.file = [];
        this.creationDate = undefined;
        this.renewalDate = undefined;
        /**
         * TODO: Did my best to use the RENEWAL_FREQUENCY_OPTIONS object, but it's not working. An unrelated unit test fails.
         * If anyone knows how to fix it, please let me know how to do it or remove the TODO by yourself.
         */
        this.renewalFrequency = {
            id: 'renewal-frequency-option-1-year',
            label: '1 Year',
            value: 'ONE_YEAR',
        };
        this.owner = undefined;
        this.externalFile = undefined;
    };

    storeEvidenceDetailsStepValues = (values: FormValues): void => {
        this.name = values.name as string;
        this.description = values.description as string;
        this.implementationGuidance = values.implementationGuidance as string;
    };

    storeEvidenceSourceStepValues = (values: FormValues): void => {
        this.url = values.url as string;
        this.ticketUrl = values.ticketUrl as string;
        if (!isEmpty(values.file)) {
            this.file = [
                {
                    // Business rule: we only allow one file per artifact
                    file: (values.file as File[])[0],
                    errors: [],
                },
            ] as CosmosFileObject[];
        }

        if (!isNil(values.externalFile)) {
            this.externalFile = values.externalFile as ExternalFile;
        }

        const artifactDatesInput = values.artifactDatesInput as {
            creationDate: TDateISODate;
            renewalDate: TDateISODate;
            renewalFrequency: ListBoxItemData;
        };

        if (!isNil(artifactDatesInput)) {
            this.creationDate = artifactDatesInput.creationDate;
            this.renewalDate = artifactDatesInput.renewalDate;
            this.renewalFrequency = artifactDatesInput.renewalFrequency;
        }

        const ownerSelected = this.generateEvidenceOwner(
            values.owner as UserListBoxItemData,
        );

        this.owner = ownerSelected;
    };

    storeFileValue = (values: FormValues): void => {
        if (!isEmpty(values.file)) {
            this.file = [
                {
                    // Business rule: we only allow one file per artifact
                    file: (values.file as File[])[0],
                    errors: [],
                },
            ] as CosmosFileObject[];
        }
    };

    storeFileExternalValue = (values: FormValues): void => {
        if (!isEmpty(values.externalFile)) {
            this.externalFile = values.externalFile as ExternalFile;
        }
    };

    generateEvidenceOwner = (
        ownerSelected: UserListBoxItemData,
    ): EvidenceOwner => {
        const { userData } = ownerSelected;

        const fullName = getFullName(userData.firstName, userData.lastName);
        const initials = getInitials(fullName);

        return {
            email: userData.email,
            fullName,
            initials,
            avatarUrl: userData.avatarUrl ?? '',
            id: userData.id.toString(),
            userData,
        };
    };

    getFormValues = (): FormValues => {
        return {
            name: this.name,
            description: this.description,
            implementationGuidance: this.implementationGuidance,
            source: this.source.value,
            url: this.url,
            ticketUrl: this.ticketUrl,
            file: this.file[0]?.file,
            creationDate: this.creationDate,
            renewalDate: this.renewalDate,
            renewalFrequency: this.renewalFrequency.value,
            owner: this.owner,
            controls: this.controls,
            externalFile: this.externalFile,
        };
    };
}

export const sharedEvidenceLibraryAddEvidenceFormModel =
    new EvidenceLibraryAddEvidenceFormModel();
