import { sharedControlFrameworksController } from '@controllers/controls';
import { Datatable } from '@cosmos/components/datatable';
import type { RequirementWithControlsShortListResponseDto } from '@globals/api-sdk/types';
import { action, observer } from '@globals/mobx';
import {
    openRequirementDetailsPanel,
    RequirementDetailsPanelView,
} from '@views/requirement-details-panel';
import { CONTROLS_FRAMEWORKS_COLUMNS } from '../constants/columns.constant';
import { sharedControlsFrameworksViewModel } from './models/controls-frameworks-view.model';

const onRowClick = action(
    ({ row }: { row: RequirementWithControlsShortListResponseDto }) => {
        openRequirementDetailsPanel(row.id, row.frameworkSlug, () => (
            <RequirementDetailsPanelView data-id="_KIehYY" />
        ));
    },
);

export const ControlsFrameworksView = observer((): React.JSX.Element => {
    const {
        allControlFrameworks,
        total,
        isLoading,
        loadControlFrameworksPage,
    } = sharedControlFrameworksController;

    const { tableActions } = sharedControlsFrameworksViewModel;

    return (
        <Datatable
            isLoading={isLoading}
            tableId="datatable-controls-list"
            total={total}
            data={allControlFrameworks}
            columns={CONTROLS_FRAMEWORKS_COLUMNS}
            data-testid="ControlsFrameworksView"
            data-id="8KvKvrlG"
            tableActions={tableActions}
            onFetchData={loadControlFrameworksPage}
            onRowClick={onRowClick}
        />
    );
});
