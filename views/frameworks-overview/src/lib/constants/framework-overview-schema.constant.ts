import { z } from 'zod';
import { sharedCustomFrameworkRequirementsController } from '@controllers/frameworks';
import { sharedRequirementDetailsController } from '@controllers/requirements';
import { t } from '@globals/i18n/macro';
import type { FormSchema } from '@ui/forms';

const CODE_MAX_LENGTH = 191;
const LONG_TEXT_MAX_LENGTH = 30000;

export const buildFrameworkOverviewSchema = (): FormSchema => {
    const { requirement } = sharedRequirementDetailsController;
    const { isLoadingCategories, customCategories } =
        sharedCustomFrameworkRequirementsController;

    const options =
        customCategories?.data.map((category) => ({
            id: category.label,
            label: category.label,
            value: category.label,
        })) ?? [];

    const initialValue = requirement?.customCategory
        ? {
              id: requirement.customCategory,
              label: requirement.customCategory,
              value: requirement.customCategory,
          }
        : undefined;

    return {
        code: {
            type: 'text',
            label: t`Requirement code`,
            helpText: t`This code should uniquely identify the requirement.`,
            initialValue: requirement?.name ?? '',
            validator: z.string().min(1).max(CODE_MAX_LENGTH),
        },
        name: {
            type: 'text',
            label: t`Requirement name`,
            helpText: t`This name should succinctly describe the requirement.`,
            initialValue: requirement?.description ?? '',
            validator: z.string().min(1).max(LONG_TEXT_MAX_LENGTH),
        },
        category: {
            type: 'combobox',
            label: t`Category`,
            loaderLabel: t`Loading categories`,
            placeholder: t`Search by category`,
            isLoading: isLoadingCategories,
            getSearchEmptyState: () => t`No categories found`,
            options,
            initialValue,
            onFetchOptions: ({ search }) => {
                if (requirement && !isLoadingCategories) {
                    sharedCustomFrameworkRequirementsController.loadCustomCategories(
                        Number(requirement.customFrameworkId),
                        search,
                    );
                }
            },
            validator: z
                .array(z.any())
                .min(1, t`At least one category must be selected`),
        },
        description: {
            type: 'textarea',
            label: t`Description`,
            helpText: t`This description should explain the requirement standards.`,
            initialValue: requirement?.longDescription ?? '',
            validator: z.string().max(LONG_TEXT_MAX_LENGTH),
        },
        additionalInfo: {
            type: 'textarea',
            label: t`Additional info`,
            initialValue: requirement?.additionalInfo ?? '',
            validator: z.string().max(LONG_TEXT_MAX_LENGTH),
        },
    };
};
