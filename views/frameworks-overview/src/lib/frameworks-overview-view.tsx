import { useMemo } from 'react';
import { ViewEditCardComponent } from '@components/view-edit-card';
import {
    sharedCustomFrameworkRequirementsController,
    sharedFrameworkDetailsController,
} from '@controllers/frameworks';
import { sharedRequirementDetailsController } from '@controllers/requirements';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { useFormSubmit } from '@ui/forms';
import { FrameworksOverviewViewCard } from './components/frameworks-overview-card.component';
import { FrameworksOverviewFormCard } from './components/frameworks-overview-form-card.component';

export const FrameworksOverviewView = observer((): React.JSX.Element => {
    const { frameworkDetails, isLoading } = sharedFrameworkDetailsController;

    const { requirement } = sharedRequirementDetailsController;
    const { formRef, triggerSubmit } = useFormSubmit();

    const isCustomFramework = useMemo(() => {
        if (isLoading || !frameworkDetails) {
            return false;
        }

        return frameworkDetails.tag === 'CUSTOM';
    }, [frameworkDetails, isLoading]);

    const handleEdit = action(() => {
        if (requirement) {
            sharedCustomFrameworkRequirementsController.loadCustomCategories(
                Number(requirement.customFrameworkId),
            );
        }
    });

    return (
        <ViewEditCardComponent
            title={t`Overview`}
            data-testid="FrameworksOverviewView"
            data-id="fOBpfP4c"
            readOnlyComponent={<FrameworksOverviewViewCard />}
            editComponent={
                isCustomFramework ? (
                    <FrameworksOverviewFormCard formRef={formRef} />
                ) : null
            }
            onEdit={handleEdit}
            onSave={triggerSubmit}
        />
    );
});
