import { observer } from '@globals/mobx';
import { Form } from '@ui/forms';
import { buildFrameworkOverviewSchema } from '../constants/framework-overview-schema.constant';
import { frameworksOverviewSubmit } from '../helpers/frameworks-overview-submit.helper';
import type { FrameworksOverviewFormCardProps } from '../types/frameworks-overview-form-card-props.type';

export const FrameworksOverviewFormCard = observer(
    ({ formRef }: FrameworksOverviewFormCardProps): React.JSX.Element => {
        return (
            <Form
                hasExternalSubmitButton
                ref={formRef}
                formId="disable-monitor-form"
                schema={buildFrameworkOverviewSchema()}
                data-id="rp4GABXw"
                onSubmit={frameworksOverviewSubmit}
            />
        );
    },
);
