import { sharedRequirementDetailsController } from '@controllers/requirements';
import { Grid } from '@cosmos/components/grid';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Text } from '@cosmos/components/text';
import { ShowMore } from '@cosmos-lab/components/show-more';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const FrameworksOverviewViewCard = observer((): React.JSX.Element => {
    const { requirement } = sharedRequirementDetailsController;

    return (
        <Grid
            gap={'xl'}
            data-testid="FrameworksOverviewViewCard"
            data-id="MnpB3GLy"
        >
            <KeyValuePair label={t`Code`} value={requirement?.name} />
            <KeyValuePair
                label={t`Control description`}
                value={requirement?.description}
            />
            <KeyValuePair
                label={t`Description`}
                value={requirement?.longDescription}
            />
            {requirement?.additionalInfo && (
                <KeyValuePair
                    label={t`Additional information`}
                    value={requirement.additionalInfo}
                />
            )}
            {requirement?.additionalInfo2 && (
                <ShowMore
                    content={
                        <>
                            <Text size="200" as="p">
                                {requirement.additionalInfo2}
                            </Text>
                            {requirement.additionalInfo3 && (
                                <Text size="200" as="p">
                                    {requirement.additionalInfo3}
                                </Text>
                            )}
                        </>
                    }
                />
            )}
        </Grid>
    );
});
