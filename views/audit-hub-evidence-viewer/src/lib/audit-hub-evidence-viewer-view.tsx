import { DocumentViewer } from '@components/document-viewer';
import { sharedAuditHubEvidenceViewerController } from '@controllers/audit-hub-evidence-viewer';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const AuditHubEvidenceViewerView = observer((): JSX.Element => {
    const { evidenceDocument } = sharedAuditHubEvidenceViewerController;

    const evidenceName = evidenceDocument?.name ?? t`Evidence Document`;

    return (
        <DocumentViewer
            src={evidenceDocument?.signedUrl || ''}
            data-id="evidence-viewer-iframe"
            data-testid="EvidenceViewer"
            label={evidenceName}
        />
    );
});
