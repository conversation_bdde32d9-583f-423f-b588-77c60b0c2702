import { isArray, isEmpty } from 'lodash-es';
import { useCallback, useState } from 'react';
import {
    activeAccessReviewApplicationDetailsController,
    sharedAccessReviewApplicationReviewersController,
    sharedAccessReviewPeriodApplicationController,
} from '@controllers/access-reviews';
import { sharedUsersInfiniteController } from '@controllers/users';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Card } from '@cosmos/components/card';
import { ComboboxField } from '@cosmos/components/combobox-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import { ShowMore } from '@cosmos-lab/components/show-more';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import type { UserResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';

const MAX_USERS_TO_DISPLAY = 5;
const FORM_ID = 'editing-reviewers';

const getReviewers = (applicationReviewers: UserResponseDto[]) => {
    let firstUsers, remainingUsers;

    if (applicationReviewers.length > MAX_USERS_TO_DISPLAY) {
        const firstFiveReviewers =
            applicationReviewers.length > MAX_USERS_TO_DISPLAY
                ? applicationReviewers.slice(0, MAX_USERS_TO_DISPLAY)
                : applicationReviewers;

        const remainingReviewers =
            applicationReviewers.length > MAX_USERS_TO_DISPLAY
                ? applicationReviewers.slice(MAX_USERS_TO_DISPLAY)
                : [];

        remainingUsers = (
            <ShowMore
                content={remainingReviewers.map(
                    ({ firstName, lastName, id, email, avatarUrl }) => {
                        const fullName = getFullName(firstName, lastName);
                        const fallbackText = getInitials(fullName);

                        return (
                            <Stack
                                direction="row"
                                align="center"
                                gap="2x"
                                key={`${id}-${firstName}-${lastName}`}
                                data-id="V0smpIue"
                            >
                                <AvatarIdentity
                                    primaryLabel={fullName}
                                    secondaryLabel={email}
                                    fallbackText={fallbackText}
                                    data-testid="AuditorCell"
                                    data-id="eVtVN8Z-"
                                    imgSrc={avatarUrl ?? undefined}
                                />
                            </Stack>
                        );
                    },
                )}
            />
        );

        firstUsers = firstFiveReviewers.map(
            ({ firstName, lastName, id, email, avatarUrl }) => {
                const fullName = getFullName(firstName, lastName);
                const fallbackText = getInitials(fullName);

                return (
                    <Stack
                        direction="row"
                        align="center"
                        gap="2x"
                        key={`${id}-${firstName}-${lastName}`}
                        data-id="V0smpIue"
                    >
                        <AvatarIdentity
                            primaryLabel={fullName}
                            secondaryLabel={email}
                            fallbackText={fallbackText}
                            data-testid="AuditorCell"
                            data-id="eVtVN8Z-"
                            imgSrc={avatarUrl ?? undefined}
                        />
                    </Stack>
                );
            },
        );
    }

    return { firstUsers, remainingUsers };
};

export const ReviewersCard = observer((): React.JSX.Element => {
    const [isEditing, setIsEditing] = useState(false);
    const [feedback, setFeedback] = useState<
        { type: 'error' | 'success'; message: string } | undefined
    >(undefined);
    const [selectedReviewers, setSelectedReviewers] = useState<
        ListBoxItemData[] | ListBoxItemData
    >([]);

    const { applicationReviewers } =
        activeAccessReviewApplicationDetailsController;

    const reviewers = getReviewers(applicationReviewers);

    const {
        hasNextPage: hasMoreUsers,
        isLoading,
        onFetchUsers,
        options,
    } = sharedUsersInfiniteController;

    const { hasLimitedAccess } = sharedFeatureAccessModel;

    const handleOnChange = useCallback(
        (selectedItems: ListBoxItemData[] | ListBoxItemData) => {
            setSelectedReviewers(selectedItems);
            setFeedback(undefined);
        },
        [setSelectedReviewers],
    );

    const handleOnSave = useCallback(() => {
        if (isArray(selectedReviewers) && isEmpty(selectedReviewers)) {
            setFeedback({
                type: 'error',
                message: t`Application must have at least 1 reviewer`,
            });

            return;
        }

        if (isArray(selectedReviewers) && selectedReviewers.length === 1) {
            setFeedback({
                type: 'error',
                message: t`Reviewers cannot review their own status. Please add another reviewer.`,
            });

            return;
        }

        sharedAccessReviewApplicationReviewersController.updateApplicationReviewers(
            (selectedReviewers as ListBoxItemData[]).map((reviewer) =>
                Number(reviewer.id),
            ),
            () => {
                sharedAccessReviewPeriodApplicationController.accessReviewPeriodApplication.invalidate();
                activeAccessReviewApplicationDetailsController.refreshUsersReviewers();
            },
        );
        setIsEditing(false);
    }, [selectedReviewers]);

    if (isLoading) {
        return <Skeleton barCount={1} />;
    }

    return (
        <Box data-id="linked-evidence-card" height="fit-content">
            <Card
                title={t`Reviewers`}
                isEditMode={isEditing}
                body={
                    isEditing ? (
                        <Stack direction="column" gap="6x">
                            <ComboboxField
                                isMultiSelect
                                isLoading={isLoading}
                                clearSelectedItemButtonLabel={t`Clear`}
                                data-id="reviewers-combobox"
                                formId={FORM_ID}
                                feedback={feedback}
                                placeholder={t`Search by name or email`}
                                label=""
                                hasMore={hasMoreUsers}
                                loaderLabel={t`Loading results`}
                                name="reviewers-combobox"
                                removeAllSelectedItemsLabel={t`Clear all`}
                                options={options}
                                getSearchEmptyState={() =>
                                    t`No employees found`
                                }
                                getRemoveIndividualSelectedItemClickLabel={
                                    undefined
                                }
                                defaultSelectedOptions={applicationReviewers.map(
                                    (reviewer) => ({
                                        avatar: {
                                            fallbackText: getInitials(
                                                getFullName(
                                                    reviewer.firstName,
                                                    reviewer.lastName,
                                                ),
                                            ),
                                            imgAlt: getFullName(
                                                reviewer.firstName,
                                                reviewer.lastName,
                                            ),
                                        },
                                        id: String(reviewer.id),
                                        label: getFullName(
                                            reviewer.firstName,
                                            reviewer.lastName,
                                        ),
                                        value: String(reviewer.id),
                                    }),
                                )}
                                onChange={handleOnChange}
                                onFetchOptions={onFetchUsers}
                            />
                            <Stack gap="3x">
                                <Button
                                    label={t`Save`}
                                    level="primary"
                                    onClick={handleOnSave}
                                />
                                <Button
                                    label={t`Cancel`}
                                    level="secondary"
                                    onClick={() => {
                                        setFeedback(undefined);
                                        setIsEditing(false);
                                    }}
                                />
                            </Stack>
                        </Stack>
                    ) : (
                        <>
                            {applicationReviewers.length >
                            MAX_USERS_TO_DISPLAY ? (
                                <Stack direction={'column'} gapY="2x">
                                    {reviewers.firstUsers}
                                    {reviewers.remainingUsers}
                                </Stack>
                            ) : (
                                <StackedList data-id="V0smpIue">
                                    {applicationReviewers.map(
                                        ({
                                            firstName,
                                            lastName,
                                            id,
                                            avatarUrl,
                                        }) => {
                                            const fullName = getFullName(
                                                firstName,
                                                lastName,
                                            );
                                            const fallbackText =
                                                getInitials(fullName);

                                            return (
                                                <StackedListItem
                                                    data-id="XJzjA1c4"
                                                    key={`${id}-${firstName}-${lastName}`}
                                                    primaryColumn={
                                                        <Stack
                                                            direction="row"
                                                            align="center"
                                                            gap="2x"
                                                            key={`${id}-${firstName}-${lastName}`}
                                                            data-id="V0smpIue"
                                                        >
                                                            <AvatarIdentity
                                                                data-id="eVtVN8Z-"
                                                                primaryLabel={
                                                                    fullName
                                                                }
                                                                fallbackText={
                                                                    fallbackText
                                                                }
                                                                imgSrc={
                                                                    avatarUrl ??
                                                                    undefined
                                                                }
                                                            />
                                                        </Stack>
                                                    }
                                                />
                                            );
                                        },
                                    )}
                                </StackedList>
                            )}
                        </>
                    )
                }
                actions={
                    hasLimitedAccess || isEditing
                        ? []
                        : [
                              {
                                  actionType: 'button',
                                  id: 'edit-button',
                                  typeProps: {
                                      label: t`Edit`,
                                      level: 'secondary',
                                      'data-id': 'edit-button',
                                      onClick: () => {
                                          setIsEditing(true);
                                      },
                                  },
                              },
                          ]
                }
            />
        </Box>
    );
});
