import {
    activeAccessReviewApplicationDetailsController,
    sharedAccessReviewPeriodApplicationUsersController,
} from '@controllers/access-reviews';
import { Banner } from '@cosmos/components/banner';
import { DEFAULT_PAGE_SIZE } from '@cosmos/components/datatable';
import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { LinkedEvidenceCard } from './linked-evidence-card';
import { ReviewersCard } from './reviewers-card';

export const AccessReviewApplicationDetailsOverview = observer(
    (): React.JSX.Element => {
        const {
            displayReviewersBanner,
            isLoading: isApplicationDetailsLoading,
        } = activeAccessReviewApplicationDetailsController;
        const { hasLimitedAccess } = sharedFeatureAccessModel;

        sharedAccessReviewPeriodApplicationUsersController.loadAccessReviewPeriodApplicationUsers(
            {
                pagination: {
                    pageIndex: 0,
                    pageSize: 10,
                    pageSizeOptions: [DEFAULT_PAGE_SIZE],
                },
                globalFilter: { search: '', filters: {} },
                sorting: [],
            },
        );

        return (
            <Stack
                gap="4x"
                data-testid="AccessReviewApplicationDetailsOverview"
                data-id="zEObklam"
                direction="column"
            >
                {!isApplicationDetailsLoading && displayReviewersBanner && (
                    <Banner
                        displayMode="section"
                        severity="critical"
                        title={t`Assign an additional reviewer`}
                        body={
                            <Text as="p" size="100" colorScheme="critical">
                                {hasLimitedAccess
                                    ? t`The current reviewer is also a user and cannot review their own status. Contact an admin to add another reviewer.`
                                    : t`The current reviewer is also a user and cannot review their own status. Select "Edit" on the reviewer card and add another reviewer.`}
                            </Text>
                        }
                    />
                )}
                <Grid columns="2" gap="4x">
                    <ReviewersCard />
                    <LinkedEvidenceCard />
                </Grid>
            </Stack>
        );
    },
);
