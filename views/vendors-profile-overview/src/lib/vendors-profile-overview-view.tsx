import { sharedVendorsDetailsController } from '@controllers/vendors';
import { Grid } from '@cosmos/components/grid';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { ScheduleBannerComponent } from './components/schedule-banner-component';
import { ContinuousMonitoringCardComponent } from './components/vendor-continuous-monitoring-card-component';
import { VendorDetailsCardComponent } from './components/vendor-details-card-component';
import { VendorImpactAssessmentCardComponent } from './components/vendor-impact-assessment-card-component';
import { VendorInternalDetailsCardComponent } from './components/vendor-internal-details-card-component';

export const VendorsProfileOverviewView = observer((): React.JSX.Element => {
    const { vendorDetails, isLoading } = sharedVendorsDetailsController;
    const { isVendorRiskManagementProEnabled, isVendorEditable } =
        sharedFeatureAccessModel;

    if (isLoading) {
        return <Loader isSpinnerOnly label={t`Loading...`} />;
    }

    return (
        <Grid
            gap="8x"
            data-testid="VendorsProfileOverviewView"
            data-id="ZVFSjIV5"
        >
            <Stack gap="8x" direction="column">
                <ScheduleBannerComponent />
                {vendorDetails?.isDrataUser && (
                    <ContinuousMonitoringCardComponent />
                )}
                <Grid flow="column" gap="8x" columns="2" justify="center">
                    <Stack direction="column" gap="6x">
                        <VendorDetailsCardComponent
                            isEditable={isVendorEditable}
                        />
                        {isVendorRiskManagementProEnabled && (
                            <VendorImpactAssessmentCardComponent
                                isEditable={isVendorEditable}
                            />
                        )}
                    </Stack>
                    <Stack direction="column" gap="6x">
                        <VendorInternalDetailsCardComponent
                            isEditable={isVendorEditable}
                        />
                    </Stack>
                </Grid>
            </Stack>
        </Grid>
    );
});
