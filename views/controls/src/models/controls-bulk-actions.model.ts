import { has, noop } from 'lodash-es';
import {
    openAddApprovalsModal,
    openControlOwnerModal,
} from '@components/controls';
import {
    type ControlsSearchQuery,
    sharedControlsBulkMutationController,
    sharedControlsController,
} from '@controllers/controls';
import type {
    BulkAction,
    DatatableRef,
    DatatableRowSelectionState,
} from '@cosmos/components/datatable';
import type { ControlListResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';

export class ControlsBulkActionsModel {
    selectedControls: ControlListResponseDto[] = [];
    isAllRowsSelected = false;
    appliedFilters: ControlsSearchQuery | null;
    datatableRef: React.RefObject<DatatableRef>;

    constructor(
        appliedFilters: ControlsSearchQuery | null,
        datatableRef: React.RefObject<DatatableRef>,
    ) {
        makeAutoObservable(this);
        this.appliedFilters = appliedFilters;
        this.datatableRef = datatableRef;
    }

    get hasArchiveFilterActive(): boolean {
        return has(this.appliedFilters, 'isArchived');
    }

    get hasApproversAssignedFilterActive(): boolean {
        return has(this.appliedFilters, 'approversAssigned');
    }

    get markOutScopeAction(): BulkAction {
        return {
            actionType: 'button',
            id: 'bulk-actions-controls-mark-out-of-scope',
            typeProps: {
                label: t`Mark out of scope`,
                level: 'tertiary',
                onClick: action(() => {
                    sharedControlsBulkMutationController.markOutOfScopeControls(
                        this.selectedControlsIds,
                        {
                            isAllRowsSelected: this.isAllRowsSelected,
                            onSuccess: this.onSuccess,
                        },
                    );
                }),
            },
        };
    }

    get markInScopeAction(): BulkAction {
        return {
            actionType: 'button',
            id: 'bulk-actions-controls-mark-in-scope',
            typeProps: {
                label: t`Mark in scope`,
                level: 'tertiary',
                onClick: action(() => {
                    sharedControlsBulkMutationController.markInScopeControls(
                        this.selectedControlsIds,
                        {
                            isAllRowsSelected: this.isAllRowsSelected,
                            onSuccess: this.onSuccess,
                        },
                    );
                }),
            },
        };
    }

    get addApprovalsAction(): BulkAction {
        const readOnlyButton =
            !this.hasApproversAssignedFilterActive ||
            this.appliedFilters?.approversAssigned;

        return {
            actionType: 'tooltipButton',
            id: 'bulk-actions-controls-add-approvals',
            typeProps: {
                button: {
                    label: t`Add approvals`,
                    level: 'tertiary',
                    onClick: readOnlyButton
                        ? noop
                        : action(() => {
                              openAddApprovalsModal({
                                  controls: this.selectedControls,
                                  onSuccess: this.onSuccess,
                              });
                          }),
                },
                tooltip: {
                    text: t`Filter to "no approvers assigned" to set up bulk approvals`,
                    isInteractive: true,
                },
            },
        };
    }

    get bulkActions(): BulkAction[] {
        return [
            this.markOutScopeAction,
            this.markInScopeAction,
            {
                actionType: 'button',
                id: 'bulk-actions-controls-assign-remove-owners',
                typeProps: {
                    label: t`Assign/remove control owners`,
                    level: 'tertiary',
                    onClick: action(() => {
                        openControlOwnerModal({
                            controlsIds: this.selectedControlsIds,
                            onSuccess: this.onSuccess,
                        });
                    }),
                },
            },
            this.addApprovalsAction,
        ];
    }

    onSuccess = (): void => {
        this.datatableRef.current?.resetRowSelection();
    };

    get selectedControlsIds(): number[] {
        return this.selectedControls.map((item) => item.id);
    }

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ): void => {
        const { selectedRows, isAllRowsSelected } = currentRowSelectionState;
        const { controls } = sharedControlsController;
        const selectedIndices = Object.keys(selectedRows);
        const selectedRowData = selectedIndices.map(
            (index) => controls[parseInt(index)],
        );

        this.selectedControls = selectedRowData
            .map((row) => row)
            .filter(Boolean);
        this.isAllRowsSelected = isAllRowsSelected;
    };
}
