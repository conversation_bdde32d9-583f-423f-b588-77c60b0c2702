import { sharedConnectionsController } from '@controllers/connections';
import { sharedControlInfiniteAllOwnersController } from '@controllers/controls';
import type { FilterProps } from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

export class ControlListFilterModel {
    initialFrameworkSlug?: string;

    constructor(initialFrameworkSlug?: string) {
        this.initialFrameworkSlug = initialFrameworkSlug;
        makeAutoObservable(this);
    }

    get #statusFilter(): Filter {
        return {
            filterType: 'radio',
            id: 'isArchived',
            label: t`Status`,
            options: [
                {
                    label: t`In scope`,
                    value: 'false',
                },
                {
                    label: t`Out of scope`,
                    value: 'true',
                },
            ],
        };
    }

    get #readinessFiler(): Filter {
        return {
            filterType: 'radio',
            id: 'isReady',
            label: t`Readiness`,
            options: [
                {
                    label: t`Ready`,
                    value: 'true',
                },
                {
                    label: t`Not ready`,
                    value: 'false',
                },
            ],
        };
    }

    get #monitoringFilter(): Filter {
        return {
            filterType: 'radio',
            id: 'isMonitored',
            label: t`Monitoring`,
            options: [
                {
                    label: t`Monitored`,
                    value: 'true',
                },
                {
                    label: t`Not monitored`,
                    value: 'false',
                },
            ],
        };
    }

    get #requiredApprovalsFilter(): Filter {
        return {
            filterType: 'radio',
            id: 'requiredApprovals',
            label: t`Required approvals`,
            options: [
                {
                    label: t`Approved`,
                    value: 'APPROVED',
                },
                {
                    label: t`Changes requested`,
                    value: 'CHANGES_REQUESTED',
                },
                {
                    label: t`Needs approval`,
                    value: 'NEEDS_APPROVAL',
                },
                {
                    label: t`Prepare for approvers`,
                    value: 'PREPARE_FOR_APPROVERS',
                },
                {
                    label: t`Not assigned`,
                    value: 'NO_REVIEW',
                },
            ],
        };
    }

    get #evidenceFilter(): Filter {
        return {
            filterType: 'radio',
            id: 'hasEvidence',
            label: t`Evidence`,
            options: [
                {
                    label: t`Manual evidence linked`,
                    value: 'true',
                },
                {
                    label: t`No manual evidence`,
                    value: 'false',
                },
            ],
        };
    }

    get #policiesFilter(): Filter {
        return {
            filterType: 'radio',
            id: 'hasPolicy',
            label: t`Policies`,
            options: [
                {
                    label: t`Policies linked`,
                    value: 'true',
                },
                {
                    label: t`No policies`,
                    value: 'false',
                },
            ],
        };
    }

    get #controlOwnersFilter(): Filter {
        return {
            filterType: 'radio',
            id: 'isOwned',
            label: t`Control owners`,
            options: [
                {
                    label: t`Owners assigned`,
                    value: 'true',
                },
                {
                    label: t`No owners assigned`,
                    value: 'false',
                },
            ],
        };
    }

    get #approversFilter(): Filter {
        return {
            filterType: 'radio',
            id: 'approversAssigned',
            label: t`Approvers`,
            options: [
                {
                    label: t`Approvers assigned`,
                    value: 'true',
                },
                {
                    label: t`No approvers assigned`,
                    value: 'false',
                },
            ],
        };
    }

    get #frameworksFilter(): Filter {
        const { currentWorkspaceEnabledFrameworks } =
            sharedWorkspacesController;

        const frameworksOptions = currentWorkspaceEnabledFrameworks
            .map(({ name, tag }) => ({
                label: name,
                value: tag,
            }))
            .sort((a, b) => a.label.localeCompare(b.label));

        const filter: Filter = {
            filterType: 'radio',
            id: 'frameworkTags',
            label: t`Framework`,
            options: [
                ...frameworksOptions,
                { label: t`No framework`, value: 'NONE' },
            ],
        };

        // Set initial value if frameworkSlug is provided and exists in options
        if (this.initialFrameworkSlug) {
            const allOptions = [
                ...frameworksOptions,
                { label: t`No framework`, value: 'NONE' },
            ];
            const matchingOption = allOptions.find(
                (option) =>
                    option.value.toUpperCase() ===
                    this.initialFrameworkSlug?.toUpperCase(),
            );

            if (matchingOption) {
                filter.value = matchingOption.value;
            }
        }

        return filter;
    }

    get #hasJiraConnection(): boolean {
        const { allConfiguredConnections } = sharedConnectionsController;
        const { currentWorkspace } = sharedWorkspacesController;

        const jiraConnections = allConfiguredConnections.filter(
            (connection) =>
                connection.clientType === 'JIRA' ||
                connection.clientType === 'MERGEDEV_JIRA_DATA_CENTER',
        );

        const usableJiraConnections = jiraConnections.filter((connection) =>
            connection.workspaces.some(
                (workspace) => workspace.id === currentWorkspace?.id,
            ),
        );

        return usableJiraConnections.some(
            (connection) => connection.writeAccessEnabled,
        );
    }

    get #ticketsFilter(): Filter {
        return {
            filterType: 'radio',
            id: 'hasTicket',
            label: t`Tickets`,
            options: [
                {
                    label: t`In progress`,
                    value: 'IN_PROGRESS',
                },
                {
                    label: t`Done`,
                    value: 'ARCHIVED',
                },
            ],
        };
    }

    get #controlOwnerFiler(): Filter {
        const { options, hasNextPage, isFetching, isLoading, loadNextPage } =
            sharedControlInfiniteAllOwnersController;

        return {
            filterType: 'combobox',
            id: 'userIds',
            label: t`Control owner`,
            placeholder: t`Search by name`,
            options,
            hasMore: hasNextPage,
            isLoading: isFetching && isLoading,
            onFetchOptions: loadNextPage,
        };
    }

    get filters(): FilterProps {
        return {
            clearAllButtonLabel: t`Reset`,
            filters: [
                this.#statusFilter,
                this.#frameworksFilter,
                this.#readinessFiler,
                this.#monitoringFilter,
                this.#requiredApprovalsFilter,
                this.#evidenceFilter,
                this.#policiesFilter,
                ...(this.#hasJiraConnection ? [this.#ticketsFilter] : []),
                this.#controlOwnersFilter,
                this.#approversFilter,
                this.#controlOwnerFiler,
            ],
            triggerLabel: t`Filters`,
        };
    }
}
