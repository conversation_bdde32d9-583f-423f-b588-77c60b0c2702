import type { Row } from '@cosmos/components/datatable';
import { Metadata } from '@cosmos/components/metadata';
import type { ControlListResponseDto } from '@globals/api-sdk/types';
import { getControlStatusDisplay } from '@helpers/control-status';

export const ControlCodeCell = ({
    row,
}: {
    row: Row<ControlListResponseDto>;
}): React.JSX.Element => {
    const { isReady, code, archivedAt } = row.original;

    const { colorScheme, iconName } = getControlStatusDisplay({
        isReady,
        archivedAt,
    });

    return (
        <Metadata
            iconName={iconName}
            label={code}
            data-id="kTs6wj1A"
            data-testid="ControlCodeCell"
            colorScheme={colorScheme}
        />
    );
};
