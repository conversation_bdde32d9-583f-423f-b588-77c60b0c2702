import {
    AssetsClassesCellComponent,
    AssetsDateCellComponent,
    AssetsDeviceComplianceCellComponent,
    AssetsNoteCellComponent,
    AssetsOwnerCellComponent,
    AssetsSourceCellComponent,
    AssetsTypeCell,
    OwnerStatusCell,
} from '@components/assets';
import type { DatatableProps } from '@cosmos/components/datatable';
import type { AssetResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const getAssetsColumns =
    (): DatatableProps<AssetResponseDto>['columns'] => [
        {
            id: 'NAME',
            header: t`Name`,
            enableSorting: true,
            accessorKey: 'name',
        },
        {
            id: 'ASSET_CLASS_TYPES',
            header: t`Class`,
            enableSorting: false,
            accessorKey: 'assetClassTypes',
            minSize: 300,
            cell: AssetsClassesCellComponent,
        },
        {
            id: 'ASSET_TYPE',
            header: t`Type`,
            enableSorting: false,
            accessorKey: 'assetType',
            cell: AssetsTypeCell,
        },
        {
            id: 'ACCOUNT_NAME',
            header: t`Account name`,
            enableSorting: false,
            accessorKey: 'company',
        },
        {
            id: 'DESCRIPTION',
            header: t`Description`,
            enableSorting: false,
            accessorKey: 'description',
        },
        {
            id: 'ASSET_PROVIDER',
            header: t`Source`,
            enableSorting: false,
            accessorKey: 'assetProvider',
            minSize: 200,
            cell: AssetsSourceCellComponent,
        },
        {
            id: 'UNIQUE_ID',
            header: t`Unique ID`,
            enableSorting: false,
            accessorKey: 'uniqueId',
        },
        {
            id: 'OWNER',
            header: t`Owner`,
            enableSorting: false,
            accessorKey: 'owner',
            cell: AssetsOwnerCellComponent,
        },
        {
            id: 'EMPLOYMENT_STATUS',
            header: t`Owner status`,
            enableSorting: false,
            accessorKey: 'employmentStatus',
            cell: OwnerStatusCell,
        },
        {
            id: 'CREATED',
            header: t`Created on`,
            enableSorting: true,
            accessorKey: 'createdAt',
            cell: AssetsDateCellComponent,
        },
        {
            id: 'UPDATED',
            header: t`Last modified on`,
            enableSorting: true,
            accessorKey: 'updatedAt',
            cell: AssetsDateCellComponent,
        },
        {
            id: 'REMOVED_AT',
            header: t`Deleted on`,
            enableSorting: true,
            sortDescFirst: false,
            accessorKey: 'removedAt',
            cell: AssetsDateCellComponent,
        },
        {
            id: 'NOTES',
            header: t`Notes`,
            enableSorting: false,
            accessorKey: 'notes',
            cell: (props) => (
                <AssetsNoteCellComponent
                    key={props.row.original.id}
                    {...props}
                    data-id="lEYAIUzK"
                />
            ),
            minSize: 250,
        },
        {
            id: 'DEVICE_COMPLIANCE',
            header: t`Device compliance`,
            enableSorting: false,
            accessorKey: 'device',
            cell: AssetsDeviceComplianceCellComponent,
        },
    ];
