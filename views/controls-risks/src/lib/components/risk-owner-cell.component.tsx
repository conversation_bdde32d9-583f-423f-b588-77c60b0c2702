import { isEmpty } from 'lodash-es';
import type React from 'react';
import { AvatarStack } from '@cosmos-lab/components/avatar-stack';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { RiskUserResponseDto } from '@globals/api-sdk/types';
import { getFullName, getInitials } from '@helpers/formatters';

const MAX_OWNERS_TO_SHOW = 6;

interface RiskOwnerCellProps {
    row: {
        original: {
            owners: RiskUserResponseDto[];
        };
    };
}

export const RiskOwnerCell = ({
    row: { original },
}: RiskOwnerCellProps): React.ReactNode => {
    const { owners } = original;

    if (isEmpty(owners)) {
        return <EmptyValue label="-" />;
    }

    return (
        <AvatarStack
            data-id="ecJ4vcxR"
            maxVisibleItems={MAX_OWNERS_TO_SHOW}
            data-testid="RiskOwnerCell"
            avatarData={owners.map((owner) => ({
                fallbackText: getInitials(
                    getFullName(owner.firstName, owner.lastName),
                ),
                primaryLabel: getFull<PERSON><PERSON>(owner.firstName, owner.lastName),
                secondaryLabel: owner.email,
                imgSrc: owner.avatarUrl,
            }))}
        />
    );
};
