import {
    AccessReviewApplicationAction<PERSON>ell,
    AccessReviewApplicationCell,
    AccessReviewApplicationSourceCell,
    AccessReviewApplicationWarningsCell,
} from '@components/access-review';
import type { DatatableProps } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import type { ApplicationData } from '../types/access-review-applications.types';

export const ACCESS_REVIEW_APPLICATIONS_FORM_ID =
    'access-review-applications-form';

export const getAccessReviewApplicationColumns =
    (): DatatableProps<ApplicationData>['columns'] =>
        [
            {
                accessorKey: 'id',
                header: '',
                id: 'actions',
                enableSorting: false,
                size: 50,
                meta: {
                    shouldIgnoreRowClick: true,
                },
                cell: AccessReviewApplicationActionCell,
            },
            {
                accessorKey: 'name',
                header: t`Application`,
                id: 'name',
                enableSorting: false,
                size: 200,
                cell: AccessReviewApplicationCell,
            },
            {
                accessorKey: 'clientType',
                header: t`Warnings`,
                id: 'totalWarnings',
                enableSorting: false,
                size: 150,
                cell: AccessReviewApplicationWarningsCell,
            },
            {
                accessorKey: 'source',
                header: t`Type`,
                id: 'source',
                enableSorting: false,
                size: 600,
                cell: AccessReviewApplicationSourceCell,
            },
        ] satisfies DatatableProps<ApplicationData>['columns'];
