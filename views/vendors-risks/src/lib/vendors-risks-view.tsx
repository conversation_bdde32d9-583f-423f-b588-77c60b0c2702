import type React from 'react';
import { AppDatatable } from '@components/app-datatable';
import { VendorsRisksOverviewPanelComponent } from '@components/vendors-risks-overview-panel';
import {
    handleOpenDetailsPanel,
    sharedVendorsRisksController,
    sharedVendorsRisksDownloadController,
} from '@controllers/vendors';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { EmptyState } from '@cosmos/components/empty-state';
import { Grid } from '@cosmos/components/grid';
import { Loader } from '@cosmos/components/loader';
import { DataPosture } from '@cosmos-lab/components/data-posture';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import { plural, t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { generateRiskPostureBoxes } from '@helpers/risk-score';
import { DEFAULT_PAGINATION_OPTIONS } from './constants/vendors-risks-view.constants';
import {
    getTableActions,
    getTableColumns,
} from './helpers/vendors-risks.helpers';

export const VendorsRisksView = observer((): React.JSX.Element => {
    const {
        settings,
        risks,
        isLoading,
        loadRisks,
        dashboard,
        risksQuery,
        hasSearchFilter,
        isSettingsLoading,
    } = sharedVendorsRisksController;

    const { isLoading: isDownloadLoading, downloadVendorRisksReport } =
        sharedVendorsRisksDownloadController;

    const remainingCount = dashboard?.remaining ?? 0;

    if (
        !isLoading &&
        !isSettingsLoading &&
        risksQuery.data?.total === 0 &&
        !hasSearchFilter
    ) {
        return (
            <EmptyState
                title={t`See all risks related to your vendors in one place.`}
                description={t`When you add a risk to a vendor, it’ll show up here.`}
                illustrationName="RiskManagement"
            />
        );
    }

    return (
        <>
            {isSettingsLoading ? (
                <Loader isSpinnerOnly label={t`Loading...`} />
            ) : (
                <Grid
                    columns="2fr 6fr"
                    gap="4x"
                    pb="4x"
                    data-testid="VendorsRisksView"
                >
                    <Box
                        borderRadius="borderRadiusLg"
                        borderColor="neutralBorderFaded"
                        borderWidth="borderWidth1"
                        p="xl"
                    >
                        <StatBlock
                            title={t`Risk assessed`}
                            statValue={dashboard?.scored ?? 0}
                            statValueColor="neutral"
                            totalText={t`${remainingCount} ${plural(remainingCount, { one: 'risk', other: 'risks' })} left for assessment`}
                        />
                    </Box>

                    <Box>
                        <Card
                            title={t`Risks posture`}
                            body={
                                <DataPosture
                                    size="lg"
                                    boxes={generateRiskPostureBoxes(
                                        dashboard?.riskPosture ?? {},
                                        settings?.thresholds ?? [],
                                    )}
                                />
                            }
                        />
                    </Box>
                </Grid>
            )}

            <AppDatatable
                isLoading={isLoading}
                tableId="vendor-risks-datatable"
                total={risksQuery.data?.total ?? 0}
                data-id="vendor-risks-overview-datatable"
                columns={getTableColumns()}
                data={risks}
                defaultPaginationOptions={DEFAULT_PAGINATION_OPTIONS}
                tableActions={getTableActions({
                    onDownload: downloadVendorRisksReport,
                    isDownloadLoading,
                })}
                tableSearchProps={{
                    placeholder: t`Search`,
                    hideSearch: false,
                    debounceDelay: 1000,
                    defaultValue: '',
                }}
                onFetchData={loadRisks}
                onRowClick={({ row }) => {
                    handleOpenDetailsPanel(row.riskId, () => (
                        <VendorsRisksOverviewPanelComponent data-id="o6TMqNJn" />
                    ));
                }}
            />
        </>
    );
});
