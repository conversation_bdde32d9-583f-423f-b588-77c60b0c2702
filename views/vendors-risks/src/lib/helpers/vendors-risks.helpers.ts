import type { ComponentProps } from 'react';
import {
    VendorsRisksCellAnticipatedCompletionDateComponent,
    VendorsRisksCellIdentifiedDateComponent,
    VendorsRisksCellInherentScoreComponent,
    VendorsRisksCellOwnerAvatarComponent,
    VendorsRisksCellResidualScoreComponent,
    VendorsRisksCellScoreSelectImpactComponent,
    VendorsRisksCellScoreSelectLikelihoodComponent,
    VendorsRisksCellScoreSelectResidualImpactComponent,
    VendorsRisksCellScoreSelectResidualLikelihoodComponent,
    VendorsRisksCellTreatmentSelect,
} from '@components/vendors-risks';
import type { VendorsRisksTableItem } from '@controllers/vendors';
import type { Datatable, DatatableProps } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';

export const getTableColumns =
    (): DatatableProps<VendorsRisksTableItem>['columns'] => [
        {
            header: t`Risk identified date`,
            id: 'identifiedAt',
            accessorKey: 'identifiedAt',
            cell: VendorsRisksCellIdentifiedDateComponent,
        },
        {
            accessorKey: 'title',
            header: t`Risk name`,
            id: 'riskName',
        },
        {
            accessorKey: 'vendorName',
            header: t`Vendor name`,
            id: 'vendorName',
        },
        {
            cell: VendorsRisksCellScoreSelectImpactComponent,
            header: t`Inherent impact`,
            id: 'impact',
            accessorKey: 'title', // use title as accessor key to always render the cell
            meta: {
                shouldIgnoreRowClick: true,
            },
        },
        {
            cell: VendorsRisksCellScoreSelectLikelihoodComponent,
            header: t`Inherent likelihood`,
            id: 'likelihood',
            accessorKey: 'title', // use title as accessor key to always render the cell
            meta: {
                shouldIgnoreRowClick: true,
            },
        },
        {
            cell: VendorsRisksCellInherentScoreComponent,
            header: t`Risk Score`,
            id: 'riskScore',
            accessorKey: 'score',
            enableSorting: false,
        },
        {
            cell: VendorsRisksCellScoreSelectResidualImpactComponent,
            header: t`Residual impact`,
            id: 'residualImpact',
            accessorKey: 'title', // use title as accessor key to always render the cell
            meta: {
                shouldIgnoreRowClick: true,
            },
        },
        {
            cell: VendorsRisksCellScoreSelectResidualLikelihoodComponent,
            header: t`Residual likelihood`,
            id: 'residualLikelihood',
            accessorKey: 'title', // use title as accessor key to always render the cell
            meta: {
                shouldIgnoreRowClick: true,
            },
        },
        {
            cell: VendorsRisksCellResidualScoreComponent,
            header: t`Residual Score`,
            id: 'residualScore',
            accessorKey: 'score',
            enableSorting: false,
        },
        {
            header: t`Owner`,
            id: 'owner',
            accessorKey: 'owners',
            cell: VendorsRisksCellOwnerAvatarComponent,
            meta: {
                shouldIgnoreRowClick: true,
            },
        },
        {
            cell: VendorsRisksCellTreatmentSelect,
            header: t`Treatment`,
            id: 'treatment',
            accessorKey: 'treatmentPlan',
            minSize: 160,
            maxSize: 160,
            size: 160,
            meta: {
                shouldIgnoreRowClick: true,
            },
        },
        {
            accessorKey: 'anticipatedCompletionDate',
            header: t`Anticipated completion date`,
            id: 'anticipatedCompletionDate',
            cell: VendorsRisksCellAnticipatedCompletionDateComponent,
        },
    ];

export const getTableActions = action(
    (options?: {
        onDownload?: () => void;
        isDownloadLoading?: boolean;
    }): ComponentProps<typeof Datatable>['tableActions'] => [
        {
            actionType: 'button',
            id: 'vendor-risks-download-action',
            typeProps: {
                label: t`Download`,
                level: 'tertiary',
                colorScheme: 'neutral',
                startIconName: 'Download',
                isLoading: options?.isDownloadLoading ?? false,
                onClick: options?.onDownload,
            },
        },
    ],
);
