import { isNil } from 'lodash-es';
import type { IconName } from '@cosmos/components/icon';
import type { ColorScheme } from '@cosmos/components/metadata';

export interface ControlStatusDisplayOptions {
    isReady: boolean;
    archivedAt: string;
}

export interface ControlStatusDisplay {
    colorScheme: ColorScheme;
    iconName: IconName;
}

/**
 * Gets the appropriate color scheme and icon for a control based on its status.
 *
 * @param options - The control status options containing isReady (whether the control is ready) and archivedAt (the archived date if archived, or empty string if not archived).
 * @returns The color scheme and icon name for the control status.
 * @example
 * ```tsx
 * const { colorScheme, iconName } = getControlStatusDisplay({
 *   isReady: true,
 *   archivedAt: ''
 * });
 * // Returns: { colorScheme: 'success', iconName: 'CheckCircle' }
 * ```
 */
export function getControlStatusDisplay({
    isReady,
    archivedAt,
}: ControlStatusDisplayOptions): ControlStatusDisplay {
    const isArchived = !isNil(archivedAt) && archivedAt !== '';

    if (isArchived) {
        return {
            colorScheme: 'neutral',
            iconName: 'OutOfScope',
        };
    }

    return {
        colorScheme: isReady ? 'success' : 'critical',
        iconName: isReady ? 'CheckCircle' : 'Cancel',
    };
}
