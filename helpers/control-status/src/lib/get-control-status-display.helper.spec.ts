import { describe, expect, test } from 'vitest';
import { getControlStatusDisplay } from './get-control-status-display.helper';

describe('getControlStatusDisplay', () => {
    test('should return success color scheme and CheckCircle icon for ready control', () => {
        const result = getControlStatusDisplay({
            isReady: true,
            archivedAt: '',
        });

        expect(result).toStrictEqual({
            colorScheme: 'success',
            iconName: 'CheckCircle',
        });
    });

    test('should return critical color scheme and Cancel icon for not ready control', () => {
        const result = getControlStatusDisplay({
            isReady: false,
            archivedAt: '',
        });

        expect(result).toStrictEqual({
            colorScheme: 'critical',
            iconName: 'Cancel',
        });
    });

    test('should return neutral color scheme and OutOfScope icon for archived control regardless of ready status', () => {
        const result = getControlStatusDisplay({
            isReady: true,
            archivedAt: '2023-01-01T00:00:00Z',
        });

        expect(result).toStrictEqual({
            colorScheme: 'neutral',
            iconName: 'OutOfScope',
        });
    });

    test('should return neutral color scheme and OutOfScope icon for archived not ready control', () => {
        const result = getControlStatusDisplay({
            isReady: false,
            archivedAt: '2023-01-01T00:00:00Z',
        });

        expect(result).toStrictEqual({
            colorScheme: 'neutral',
            iconName: 'OutOfScope',
        });
    });

    test('should handle empty string archivedAt as not archived', () => {
        const result = getControlStatusDisplay({
            isReady: true,
            archivedAt: '',
        });

        expect(result).toStrictEqual({
            colorScheme: 'success',
            iconName: 'CheckCircle',
        });
    });
});
