# @helpers/control-status

A helper utility for determining the appropriate visual display properties (color scheme and icon) for controls based on their status.

## Features

- **Consistent Status Display**: Provides standardized color schemes and icons for control status across the application
- **Archive-Aware**: Handles archived controls with appropriate neutral styling
- **Type-Safe**: Full TypeScript support with proper type definitions
- **Well-Tested**: Comprehensive test coverage for all status combinations

## Usage

```tsx
import { getControlStatusDisplay } from '@helpers/control-status';

// For a ready, active control
const { colorScheme, iconName } = getControlStatusDisplay({
  isReady: true,
  archivedAt: ''
});
// Returns: { colorScheme: 'success', iconName: 'CheckCircle' }

// For a not ready control
const { colorScheme, iconName } = getControlStatusDisplay({
  isReady: false,
  archivedAt: ''
});
// Returns: { colorScheme: 'critical', iconName: 'Cancel' }

// For an archived control (regardless of ready status)
const { colorScheme, iconName } = getControlStatusDisplay({
  isReady: true, // doesn't matter for archived controls
  archivedAt: '2023-01-01T00:00:00Z'
});
// Returns: { colorScheme: 'neutral', iconName: 'OutOfScope' }
```

## Example Implementations

### In Data Table Cells
```tsx
import { Metadata } from '@cosmos/components/metadata';
import { getControlStatusDisplay } from '@helpers/control-status';

export const ControlCodeCell = ({ row }) => {
  const { isReady, code, archivedAt } = row.original;
  const { colorScheme, iconName } = getControlStatusDisplay({
    isReady,
    archivedAt,
  });

  return (
    <Metadata
      iconName={iconName}
      label={code}
      colorScheme={colorScheme}
    />
  );
};
```

### In Page Headers
```tsx
import { Metadata } from '@cosmos/components/metadata';
import { getControlStatusDisplay } from '@helpers/control-status';

export const ControlPageHeaderSlot = ({ controlDetails }) => {
  const { colorScheme, iconName } = getControlStatusDisplay({
    isReady: controlDetails.isReady,
    archivedAt: controlDetails.archivedAt,
  });

  return (
    <Metadata
      colorScheme={colorScheme}
      iconName={iconName}
      label={controlDetails.code}
      type="tag"
    />
  );
};
```

### In Control Panels/Cards
```tsx
import { Metadata } from '@cosmos/components/metadata';
import { getControlStatusDisplay } from '@helpers/control-status';

export const ControlStatusBadge = ({ control }) => {
  const { colorScheme, iconName } = getControlStatusDisplay({
    isReady: control.isReady,
    archivedAt: control.archivedAt,
  });

  return (
    <Metadata
      iconName={iconName}
      label={control.code}
      colorScheme={colorScheme}
    />
  );
};
```

## Status Logic

| Control State | Color Scheme | Icon | Description |
|---------------|--------------|------|-------------|
| Ready & Active | `success` | `CheckCircle` | Control is ready and not archived |
| Not Ready & Active | `critical` | `Cancel` | Control is not ready and not archived |
| Archived | `neutral` | `OutOfScope` | Control is archived (ready status ignored) |

## API Reference

### `getControlStatusDisplay(options)`

#### Parameters

- `options.isReady` (boolean): Whether the control is ready
- `options.archivedAt` (string): The archived date, or empty string if not archived

#### Returns

- `colorScheme` (ColorScheme): The appropriate color scheme for the control status
- `iconName` (IconName): The appropriate icon name for the control status

## Dependencies

- `lodash-es`: For `isNil` utility function
- `@cosmos/components`: For TypeScript type definitions
