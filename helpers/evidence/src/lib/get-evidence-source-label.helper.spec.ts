import { describe, expect, test, vi } from 'vitest';
import { getEvidenceSourceLabel } from './get-evidence-source-label.helper';

vi.mock('@globals/i18n/macro', () => ({
    t: (str: string) => str,
}));

describe('getEvidenceSourceLabel', () => {
    test('should return correct label for BOX type', () => {
        expect(getEvidenceSourceLabel('BOX', null)).toBe('File - Box');
    });

    test('should return correct label for DROPBOX type', () => {
        expect(getEvidenceSourceLabel('DROPBOX', null)).toBe('File - Dropbox');
    });

    test('should return correct label for GOOGLE_DRIVE type', () => {
        expect(getEvidenceSourceLabel('GOOGLE_DRIVE', null)).toBe(
            'File - GDrive',
        );
    });

    test('should return correct label for ONE_DRIVE type', () => {
        expect(getEvidenceSourceLabel('ONE_DRIVE', null)).toBe(
            'File - OneDrive',
        );
    });

    test('should return correct label for S3_FILE type', () => {
        expect(getEvidenceSourceLabel('S3_FILE', null)).toBe('File - Local');
    });

    test('should return correct label for SHARE_POINT type', () => {
        expect(getEvidenceSourceLabel('SHARE_POINT', null)).toBe(
            'File - SharePoint',
        );
    });

    test('should return correct label for TEST_RESULT type', () => {
        expect(getEvidenceSourceLabel('TEST_RESULT', null)).toBe('Test');
    });

    test('should return correct label for URL type', () => {
        expect(getEvidenceSourceLabel('URL', null)).toBe('URL');
    });

    test('should return correct label for TICKET_PROVIDER type without clientType', () => {
        expect(getEvidenceSourceLabel('TICKET_PROVIDER', null)).toBe('Ticket');
    });

    test('should return correct label for TICKET_PROVIDER type with clientType', () => {
        expect(getEvidenceSourceLabel('TICKET_PROVIDER', 'JIRA')).toBe(
            'Ticket - JIRA',
        );
    });

    test('should return dash for undefined type', () => {
        expect(getEvidenceSourceLabel(undefined, null)).toBe('—');
    });
});
