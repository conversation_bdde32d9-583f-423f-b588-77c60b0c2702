import { describe, expect, test } from 'vitest';
import { t } from '@globals/i18n/macro';
import { getEvidenceStatusMetadata } from './get-evidence-status-metadata.helper';
import type { EvidenceStatusType } from './types/evidence-status.type';

describe('evidence Status Metadata Helper', () => {
    describe('getEvidenceStatusMetadata', () => {
        test('should return success metadata for READY status', () => {
            const status: EvidenceStatusType = 'READY';

            const result = getEvidenceStatusMetadata(status);

            expect(result).toStrictEqual({
                iconName: 'CheckCircle',
                colorScheme: 'success',
                label: t`Ready`,
            });
        });

        test('should return success metadata for TEST_PASSED status', () => {
            const status: EvidenceStatusType = 'TEST_PASSED';

            const result = getEvidenceStatusMetadata(status);

            expect(result).toStrictEqual({
                iconName: 'CheckCircle',
                colorScheme: 'success',
                label: t`Ready`,
            });
        });

        test('should return warning metadata for EXPIRING_SOON status', () => {
            const status: EvidenceStatusType = 'EXPIRING_SOON';

            const result = getEvidenceStatusMetadata(status);

            expect(result).toStrictEqual({
                iconName: 'WarningTriangle',
                colorScheme: 'warning',
                label: t`Upcoming renewal`,
            });
        });

        test('should return critical metadata for EXPIRED status', () => {
            const status: EvidenceStatusType = 'EXPIRED';

            const result = getEvidenceStatusMetadata(status);

            expect(result).toStrictEqual({
                iconName: 'WarningDiamond',
                colorScheme: 'critical',
                label: t`Needs renewal`,
            });
        });

        test('should return warning metadata for NEEDS_SOURCE status', () => {
            const status: EvidenceStatusType = 'NEEDS_SOURCE';

            const result = getEvidenceStatusMetadata(status);

            expect(result).toStrictEqual({
                iconName: 'WarningDiamond',
                colorScheme: 'critical',
                label: t`Needs artifact`,
            });
        });

        test('should return critical metadata for TEST_FAILED status', () => {
            const status: EvidenceStatusType = 'TEST_FAILED';

            const result = getEvidenceStatusMetadata(status);

            expect(result).toStrictEqual({
                iconName: 'WarningTriangle',
                colorScheme: 'critical',
                label: t`Failed`,
            });
        });

        test('should return neutral metadata for TEST_UNUSED status', () => {
            const status: EvidenceStatusType = 'TEST_UNUSED';

            const result = getEvidenceStatusMetadata(status);

            expect(result).toStrictEqual({
                colorScheme: 'neutral',
                label: t`Test unused`,
            });
        });

        test('should return neutral metadata for TEST_DISABLED status', () => {
            const status: EvidenceStatusType = 'TEST_DISABLED';

            const result = getEvidenceStatusMetadata(status);

            expect(result).toStrictEqual({
                colorScheme: 'neutral',
                label: t`Test disabled`,
            });
        });

        test('should return warning metadata for TEST_ERROR status', () => {
            const status: EvidenceStatusType = 'TEST_ERROR';

            const result = getEvidenceStatusMetadata(status);

            expect(result).toStrictEqual({
                iconName: 'WarningTriangle',
                colorScheme: 'warning',
                label: t`Test error`,
            });
        });

        test('should return neutral metadata for TEST_DELETED status', () => {
            const status: EvidenceStatusType = 'TEST_DELETED';

            const result = getEvidenceStatusMetadata(status);

            expect(result).toStrictEqual({
                iconName: 'Trash',
                colorScheme: 'neutral',
                label: t`Evidence status`,
            });
        });

        test('should return default metadata for unknown status', () => {
            const status = 'UNKNOWN_STATUS' as EvidenceStatusType;

            const result = getEvidenceStatusMetadata(status);

            expect(result).toStrictEqual({
                label: '-',
                colorScheme: 'neutral',
            });
        });
    });
});
