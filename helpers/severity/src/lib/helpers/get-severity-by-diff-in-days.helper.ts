/**
 * Determines the severity level based on the difference in days.
 *
 * @param diffInDays - The difference in days between today and the due date.
 * @returns The severity level: 'critical', 'warning', or 'neutral'.
 */
export const getSeverityByDiffInDays = (
    diffInDays: number,
): 'critical' | 'neutral' | 'warning' => {
    if (diffInDays < 0) {
        return 'critical';
    }

    if (diffInDays >= 0 && diffInDays <= 6) {
        return 'warning';
    }

    return 'neutral';
};
