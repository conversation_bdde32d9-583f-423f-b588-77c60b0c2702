import { describe, expect, test } from 'vitest';
import { getSeverityByDiffInDays } from './get-severity-by-diff-in-days.helper';

describe('getSeverityByDiffInDays', () => {
    test('should return "critical" when diffInDays is negative', () => {
        // Test with various negative values
        expect(getSeverityByDiffInDays(-1)).toBe('critical');
        expect(getSeverityByDiffInDays(-5)).toBe('critical');
        expect(getSeverityByDiffInDays(-100)).toBe('critical');
    });

    test('should return "warning" when diffInDays is between 0 and 6 inclusive', () => {
        // Test the boundary values
        expect(getSeverityByDiffInDays(0)).toBe('warning');
        expect(getSeverityByDiffInDays(6)).toBe('warning');

        // Test a value in the middle of the range
        expect(getSeverityByDiffInDays(3)).toBe('warning');
    });

    test('should return "neutral" when diffInDays is greater than 6', () => {
        // Test the boundary value
        expect(getSeverityByDiffInDays(7)).toBe('neutral');

        // Test various values above the boundary
        expect(getSeverityByDiffInDays(10)).toBe('neutral');
        expect(getSeverityByDiffInDays(30)).toBe('neutral');
        expect(getSeverityByDiffInDays(100)).toBe('neutral');
    });

    test('should handle edge cases correctly', () => {
        // Test with zero
        expect(getSeverityByDiffInDays(0)).toBe('warning');

        // Test with very large positive number
        expect(getSeverityByDiffInDays(Number.MAX_SAFE_INTEGER)).toBe(
            'neutral',
        );

        // Test with very large negative number
        expect(getSeverityByDiffInDays(Number.MIN_SAFE_INTEGER)).toBe(
            'critical',
        );
    });

    test('should handle decimal values correctly', () => {
        // Decimal values should be treated the same as integers
        expect(getSeverityByDiffInDays(-0.5)).toBe('critical');
        expect(getSeverityByDiffInDays(0.5)).toBe('warning');
        expect(getSeverityByDiffInDays(6.5)).toBe('neutral');
    });
});
