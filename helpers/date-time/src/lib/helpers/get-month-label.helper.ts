import { isInteger } from 'lodash-es';
import { sharedDirectLocaleController } from '@globals/i18n';
import { t } from '@globals/i18n/macro';

/**
 * Gets the localized month name for a given month number.
 *
 * @param month - Month number (1-12).
 * @param format - Month format ('long' for full name, 'short' for abbreviated).
 * @returns Localized month name.
 */
export const getMonthLabel = (
    month: number,
    format: 'long' | 'short' = 'long',
): string => {
    if (month < 1 || month > 12 || !isInteger(month)) {
        return t`Invalid`;
    }

    const locale = sharedDirectLocaleController.getLocale();

    // Create a date object for the specified month (using day 1)
    const date = new Date(2000, month - 1, 1);

    // Use Intl.DateTimeFormat to get the localized month name
    return new Intl.DateTimeFormat(locale, { month: format }).format(date);
};
