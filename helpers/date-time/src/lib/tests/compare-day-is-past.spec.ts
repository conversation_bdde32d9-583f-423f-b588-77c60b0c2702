import { beforeEach, describe, expect, test, vi } from 'vitest';
import {
    compareDayIsFuture,
    compareDayIsPast,
} from '../helpers/compare-day-is-past.helper';

describe('compareDayIsFuture', () => {
    const baseDate = new Date('2025-03-09T14:23:00Z');

    beforeEach(() => {
        vi.setSystemTime(baseDate);
    });

    describe('with default reference date (today)', () => {
        test('should return true for future day', () => {
            const tomorrow = new Date();

            tomorrow.setDate(tomorrow.getDate() + 1);

            expect(compareDayIsFuture(tomorrow)).toBeTruthy();
        });

        test('should return false for same day at different times', () => {
            const sameDayMorning = new Date('2025-03-09T06:00:00Z');
            const sameDayEvening = new Date('2025-03-09T22:00:00Z');

            expect(compareDayIsFuture(sameDayMorning)).toBeFalsy();
            expect(compareDayIsFuture(sameDayEvening)).toBeFalsy();
        });

        test('should return false for past day', () => {
            const yesterday = new Date('2025-03-08T23:59:59Z');

            expect(compareDayIsFuture(yesterday)).toBeFalsy();
        });
    });
});

describe('compareDayIsPast', () => {
    const baseDate = new Date('2025-03-09T14:23:00Z');

    beforeEach(() => {
        vi.setSystemTime(baseDate);
    });

    describe('with default reference date (today)', () => {
        test('should return false for future day', () => {
            const tomorrow = new Date('2025-03-10T00:00:00Z');

            expect(compareDayIsPast(tomorrow)).toBeFalsy();
        });

        test('should return false for same day at different times', () => {
            const sameDayMorning = new Date('2025-03-10T06:00:00Z');
            const sameDayEvening = new Date('2025-03-10T22:00:00Z');

            expect(compareDayIsPast(sameDayMorning, baseDate)).toBeFalsy();
            expect(compareDayIsPast(sameDayEvening, baseDate)).toBeFalsy();
        });

        test('should return true for past day', () => {
            const yesterday = new Date('2025-03-08T23:59:59Z');

            expect(compareDayIsPast(yesterday)).toBeTruthy();
        });
    });

    describe('with custom reference date', () => {
        const referenceDate = new Date('2025-03-09T00:00:00Z');

        test('should return false for day after reference', () => {
            const nextDay = new Date('2025-03-10T00:00:00Z');

            expect(compareDayIsPast(nextDay, referenceDate)).toBeFalsy();
        });

        test('should return false for same day as reference', () => {
            const sameDay = new Date('2025-03-09T23:59:59Z');

            expect(compareDayIsPast(sameDay, referenceDate)).toBeFalsy();
        });

        test('should return true for day before reference', () => {
            // Use dates that are clearly different days
            const previousDay = new Date('2025-03-07T12:00:00Z');
            const refDate = new Date('2025-03-09T12:00:00Z');

            // Now test the function
            expect(compareDayIsPast(previousDay, refDate)).toBeTruthy();
        });
    });

    describe('edge cases', () => {
        test('should handle null input', () => {
            expect(() => compareDayIsPast(null as unknown as Date)).toThrow(
                TypeError,
            );
        });

        test('should handle undefined input', () => {
            expect(() =>
                compareDayIsPast(undefined as unknown as Date),
            ).toThrow(TypeError);
        });

        test('should handle timezone transitions', () => {
            // Date during daylight saving time transition
            const dstDate = new Date('2025-03-30T02:00:00Z');

            expect(compareDayIsPast(dstDate, baseDate)).toBeFalsy();
        });

        test('should handle month transitions', () => {
            const lastDayOfMonth = new Date('2025-03-31T23:59:59Z');
            const firstDayOfNextMonth = new Date('2025-04-01T00:00:00Z');

            expect(compareDayIsPast(lastDayOfMonth, baseDate)).toBeFalsy();
            expect(compareDayIsPast(firstDayOfNextMonth, baseDate)).toBeFalsy();
        });

        test('should handle year transitions', () => {
            const lastDayOfYear = new Date('2025-12-31T23:59:59Z');
            const firstDayOfNextYear = new Date('2026-01-01T00:00:00Z');

            expect(compareDayIsPast(lastDayOfYear, baseDate)).toBeFalsy();
            expect(compareDayIsPast(firstDayOfNextYear, baseDate)).toBeFalsy();
        });

        test('should handle leap years', () => {
            const leapDay2024 = new Date('2024-02-29T12:00:00Z');
            const referenceDate = new Date('2024-02-28T00:00:00Z');

            expect(compareDayIsPast(leapDay2024, referenceDate)).toBeFalsy();
        });
    });

    describe('with inclusive parameter', () => {
        test('should include current day when inclusive=true', () => {
            const sameDay = new Date('2025-03-09T14:23:00Z');

            expect(compareDayIsPast(sameDay, baseDate, true)).toBeTruthy();
            expect(compareDayIsPast(sameDay, baseDate, false)).toBeFalsy();
        });

        test('should exclude current day when inclusive=false (default)', () => {
            const sameDay = new Date();

            expect(compareDayIsPast(sameDay)).toBeFalsy();
            expect(compareDayIsPast(sameDay, undefined, false)).toBeFalsy();
        });
    });
});
