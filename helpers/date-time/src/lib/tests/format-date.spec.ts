import { afterEach, beforeEach, describe, expect, test, vi } from 'vitest';
import { Language } from '@drata/enums';
import { sharedDirectLocaleController } from '@globals/i18n';
import { formatDate } from '../date-time-helper';

describe('formatDate', () => {
    const mockDate = new Date('2025-03-09T14:23:00Z');
    const mockEndDate = new Date('2025-03-15T16:45:00Z');

    beforeEach(() => {
        process.env.TZ = 'UTC';
        vi.useFakeTimers();
        vi.setSystemTime(mockDate);
        vi.setConfig({
            fakeTimers: {
                now: mockDate.valueOf(),
                toFake: ['Date'],
                timers: true,
                loopLimit: false,
                shouldAdvanceTime: false,
                advanceTimeDelta: 0,
                shouldClearNativeTimers: true,
                timezone: 'UTC',
            },
        });
    });

    afterEach(() => {
        vi.useRealTimers();
        process.env.TZ = 'UTC';
    });

    describe('single date formats', () => {
        test('should format date in sentence format', () => {
            expect(formatDate('sentence')).toBe('March 9, 2025');
        });

        test('should format date in sentence_time format', () => {
            expect(formatDate('sentence_time')).toBe('March 9, 2025 at 2:23pm');
        });

        test('should format date in field format', () => {
            expect(formatDate('field')).toBe('Mar 9, 2025');
        });

        test('should format date in field_time format', () => {
            expect(formatDate('field_time')).toBe('Mar 9, 2025 at 2:23pm');
        });

        test('should format date in table format', () => {
            expect(formatDate('table')).toBe('Mar 09, 2025');
        });

        test('should format date in table_time format', () => {
            expect(formatDate('table_time')).toBe('Mar 09, 2025 at 2:23pm');
        });

        test('should format time only', () => {
            expect(formatDate('time')).toBe('2:23pm');
        });

        test('should format overdue plural days', () => {
            const twoDaysAgo = new Date('2025-03-07T14:23:00');

            expect(formatDate('overdue', twoDaysAgo)).toBe('2 days ago');
        });

        test('should format overdue singular day', () => {
            const twoDaysAgo = new Date('2025-03-08T14:23:00');

            expect(formatDate('overdue', twoDaysAgo)).toBe('a day ago');
        });

        test('should format overdue plural months for periods longer than 90 days', () => {
            const fourMonthsAgo = new Date('2024-11-09T14:23:00');

            expect(formatDate('overdue', fourMonthsAgo)).toBe('4 months ago');
        });

        test('should format overdue singular month', () => {
            const oneMonthAgo = new Date('2025-02-09T14:23:00');

            // This is exactly 28 days, which is less than 30 days, so it should still show days
            expect(formatDate('overdue', oneMonthAgo)).toBe('28 days ago');

            // But if we go back 95 days (more than 90 days), it should show months
            const threeMonthsAgo = new Date('2024-12-05T14:23:00');

            expect(formatDate('overdue', threeMonthsAgo)).toBe('3 months ago');
        });

        test('should format date_tooltip', () => {
            const result = formatDate('date_tooltip');

            expect(result).toMatch('Mar 09, 2025 at 2:23:00pm UTC');
        });

        test('should format timestamp', () => {
            expect(formatDate('timestamp')).toBe(mockDate.toISOString());
        });
    });

    describe('range formats', () => {
        test('should format sentence_range', () => {
            expect(formatDate('sentence_range', mockDate, mockEndDate)).toBe(
                'March 9, 2025 to March 15, 2025',
            );
        });

        test('should format field_range', () => {
            expect(formatDate('field_range', mockDate, mockEndDate)).toBe(
                'Mar 9, 2025 to Mar 15, 2025',
            );
        });

        test('should format table_range', () => {
            expect(formatDate('table_range', mockDate, mockEndDate)).toBe(
                'Mar 09, 2025 to Mar 15, 2025',
            );
        });

        test('should throw error when endDate is missing for range format', () => {
            expect(() => formatDate('sentence_range', mockDate)).toThrow(
                'End date required for range format',
            );
        });
    });

    describe('input handling', () => {
        test('should accept string date input', () => {
            expect(formatDate('sentence', '2025-03-09')).toBe('March 9, 2025');
        });

        test('should accept Date object input', () => {
            expect(formatDate('sentence', new Date('2025-03-09'))).toBe(
                'March 9, 2025',
            );
        });

        test('should use current date when no date provided', () => {
            expect(formatDate('sentence')).toBe('March 9, 2025');
        });

        test('should throw error for invalid format', () => {
            // @ts-expect-error Testing invalid input
            expect(() => formatDate('invalid')).toThrow(
                'Invalid format: invalid',
            );
        });
    });

    describe('edge cases', () => {
        test('should handle midnight correctly', () => {
            vi.setSystemTime(new Date('2025-03-09T00:00:00Z'));
            expect(formatDate('sentence_time')).toBe(
                'March 9, 2025 at 12:00am',
            );
        });

        test('should handle noon correctly', () => {
            vi.setSystemTime(new Date('2025-03-09T12:00:00Z'));
            expect(formatDate('sentence_time')).toBe(
                'March 9, 2025 at 12:00pm',
            );
        });

        test('should handle single digit hours', () => {
            vi.setSystemTime(new Date('2025-03-09T09:00:00Z'));
            expect(formatDate('sentence_time')).toBe('March 9, 2025 at 9:00am');
        });

        test('should handle single digit minutes', () => {
            vi.setSystemTime(new Date('2025-03-09T14:05:00Z'));
            expect(formatDate('sentence_time')).toBe('March 9, 2025 at 2:05pm');
        });
    });

    describe('time zone handling', () => {
        test('should handle PST timezone using sentence format both with and without dateProvided', () => {
            process.env.TZ = 'America/Los_Angeles';

            const sentenceFormatNoDateProvided = formatDate('sentence');

            expect(sentenceFormatNoDateProvided).toBe('March 9, 2025');

            // With dateProvided
            const sentenceFormatDateProvided = formatDate(
                'sentence',
                '2025-03-30',
            );

            expect(sentenceFormatDateProvided).toBe('March 30, 2025');
        });

        test('should handle PST timezone using sentence_range', () => {
            process.env.TZ = 'America/Los_Angeles';

            const sentenceRangeFormat = formatDate(
                'sentence_range',
                '2025-01-02',
                '2025-03-30',
            );

            expect(sentenceRangeFormat).toBe(
                'January 2, 2025 to March 30, 2025',
            );
        });
    });

    describe('locale handling', () => {
        test('should format date in English locale', () => {
            sharedDirectLocaleController.setLocale(Language.ENGLISH_US);

            expect(formatDate('sentence', mockDate)).toBe('March 9, 2025');
        });

        test('should format date in Spanish locale', () => {
            sharedDirectLocaleController.setLocale(Language.SPANISH_LA);

            expect(formatDate('sentence', mockDate)).toBe('9 de marzo de 2025');
        });

        test('should format date in French locale', () => {
            sharedDirectLocaleController.setLocale(Language.FRENCH_FR);

            expect(formatDate('sentence', mockDate)).toBe('9 mars 2025');
        });

        test('should format date in German locale', () => {
            sharedDirectLocaleController.setLocale(Language.GERMAN_DE);

            expect(formatDate('sentence', mockDate)).toBe('9. März 2025');
        });
    });
});
